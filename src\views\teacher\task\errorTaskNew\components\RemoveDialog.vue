<script setup lang="ts">
const emit = defineEmits(['confirm'])
let dialogVisible = defineModel<boolean>('visible')
function confirm() {
  emit('confirm')
  dialogVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="395"
    class="!rounded-[6px]"
    :lock-scroll="false"
  >
    <template #header>
      <div class="flex items-center">
        <svg-menu-error-tips class="w-17px h-17px" />
        <div class="ml-9px text-[17px] text-[#333] font-600">
          确定删除该班级吗？
        </div>
      </div>
    </template>
    <div class="pl-26px text-[15px] text-[#333]">
      删除班级后，该班级学生将收不到错题任务
    </div>
    <template #footer>
      <div class="flex items-center justify-end">
        <div
          class="cursor-pointer van-haptics-feedback w-68px h-29px bg-[#fbfbfb] leading-[29px] text-[#666666] rounded-[4px] text-[13px] border-[1px] border-solid border-[#CCCCCC] text-center"
          @click="dialogVisible = false"
        >
          取消
        </div>
        <div
          class="cursor-pointer van-haptics-feedback w-68px h-29px bg-[#6474FD] leading-[29px] text-white rounded-[4px] text-[13px] text-center ml-13px"
          @click="confirm"
        >
          确定
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
