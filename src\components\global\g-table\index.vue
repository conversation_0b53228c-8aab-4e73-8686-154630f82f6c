<script lang="ts">
export default {
  inheritAttrs: false,
}
</script>

<script setup lang="ts" name="g-table">
// !需要手动开启tableOptions的 loading:boolean
const props = defineProps({
  tableOptions: {
    type: Object,
    default: () => {
      return {
        // !ref属于el-table ref,另额外添加下面方法：
        // !exportExcel-导出excel方法
        ref: null,
        // 唯一id,多选的话必填
        key: '',
        loading: false,
        // 多选获取数据接口
        export: '',
        // 多选选中的数据
        chooseTableIds: [],
        pageOptions: {},
        column: [],
        data: [],
      }
    },
  },
  showHeader: {
    type: Boolean,
    default: false,
  },
  showSelectAll: {
    type: Boolean,
    default: false,
  },
  showExport: {
    type: Boolean,
    default: false,
  },
  showAllButton: {
    type: Boolean,
    default: false,
  },
})
// exportExcel点击导出事件，需要手动开启loading
const emit = defineEmits(['sortChange',
'changePage',
'exportExcel',
'showAll'])
interface Props {
  /** 显示全选按钮 */
  showSelectAll: boolean
  /** 显示导出按钮 */
  showExport: boolean
  /* g-table配置 */
  tableOptions: object
}

const tableRef: any = $ref(null)
let isSelectAll = $ref(false)
onMounted(() => {
  tableRef.tableBackfill = tableBackfill
  props.tableOptions.ref = tableRef
})

watch(
  () => props.tableOptions.data,
  async (newVal) => {
    if (newVal.length != 0) {
      await nextTick()
      tableBackfill()
    }
  },
)

/* 排序发生变化触发事件 */
function sortChange({
  column,
  order,
  prop,
}) {
  const orderMap = {
    descending: 'desc',
    ascending: 'asc',
  }
  emit('sortChange', {
    column,
    order: orderMap[order],
    prop,
  })
}

function formatEmpty(row, column, cellValue) {
  let value = cellValue ?? '-'
  value = cellValue === '' ? '-' : value
  return value
}

function changePage() {
  emit('changePage')
}

/* 如果有回填需求，外部tableOptions.chooseTableIds = [{[tableOptions.key]:id}]或者[1,2,3] */
function tableBackfill() {
  props.tableOptions.data.forEach((e1, index) => {
    let flag = $g._.findIndex(props.tableOptions.chooseTableIds, (e2: any) => {
      return $g.tool.typeOf(e2) == 'object'
        ? e2[props.tableOptions.key] == e1[props.tableOptions.key]
        : e2 == e1[props.tableOptions.key]
    })
    if (flag >= 0 && props.tableOptions.key) {
      props.tableOptions.chooseTableIds[flag] = e1
      tableRef.toggleRowSelection(e1, true)
    }
  })
}

function handleSelectionChange(val) {
  // 先注释 有逻辑冲突
  // console.log("🎃 val ==> ", val)
  // console.log(
  //   "🎃 props.tableOptions.chooseTableIds==> ",
  //   props.tableOptions.chooseTableIds,
  // )
  // props.tableOptions.chooseTableIds = val
}

function selectAll2() {
  if (isSelectAll) {
    props.tableOptions.chooseTableIds =
      props.tableOptions.chooseTableIds.filter((e) => {
        return !props.tableOptions.data
          .map(e => e[props.tableOptions.key])
          .includes(e[props.tableOptions.key])
      })
  }
  else {
    props.tableOptions.chooseTableIds = $g._.uniqBy(
      [...props.tableOptions.chooseTableIds, ...props.tableOptions.data],
      props.tableOptions.key,
    )
    props.tableOptions.data.forEach((e) => {
      tableRef.toggleRowSelection(e, true)
    })
  }
  isSelectAll = !isSelectAll
}

function selectAll(val) {
  let flag = false
  if ($g.tool.isTrue(val)) {
    flag = props.tableOptions.data.some((e) => {
      return !props.tableOptions.chooseTableIds.some((e2) => {
        return e[props.tableOptions.key] == e2[props.tableOptions.key]
      })
    })
  }
  isSelectAll = !flag
  selectAll2()
}

function checkboxChange(rows, row) {
  try {
    let selected = rows.length && rows.includes(row)
    if (selected) {
      props.tableOptions.chooseTableIds.push(row)
    }
    else {
      props.tableOptions.chooseTableIds =
        props.tableOptions.chooseTableIds.filter(
          item =>
            item[props.tableOptions.key] !== row[props.tableOptions.key],
        )
    }
  }
  catch (error) {
    console.error(new Error('检查tableOptions.chooseTableIds是否存在且为数组'))
  }
}

async function onError({ reason }) {
  props.tableOptions.loading = false
}

window.addEventListener('unhandledrejection', onError, true)

onBeforeUnmount(() => {
  window.removeEventListener('unhandledrejection', onError)
})
</script>

<template>
  <div class="g-table relative">
    <!-- 导出工具栏区域||按钮区域 -->
    <div class="flex justify-between">
      <van-space>
        <slot name="header-left">
          <div />
        </slot>
      </van-space>
      <van-space>
        <slot name="header-right" />
      </van-space>
    </div>

    <el-table
      id="gTable"
      ref="tableRef"
      v-loading="tableOptions.loading"
      :class="[
        tableOptions?.loading && !tableOptions?.data?.length
          ? '!h-[650px]'
          : '',
      ]"
      :data="tableOptions.data"
      :border="true"
      style="width: 100%"
      class="mt-10px"
      :row-key="tableOptions.key"
      element-loading-text="正在加载中..."
      element-loading-background="hsla(0,0%,100%,0.85)"
      highlight-current-row
      v-bind="$attrs"
      @sort-change="sortChange"
      @select="checkboxChange"
      @select-all="selectAll"
      @selection-change="handleSelectionChange"
    >
      <template v-for="(item, key) in tableOptions.column" :key="key">
        <!-- index-序号  selection-多选 -->
        <el-table-column
          :type="item.type"
          :prop="item.prop"
          :label="item.label"
          :index="item.index || 1"
          :show-overflow-tooltip="
            item.tooltip
              ?? (item.type == 'selection' || item.prop == 'cz' ? false : true)
          "
          :selectable="item.selectable"
          :sortable="item.sort ? 'custom' : false"
          :formatter="item.formatter || formatEmpty"
          :filters="item.filters"
          :filter-multiple="item.filterMultiple"
          :width="
            ['index', 'selection'].includes(item.type) ? '64px' : item.width
          "
          :min-width="item.minWidth"
          :align="item.align || tableOptions.align || 'center'"
          :reserve-selection="Boolean(tableOptions.key)"
          :fixed="item.fixed"
          :header-align="item.headerAlign"
          :class-name="item.className"
        >
          <template v-if="item.headerSlot" #header="{ column, $index }">
            <slot
              :index="$index"
              :column="column"
              :item="item"
              :name="`header-${item.prop}`"
            />
          </template>
          <template #default="{ row, $index, column }">
            <component
              :is="item.render"
              v-if="item.render"
              :row="row"
              :index="$index"
              :column="column"
            />
            <slot
              v-else-if="item.slot"
              :row="row"
              :index="$index"
              :column="column"
              :name="item.prop"
              :item="item"
            >
              <div v-html="row[item.prop]" />
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <g-empty
          v-show="!tableOptions.loading"
          :description="tableOptions.description"
        />
      </template>
    </el-table>
    <g-page
      v-if="$g.tool.isTrue(tableOptions.pageOptions)"
      :page-options="tableOptions.pageOptions"
      v-bind="$attrs"
      :show-all-button="showAllButton"
      @change="changePage"
      @show-all="emit('showAll')"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  thead {
    height: 48px;
    th {
      background-color: #f8f8f9 !important;
      color: #606266;
    }
  }
  tbody {
    .el-table__cell {
      height: 65px;
    }
  }
  .el-table .cell.el-tooltip {
    white-space: pre-wrap;
  }
}

.el-table {
  --el-table-border-color: #e8edf8;
}
</style>
