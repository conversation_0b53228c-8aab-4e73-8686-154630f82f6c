<script setup lang="ts" name="ReportQuestionList">
import { getPersonalReportApi, getQuestionListDetail<PERSON>pi, getTeacherRemindApi } from '@/api/taskCenter'
import QuestionPanel from '../questionReport/components/QuestionPanel.vue'
import QuestionStemItem from '../questionReport/components/QuestionStemItem.vue'

const route = useRoute()
let questionList: any = $ref([])
let schoolStudentIds: any = $ref([])
let loading = $ref(true)
const router = useRouter()
const isActivity = $computed(() => (route.query.exerciseSourceType as any) == 1)
const iconList = $ref([null,
$g.tool.getFileUrl('question/wrong.png'),
$g.tool.getFileUrl('question/half.png'),
$g.tool.getFileUrl('question/right.png'),
$g.tool.getFileUrl('question/not.png')])
async function init() {
  loading = true
  try {
    let res = await getPersonalReportApi({
      exerciseTaskId: route.query.exerciseTaskId,
      startTime: route.query.startTime,
      endTime: route.query.endTime,
    })
    let res1 = await getQuestionListDetailApi({
      questionIdList: res.questionResultList?.map(v => v.questionId),
    })
    res.questionResultList.forEach((item) => {
      let find = res1?.find(v => v.questionId == item.questionId) || {}
      Object.assign(item, find)
    })
    questionList = res.questionResultList
    schoolStudentIds = [res.schoolStudentId]
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    console.log(e)
  }
  finally {
    loading = false
  }
}
onBeforeMount(() => {
  init()
})

async function remind() {
  await getTeacherRemindApi({
    taskId: route.query.exerciseSourceId,
    type: 2,
    schoolStudentIds,
  })
  $g.showToast('提醒成功')
}

function goDetail(item, index) {
  router.push({
    name: 'PersonalReportDetail',
    query: {
      ...route.query,
      exerciseId: item.exerciseId,
      quesIndex: index,
      questionId: item?.questionId ?? '',
    },
  })
}
function onQuesSelect([item, index]) {
  const dom = document.getElementById(`ques-item-${index}`)
  dom?.scrollIntoView?.()
}
</script>

<template>
  <div class="p-26px flex flex-col h-[100vh]" style="width: 100vw">
    <g-navbar :title="`个人答题结果` + `-${route.query.studentName}`">
      <template #right>
        <div class="flex items-center">
          <div class="flex-cc h-23px lh-[23px]   py-2px bg-[#DAE0FD] br-[5px] px-6px">
            <img :src="$g.tool.getFileUrl('taskCenter/grayInfo.png')"
                 alt=""
                 class="w-15px h-15px"
            >
            <span class="ml-7px">{{ [3, 4].includes(Number(route.query.state) as any) ? '学生自查中！' : '已自查' }}</span>
          </div>
          <el-button
            v-if="[3, 4].includes(Number(route.query.state) as any) && !isActivity"
            type="primary"
            class="w-90px h-34px ml-13px"
            @click="remind"
          >
            一键提醒
          </el-button>
        </div>
      </template>
    </g-navbar>
    <g-loading v-if="loading" class="h-200px"></g-loading>

    <div v-else-if="questionList.length" class="flex-1 overflow-auto ">
      <div class="text-[#6C6C74] text-15px mt-19px mb-17px ml-5px">
        共<span class="text-[#6474FD] ml-4px">{{ questionList.length }}</span>
        道题（已按题目布置顺序排序）
      </div>
      <QuestionStemItem
        v-for="(item, index) in questionList"
        :id="`ques-item-${index}`"
        :key="item.questionId"
        :question-item="item"
        :index="index + 1"
      >
        <template #footer>
          <div :class="index < 9 ? 'ml-14px' : index < 99 ? 'ml-23px' : 'ml-33px'" class="mt-19px flex justify-between">
            <div class="flex items-center text-19px  font-600">
              <div class="flex items-center">
                <img
                  v-if="iconList[item.isCorrect]"
                  :src="iconList[item.isCorrect]"
                  alt=""
                  class="w-17px h-17px mr-6px"
                >
                <span v-if="[null, '错误', '部分对', '正确', '我不会'][item.isCorrect]" class="h-27px lh-[27px]">{{ [null, '错误', '部分对', '正确', '我不会'][item.isCorrect] }}</span>
              </div>
            </div>
            <el-button class="w-64px h-30px bg-white text-[#6474FD] border border-[#6474FD]" @click="goDetail(item, index)">
              查看
            </el-button>
          </div>
        </template>
      </QuestionStemItem>
    </div>
    <g-empty v-else></g-empty>
    <QuestionPanel :ques-list="questionList" @select="onQuesSelect"></QuestionPanel>
  </div>
</template>

<style scoped lang="scss"></style>
