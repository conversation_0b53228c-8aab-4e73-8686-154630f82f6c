<script setup lang="ts">
import { deduplicateByKey } from '../tool'

const route = useRoute()
const questionList = inject<Ref<any>>('questionList', ref([]))
const resources = inject<Ref<any[]>>('resources', ref([]))
const taskModel = inject<Ref<any>>('taskModel', ref(null))
const setStepVal = inject('setStepVal') as any
let updatePicker = $ref<any>('')
const isResource = $computed(() => {
  return (route.query as any).pageType == 3 // 1为学科网选题 2为校本练习选题 3为资源类型
})
// 禁用当前时间之前的日期和时间
function disabledDate(time) {
  const now = $g.dayjs().startOf('day') // 获取今天的00:00:00
  const selectedTime = $g.dayjs(time) // 被选择的时间

  // 禁用今天以前的日期
  return selectedTime.isBefore(now, 'day')
}
const myTitle = $computed(() => {
  if ((route.query.pageType as any) == 1)
    return '布置学科网选题任务'

  if ((route.query.pageType as any) == 2)
    return '布置校本练习题任务'

  return '布置资源任务'
})
const recommendedTime = $computed(() => {
  // 如果为试题类型任务
  let minutes = 0
  if ((route.query.pageType as any) != 3) {
    const [objectArr, subjectiveArr] = questionList.value
      .flatMap(item => item.subQuestions)
      .reduce(
        (res, item) => {
          res[[1,
2,
3].includes(item.subQuestionType)
            ? 0
            : 1].push(item)
          return res
        },
        [[], []],
      )
    minutes = objectArr.length * 2 + subjectiveArr.length * 5
  }
  else {
    minutes = Math.ceil(
      resources.value.reduce(
        (res, item) => res + (item.fileDuration ?? 0),
        0,
      ) / 60,
    )
  }

  let n
  if (!minutes) { // 如果全为文件类资源 默认n为4
    n = 4
  }
  else {
    n = Math.ceil(minutes / 45) // 课时数
  }
  let m = Math.round(n / 2)
  taskData.value.courseNum = n
  taskData.value.estimateTime = minutes || n * 45
  return $g
    .dayjs(taskData.value.releaseTime)
    .endOf('day')
    .add(m - 1, 'day')
    .format('YYYY-MM-DD HH:mm')
})
const releaseTimeShortcuts = [
  {
    text: '立即发布',
    value: () => $g.dayjs().format('YYYY-MM-DD HH:mm'),
  },
]
let requireCompleteShortcuts = $ref<any>([])
const releaseTimeFormat = $computed(() => {
  if ($g.dayjs(taskData.value.releaseTime).isSame($g.dayjs().format('YYYY-MM-DD HH:mm'), 'minute')) {
    taskData.value.isImmediate = true
    return '立即发布'
  }
  taskData.value.isImmediate = false
  return 'YYYY-MM-DD HH:mm'
})
const requireCompleteTimeFormat = $computed(() => {
  if (taskData.value.isUnlimited)
    return '不限时'

  if (
    $g
      .dayjs(taskData.value.requireCompleteTime)
      .isSame(recommendedTime, 'minute')
  ) 
    return 'YYYY-MM-DD HH:mm(推荐)'


  return 'YYYY-MM-DD HH:mm'
})
const taskData = inject<Ref<any>>('taskData', ref({}))
const studentData = inject<Ref<any>>('studentData', ref({}))
let ifUnfold = $ref(false)

const showName = $computed(() => {
  if (!studentData.value.classList.length && !studentData.value.specialClass)
    return ''

  let deepCloneArr = JSON.parse(JSON.stringify(studentData.value.classList))
  let classList
  let find = deepCloneArr.find(
    h => h.schoolClassId === studentData.value.specialClass?.schoolClassId,
  )
  if (find) {
    find.selectStudentArr = studentData.value.specialClass.selectStudentArr
    find.arrangeObjectType = 1
    classList = deepCloneArr
  }
  else {
    classList = [
      ...deepCloneArr,
      ...(studentData.value.specialClass
        ? [studentData.value.specialClass]
        : []),
    ]
  }
  // 合并并去重选择的学生
  classList.forEach((item) => {
    item.combineStudent = deduplicateByKey(
      [
        ...(item.selectStudentArr || []),
        ...(item.selectGroupArr
          ?.flatMap(v => v.list)
          .filter(v =>
            (
              v.studentJoinSchoolClassIdList
            )?.includes(item.schoolClassId)) || []),
      ],
      'schoolStudentId',
    )
  })
  return classList
    .map(
      item =>
        `${item.sysGradeName + item.className}（${item.combineStudent.length}人）`,
    )
    .join('、')
})

const hasVideo = $computed(() => {
  return resources.value.some(item => $g.tool.resourceType(item.fileExtension) == 'video')
})

watch(
  () => recommendedTime,
  () => {
    requireCompleteShortcuts = [
      {
        text: `推荐时间${recommendedTime}`,
        value: () => {
          taskData.value.isUnlimited = false
          return recommendedTime
        },
      },
      {
        text: '不限时',
        value: () => {
          taskData.value.isUnlimited = true
        },
      },
    ]
    updatePicker = new Date()
    if (taskData.value.notIsRecommend) return
    taskData.value.requireCompleteTime = recommendedTime
  },
  {
    immediate: true,
  },
)
watch(() => taskModel.value, (newVal) => {
  if (newVal)
    taskData.value.configCorrect = newVal == 4 ? 1 : 2
}, { immediate: true })
function initData() {
  taskData.value.notIsRecommend = false
  taskData.value.isImmediate = true
  taskData.value.releaseTime = $g.dayjs().format('YYYY-MM-DD HH:mm')
  taskData.value.isUnlimited = false // 不限时
  taskData.value.taskName =
    $g.dayjs().format('MM/DD') + route.query.subjectName + myTitle.split('布置').join('')
  taskData.value.configAnswerPublishTime = 1
  taskData.value.configCorrect = (route.query?.pattenType as any) == 4 || taskModel.value == 4 ? 1 : 2
  taskData.value.configReachCompleteTime = 1
  taskData.value.configVideoProgressBar = 2
}
onBeforeMount(() => {
  initData()
})

function goBack() {
  setStepVal(1)
}

function handleRecommend(val) {
  taskData.value.notIsRecommend = !$g.dayjs(val).isSame(recommendedTime, 'minute')
  taskData.value.isUnlimited = !taskData.value.requireCompleteTime
}
</script>

<template>
  <div class="flex flex-col h-full">
    <g-navbar :title="myTitle"
              class="mb-17px"
              :on-back="goBack"
    >
    </g-navbar>

    <div class="flex-1 overflow-auto no-bar">
      <div class="flex items-center">
        <span class="text-16px leading-[24px] font-600">3.任务设置</span>
      </div>

      <div
        class="bg-white mt-17px min-h-[509px] flex flex-col items-center pt-17px"
      >
        <el-form :model="taskData"
                 label-width="auto"
                 class="w-[61.72vw]"
        >
          <el-form-item label="任务名称"
                        required
                        class="mb-32px"
          >
            <el-input v-model="taskData.taskName"
                      maxlength="30"
                      show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="route.query.taskSource !== 'zhrw' ? '任务时间' : ''"
            required
            class="mb-32px"
            :class="{ '!mb-0': isResource && !hasVideo }"
          >
            <div
              v-if="route.query.taskSource !== 'zhrw'"
              class="h-109px bg-[#F3F3FB] br-[4px] w-full flex px-11px text-13px pt-17px"
            >
              <div
                v-if="showName"
                class="text-13px w-84px truncate h-18px lh-[18px] mt-29px"
              >
                {{ showName }}
              </div>
              <div class="mx-17px">
                <div class="h-18px lh-[18px] mb-5px text-[#929296]">
                  发布时间
                </div>
                <el-date-picker
                  v-model="taskData.releaseTime"
                  type="datetime"
                  :show-now="false"
                  :shortcuts="releaseTimeShortcuts"
                  :format="releaseTimeFormat"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD HH:mm"
                />
              </div>
              <div>
                <div class="h-18px lh-[18px] mb-5px text-[#929296]">
                  要求完成时间
                </div>
                <el-date-picker
                  :key="updatePicker"
                  v-model="taskData.requireCompleteTime"
                  type="datetime"
                  :show-now="false"
                  :format="requireCompleteTimeFormat"
                  :shortcuts="requireCompleteShortcuts"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD HH:mm"
                  @change="handleRecommend"
                />
              </div>
            </div>
            <div :class="route.query.taskSource != 'zhrw' ? 'mt-12px' : '-mt-5px'">
              <!-- <div
                class="text-[#6474FD] flex items-center h-18px lh-[18px] cursor-pointer"
                @click="ifUnfold = !ifUnfold"
              >
                <span class="mr-3px">更多配置</span>
                <img
                  :src="
                    ifUnfold
                      ? $g.tool.getFileUrl('taskCenter/arrowBottom.png')
                      : $g.tool.getFileUrl('taskCenter/arrowRight.png')
                  "
                  alt=""
                  class="w-10px h-10px"
                />
              </div> -->
              <div class="mt-13px text-14px text-[#333]">
                <div v-if="isResource && hasVideo" class="filterItem">
                  <div class="flex items-center w-121px">
                    <span>视频进度条：</span>
                    <el-tooltip
                      class="box-item"
                      placement="top"
                      trigger="hover"
                    >
                      <template #content>
                        <div style="max-width: 200px">
                          <div>可拖动：观看视频时可任意拖动进度条</div>
                          <div>
                            首次观看不可拖动：第一次观看某个视频时，无法拖动进度条，当这个视频看完后，再次看这个视频可任意拖动进度条
                          </div>
                        </div>
                      </template>
                      <img
                        :src="$g.tool.getFileUrl('taskCenter/info.png')"
                        alt=""
                        class="w-12px h-12px cursor-pointer"
                      />
                    </el-tooltip>
                  </div>
                  <el-radio-group v-model="taskData.configVideoProgressBar">
                    <el-radio :value="1">
                      可拖动
                    </el-radio>
                    <el-radio :value="2">
                      首次观看不可拖动
                    </el-radio>
                  </el-radio-group>
                </div>
                <div v-if="!isResource">
                  <!-- <div class="filterItem">
                    <div class="flex items-center w-136px">
                      <span>答案公布时间：</span>
                      <el-tooltip
                        class="box-item"
                        placement="top"
                        trigger="hover"
                      >
                        <template #content>
                          <div style="max-width: 200px">
                            <div>
                              学生交卷后：每个学生交卷后可立即看到答案解析及批改结果
                            </div>
                            <div>
                              所有学生交卷后：所有学生交卷，或到达要求完成时间，已交卷的学生可立即看到答案解析及批改结果
                            </div>
                          </div>
                        </template>
                        <img
                          :src="$g.tool.getFileUrl('taskCenter/info.png')"
                          alt=""
                          class="w-12px h-12px cursor-pointer"
                        />
                      </el-tooltip>
                    </div>
                    <el-radio-group v-model="taskData.configAnswerPublishTime">
                      <el-radio :value="1" class="w-86px">
                        <div class="text-[#333333]">
                          学生交卷后
                        </div>
                      </el-radio>
                      <el-radio :value="2">
                        <div class="text-[#333333]">
                          所有学生交卷后<span class="text-[#929296]">(若已到完成时间自动公布)</span>
                        </div>
                      </el-radio>
                    </el-radio-group>
                  </div> -->
                  <div class="filterItem">
                    <div class="flex items-center w-136px">
                      <span>批改设置：</span>
                      <el-tooltip
                        class="box-item"
                        placement="top"
                        trigger="hover"
                      >
                        <template #content>
                          <div style="max-width: 200px">
                            <div>
                              教师批改-教师批改(纠正)学生自查后的主观题对错
                            </div>
                            <div>学生自查-公布答案后学生自评主观题的对错</div>
                          </div>
                        </template>
                        <img
                          :src="$g.tool.getFileUrl('taskCenter/info.png')"
                          alt=""
                          class="w-12px h-12px cursor-pointer"
                        />
                      </el-tooltip>
                    </div>
                    <el-radio-group v-model="taskData.configCorrect" :disabled="(route.query.pattenType as any) == 4 || taskModel == 4">
                      <el-radio :value="1" class="w-86px">
                        <div class="text-[#333333]">
                          教师批改
                        </div>
                      </el-radio>
                      <el-radio :value="2">
                        <div class="text-[#333333]">
                          学生自查
                        </div>
                      </el-radio>
                    </el-radio-group>
                  </div>
                  <!-- <div class="filterItem">
                    <div class="flex items-center w-136px">
                      <span>已到完成时间后：</span>
                      <el-tooltip
                        class="box-item"
                        placement="top"
                        trigger="hover"
                      >
                        <template #content>
                          <div style="max-width: 200px">
                            <div>允许作答：未交卷的学生可以继续作答；</div>
                            <div>
                              不允许作答：到达完成时间后，不论学生是否完成都自动提交试卷，未作答的题目视为“我不会”
                            </div>
                          </div>
                        </template>
                        <img
                          :src="$g.tool.getFileUrl('taskCenter/info.png')"
                          alt=""
                          class="w-12px h-12px cursor-pointer"
                        />
                      </el-tooltip>
                    </div>
                    <el-radio-group v-model="taskData.configReachCompleteTime">
                      <el-radio :value="1" class="w-86px">
                        <div class="text-[#333333]">
                          允许作答
                        </div>
                      </el-radio>
                      <el-radio :value="2">
                        <div class="text-[#333333]">
                          不允许作答<span class="text-[#929296]">(到完成时间自动交卷)</span>
                        </div>
                      </el-radio>
                    </el-radio-group>
                  </div> -->
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="老师留言">
            <el-input
              v-model="taskData.teacherMessage"
              type="textarea"
              :autosize="{ minRows: 5 }"
              maxlength="100"
              show-word-limit
              placeholder="请输入留言内容"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep() {
  .el-form-item__label {
    font-size: 15px;
    color: #333;
  }
}

.filterItem {
  height: 17px;
  line-height: 17px;
  display: flex;
  align-items: center;
  margin-bottom: 9px;
}
</style>
