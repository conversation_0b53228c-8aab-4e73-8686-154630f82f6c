<script>
// 引入饼图图表
import { PieChart } from 'echarts/charts'
// 引入提示框、标题、直角坐标系、数据集、内置数据转换器等组件
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  TransformComponent,
} from 'echarts/components'
// 按需引入echarts核心模块
import * as echarts from 'echarts/core'
// 引入标签布局、图例布局等特性
import { LabelLayout, UniversalTransition } from 'echarts/features'
// 引入 SVG 渲染器
import { SVGRenderer } from 'echarts/renderers'
import { debounce } from 'lodash-es'

import theme from './theme/echarts-theme.json'

// 注册必需的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  PieChart,
  LabelLayout,
  UniversalTransition,
  SVGRenderer,
])

const INIT_TRIGGERS = ['theme',
'initOptions',
'autoResize']
const REWATCH_TRIGGERS = ['manualUpdate', 'watchShallow']

export default defineComponent({
  props: {
    option: {
      type: Object,
      default: () => {},
    },
    theme: {
      type: [String, Object],
      default: () => {},
    },
    initOptions: {
      type: Object,
      default: () => {
        return {
          renderer: 'svg',
        }
      },
    },
    group: {
      type: String,
      default: '',
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    watchShallow: {
      type: Boolean,
      default: false,
    },
    manualUpdate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      lastArea: 0,
    }
  },
  watch: {
    group(group) {
      this.chart.group = group
    },
  },
  created() {
    this.initOptionsWatcher()
    INIT_TRIGGERS.forEach((prop) => {
      this.$watch(
        prop,
        () => {
          this.refresh()
        },
        { deep: true },
      )
    })
    REWATCH_TRIGGERS.forEach((prop) => {
      this.$watch(prop, () => {
        this.initOptionsWatcher()
        this.refresh()
      })
    })
  },
  mounted() {
    if (this.option) {
      echarts.registerTheme('echarts-theme', theme)
      this.init()
    }
  },
  activated() {
    if (this.autoResize)
      this.chart && this.chart.resize()
  },
  unmounted() {
    if (this.chart)
      this.destroy()
  },
  methods: {
    mergeOptions(option, notMerge, lazyUpdate) {
      if (this.manualUpdate)
        this.manualOptions = option

      if (!this.chart)
        this.init(option)

      else
        this.delegateMethod('setOption', option, notMerge, lazyUpdate)
    },
    appendData(params) {
      this.delegateMethod('appendData', params)
    },
    resize(option) {
      this.delegateMethod('resize', option)
    },
    dispatchAction(payload) {
      this.delegateMethod('dispatchAction', payload)
    },
    convertToPixel(finder, value) {
      return this.delegateMethod('convertToPixel', finder, value)
    },
    convertFromPixel(finder, value) {
      return this.delegateMethod('convertFromPixel', finder, value)
    },
    containPixel(finder, value) {
      return this.delegateMethod('containPixel', finder, value)
    },
    showLoading(type, option) {
      this.delegateMethod('showLoading', type, option)
    },
    hideLoading() {
      this.delegateMethod('hideLoading')
    },
    getDataURL(option) {
      return this.delegateMethod('getDataURL', option)
    },
    getConnectedDataURL(option) {
      return this.delegateMethod('getConnectedDataURL', option)
    },
    clear() {
      this.delegateMethod('clear')
    },
    dispose() {
      this.delegateMethod('dispose')
    },
    delegateMethod(name, ...args) {
      if (!this.chart)
        this.init()

      return this.chart[name](...args)
    },
    delegateGet(methodName) {
      if (!this.chart)
        this.init()

      return this.chart[methodName]()
    },
    getArea() {
      return this.$el.offsetWidth * this.$el.offsetHeight
    },
    init(option) {
      if (this.chart)
        return

      const chart = echarts.init(this.$el, this.theme, this.initOptions)
      if (this.group)
        chart.group = this.group

      chart.clear()
      chart.setOption(option || this.manualOptions || this.option || {}, true)
      Object.keys(this.$attrs).forEach((event) => {
        const handler = this.$attrs[event]
        if (event.indexOf('zr:') === 0)
          chart.getZr().on(event.slice(3), handler)

        else
          chart.on(event, handler)
      })
      if (this.autoResize) {
        this.lastArea = this.getArea()
        this.__resizeHandler = debounce(
          () => {
            if (this.lastArea === 0) {
              this.mergeOptions({}, true)
              this.resize()
              this.mergeOptions(this.option || this.manualOptions || {}, true)
            }
            else {
              this.resize()
            }
            this.lastArea = this.getArea()
          },
          100,
          { leading: true },
        )
      }
      this.chart = chart
      Object.defineProperties(this, {
        width: {
          configurable: true,
          get: () => {
            return this.delegateGet('getWidth')
          },
        },
        height: {
          configurable: true,
          get: () => {
            return this.delegateGet('getHeight')
          },
        },
        isDisposed: {
          configurable: true,
          get: () => {
            return !!this.delegateGet('isDisposed')
          },
        },
        computedOptions: {
          configurable: true,
          get: () => {
            return this.delegateGet('getOption')
          },
        },
      })
    },
    initOptionsWatcher() {
      if (this.__unwatchOptions) {
        this.__unwatchOptions()
        this.__unwatchOptions = null
      }
      if (!this.manualUpdate) {
        this.__unwatchOptions = this.$watch(
          'option',
          (val, oldVal) => {
            if (!this.chart && val)
              this.init()

            else
              this.chart.setOption(val, val !== oldVal)
          },
          { deep: !this.watchShallow },
        )
      }
    },
    destroy() {
      if (this.autoResize) {
      }
      this.dispose()
      this.chart = null
    },
    refresh() {
      if (this.chart) {
        this.destroy()
        this.init()
      }
    },
  },
  connect(group) {
    if (typeof group !== 'string')
      group = group.map(chart => chart.chart)

    echarts.connect(group)
  },
  disconnect(group) {
    echarts.disConnect(group)
  },
  getMap(mapName) {
    if (echarts.getMap)
      return echarts.getMap(mapName)

    console.warn('getMap方法在当前按需引入配置下可能不可用')
    return null
  },
  registerMap(mapName, geoJSON, specialAreas) {
    if (echarts.registerMap)
      echarts.registerMap(mapName, geoJSON, specialAreas)

    else
      console.warn('registerMap方法在当前按需引入配置下可能不可用')
  },
  graphic: echarts.graphic || {},
})
</script>

<template>
  <div class="echarts" />
</template>

<style>
.echarts {
  width: 100%;
  height: 100%;
}
</style>
