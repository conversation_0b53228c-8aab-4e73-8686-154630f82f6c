interface groupObj {
  list: any[] // 组里的学生对象集合
  [key: string]: any
}

interface studentObj {

  selectStudentArr: any[] // 当前班级选中的学生对象数组
  selectGroupArr: groupObj[] // 当前班级选中的组对象数组
  [key: string]: any
}

export interface studentDataObj {
  classList: studentObj[] // 自定义已选择班级列表，selectStudentArr或selectGroupArr任中有一个长度不为0，该班级就被返回。
  disabledStudentIds: any[]
  disabledGroupIds: any[]
  [key: string]: any
}
