<script setup lang="ts" name="AfterTraining">
import {
  getChange<PERSON>ist,
  getLayer<PERSON>ist,
  getOringinList,
  saveQuestionBasket,
} from '@/api/syncClass'
import {
  fetchZuJuanInfoApi,
  getQuestionListApi,
  getXkwPaperDataId,
  importBookList,
} from '@/api/taskCenter'
import { useUserStore } from '@/stores/modules/user'
import { deduplicateByKey } from '@/views/teacher/task/createTask/tool'
import QuestionBasket from './components/QuestionBasket.vue'
import QuestionItem from './components/QuestionItem.vue'

let paperList = $ref<any[]>([]) // 总的试卷列表
let questionList = $ref<any[]>([]) // 页面题目列表
let questionBasketList = $ref<any[]>([]) // 试题篮列表
let showPaperList = $ref<any[]>([]) // 试卷列表每次最多显示三个
let batchLoading = $ref(false)
let curPaperId = $ref<any>(null)
let showLoading = $ref(false)
let initLoading = $ref(true)
let originObj = $ref<any>({
  originList: [],
  bookName: '',
  bookCatalogNameList: [],
})// 原卷和分层训练预览共用obj
let curBatch = $ref(0) // 批次
let saveLoading = $ref(false)
let paperType = $ref(1) // 原卷和分卷预览为1，换一批试卷为2,用于拼接来源name
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const isPreview = $computed(() => !!route.query.isPreview)
const isAdd = $computed(() => !!route.query.isAdd)
const isLamination = $computed(() => !!route.query.isLamination)
const questionIds = $computed(() => questionBasketList.map(v => v.questionId))

const isAllInBasket = $computed(() => questionList.every(h => questionIds.includes(h.questionId) && questionList.length))
const title = $computed(() => {
  if (!isLamination && !isPreview)
    return `您正在给${route.query.bookCatalogName}${isAdd ? `${route.query.trainName}新增` : '更换'}题目`

  return `您正在预览${route.query.bookCatalogName}题目`
})
const bookName = $computed(() => {
  if (paperType === 1)
    return `${originObj.bookName}·${originObj.bookCatalogNameList.join('、')}`

  let paper = paperList.find(v => v.bookId === curPaperId)
  return `${paper?.bookName}·${paper?.bookCatalogNameList.join('、')}` || ''
})

async function init() {
  if (isPreview) {
    console.log('isPreview')
    previewInit()
    return
  }
  if (isLamination) {
    console.log('isLamination')
    laminationInit()
    return
  }
  addOrEditInit()
}

// 获取分层训练,模式和原卷一致，所以用统一个obj
async function getLayerQuestion() {
  try {
    let res = await getLayerList({
      bookCatalogId: route.query.bookCatalogId,
      bookId: route.query.bookId,
    })
    originObj.originList = res?.questionList || []
    originObj.bookName = res?.bookName || ''
    originObj.bookCatalogNameList = res?.bookCatalogNameList || []
  }
  catch {
    originObj.originList = []
    originObj.bookName = ''
    originObj.bookCatalogNameList = []
  }
}

// 获取原卷
async function getOringin() {
  try {
    let res = await getOringinList({
      bookCatalogId: route.query.bookCatalogId,
      bookCatalogClassResourceId: route.query.bookCatalogClassResourceId,
    })
    originObj.originList = res?.questionList || []
    originObj.bookName = res?.bookName || ''
    originObj.bookCatalogNameList = res?.bookCatalogNameList || []
  }
  catch {
    originObj.originList = []
    originObj.bookName = ''
    originObj.bookCatalogNameList = []
  }
}

// 获取换一批试卷列表
async function getPaperList() {
  try {
    let res = await getChangeList({
      bookCatalogId: route.query.bookCatalogId,
    })
    paperList = res || []
  }
  catch {
    paperList = []
  }
}
// 预览初始化
async function previewInit() {
  await getOringin()
  paperType = 1
  curPaperId = Number(route.query.bookCatalogId)
  questionList = originObj.originList
  $g.tool.renderMathjax()
  initLoading = false
}

// 新增或者编辑初始化
async function addOrEditInit() {
  console.log('addOrEditInit')
  if (!isAdd)
    await getOringin()

  await getPaperList()
  curBatch = isAdd ? 0 : -1
  getShowPaperList()
  paperType = isAdd ? 2 : 1
  curPaperId = isAdd ? showPaperList[0]?.bookId : Number(route.query.bookCatalogId)
  questionList = isAdd ? (showPaperList[0]?.questionList || []) : originObj.originList

  initLoading = false
  $g.tool.renderMathjax()
  if (isAdd) return
  questionBasketList = [...questionList]
}

// 分层训练初始化
async function laminationInit() {
  await getLayerQuestion()
  paperType = 1
  curPaperId = Number(route.query.bookCatalogId)
  questionList = originObj.originList
  $g.tool.renderMathjax()
  initLoading = false
}

function getShowPaperList() {
  showPaperList = paperList.slice(curBatch * 3, (curBatch + 1) * 3).map((v, index) => {
    return {
      ...v,
      title: `试卷${['一',
'二',
'三'][index]}`,
    }
  })
}
async function batch() {
  batchLoading = true
  setTimeout(() => {
    batchLoading = false
  }, 300)
  let maxBatch = Math.ceil(paperList.length / 3) // 最多几批
  if (curBatch + 1 == maxBatch) {
    $g.showToast('当前题库已无题目可更换')
    return
  }
  curBatch++
  getShowPaperList()

  $g.showToast('已更换新的题目')
  if (isAdd) {
    changePaper(showPaperList[0]?.bookId, 2)
    return
  }
  changePaper(Number(route.query.bookCatalogId), 1)
}

function dealQuestion(questionItem) {
  let index = questionBasketList.findIndex(h => h.questionId === questionItem.questionId)
  if (index === -1)
    questionBasketList.push(questionItem)

  else
    questionBasketList.splice(index, 1)
}

async function save() {
  let {
    schoolClassId,
    bookCatalogId,
    bookCatalogClassResourceId,
    isDefault,
  } = route.query
  if (!questionBasketList.length || !schoolClassId || !bookCatalogId) {
    $g.showToast('请添加试题')
    return
  }
  saveLoading = true
  try {
    await saveQuestionBasket({
      schoolClassId,
      bookCatalogId,
      bookCatalogClassResourceId,
      isDefault,
      questionIdList: questionBasketList.map(v => v.questionId),
      bookCatalogName: route.query.trainName,
    })
    $g.showToast('保存成功')
    router.back()
    setTimeout(() => {
      saveLoading = false
    }, 200)
  }
  catch {
    setTimeout(() => {
      saveLoading = false
    }, 200)
  }
}

function dealAllQuestion() {
  if (isAllInBasket)
    questionBasketList = questionBasketList.filter(h => !questionList.some(v => v.questionId === h.questionId))

  else
    questionBasketList = deduplicateByKey([...questionBasketList, ...questionList], 'questionId')
}

function toEdit() {
  let updateQuery = { ...route.query }
  delete updateQuery.isPreview
  router.replace({
    query: updateQuery,
  })
  initLoading = true
  setTimeout(() => {
    init()
  }, 0)
}

onBeforeMount(() => {
  init()
})

function changePaper(paperId, type) {
  curPaperId = paperId
  showLoading = true
  if (type === 1) {
    questionList = originObj.originList
    paperType = 1
  }
  else {
    questionList = paperList.find(h => h.bookId === paperId).questionList
    paperType = 2
  }

  setTimeout(async () => {
    showLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }, 500)
}

// 去学科网选题逻辑

const ZXXKObj: any = {
  handler: null,
  isListen: false,
  qmdrKey: null,
}

async function goXKW() {
  const res = await fetchZuJuanInfoApi()
  ZXXKObj.qmdrKey = res?.qmdrKey
  ZXXKObj.handler = window.open(
    `https://zujuan.qimingdaren.com/#/third/teacherURedirect?jztToken=${userStore.jztToken}`,
  )
}

onBeforeUnmount(() => {
  if (ZXXKObj.isListen) {
    window.removeEventListener('message', onPostMessage)
    ZXXKObj.isListen = false
  }
})
onMounted(() => {
  if (!ZXXKObj.isListen) {
    window.addEventListener('message', onPostMessage)
    ZXXKObj.isListen = true
  }
})

/** 监听学科网发送的事件 */
function onPostMessage(event) {
  console.log('onPostMessage', event)
  const {
    data: {
      paperid,
      openid,
    },
  } = event
  if (paperid && openid) {
    ZXXKObj.handler &&
    ZXXKObj.handler.postMessage('close', 'https://zujuan.qimingdaren.com')
    $g.isFlutter && $g.flutter('closeXueKeWang')
    loadXKWQuestion(paperid, openid)
  }
}

/**
  获取学科网的题目列表
 */
async function loadXKWQuestion(paperid, openid) {
  console.log('🚀 ~ loadXKWQuestion ~ paperid, openid:', paperid, openid)

  try {
    // 1、通过paperId获取xkwPaperDataId
    const PaperData = await getXkwPaperDataId(
      {
        paperId: paperid,
        openId: openid,
      },
      {
        headers: {
          Authorization: ZXXKObj.qmdrKey,
        },
      },
    )

    // 2、通过xkwPaperDataId获取bookId
    const data = await importBookList(
      { xkwPaperDataId: PaperData?.xkwPaperDataId },
      {
        headers: {
          Authorization: ZXXKObj.qmdrKey,
        },
      },
    )
    if (!data)
      throw new Error('获取bookId失败')

    // 3、通过bookId获取题目列表
    const res: any = await getQuestionListApi({
      bookId: data?.bookId,
      page: 1,
      pageSize: 999,
    })

    console.log('🚀 ~ 导入题目 ~ res.list:', res.list)
    questionBasketList = deduplicateByKey([...questionBasketList, ...res.list], 'questionId')
  }
  catch (err) {
    console.log(err)
  }
}
</script>

<template>
  <div class="p-26px flex flex-col h-full select-none">
    <g-navbar title="退出">
      <template #right>
        <el-button
          v-if="!isPreview && !isLamination"
          type="primary"
          class="h-43px w-102px br-[9px]"
          :loading="saveLoading"
          @click="save"
        >
          保存
        </el-button>
      </template>
    </g-navbar>
    <div class="flex-1 overflow-y-auto mt-11px">
      <div class="flex justify-between items-center">
        <g-mathjax :text="title" class="font-600 !text-17px text-[#333]" />
        <el-button
          v-if="isPreview && !isLamination"
          type="primary"
          class="h-30px w-66px"
          @click="toEdit"
        >
          去修改
        </el-button>
      </div>
      <g-loading v-if="initLoading" class="h-200px"></g-loading>
      <div v-else>
        <div v-if="!isPreview && !isLamination" class="flex items-center justify-between mt-19px mb-17px">
          <div class="flex items-center  text-13px">
            <div
              v-if="!isAdd "
              class="flex items-center w-94px h-43px bg-[#FFFFFF] br-[6px] justify-center lh-[43px] font-500 cursor-pointer"
              :class="[curPaperId === Number(route.query.bookCatalogId) ? 'active' : '', paperList.length ? 'mr-17px' : 'mr-21px']"
              @click="changePaper(Number(route.query.bookCatalogId), 1)"
            >
              <img :src="curPaperId === Number(route.query.bookCatalogId) ? $g.tool.getFileUrl('syncClass/paper-white.png') : $g.tool.getFileUrl('syncClass/paper.png')"
                   alt=""
                   class="w-17px h-17px"
              >
              <span class="ml-4px">原试卷</span>
            </div>
            <div
              v-for="(paperItem, index) in showPaperList"
              :key="paperItem.bookId"
              class="flex items-center w-115px h-43px bg-[#FFFFFF] br-[6px] justify-center  lh-[43px] font-500 cursor-pointer"
              :class="[curPaperId === paperItem.bookId ? 'active' : '', index !== showPaperList.length - 1 ? 'mr-17px' : 'mr-15px']"
              @click="changePaper(paperItem.bookId, 2)"
            >
              {{ paperItem.title }}
            </div>
            <el-button
              v-if="paperList.length"
              class="flex items-center !px-2px mr-21px"
              text
              :loading="batchLoading"
              @click="batch"
            >
              <img :src="$g.tool.getFileUrl('syncClass/refresh.png')"
                   alt=""
                   class="w-14px h-14px"
              >
              <span class="ml-4px text-[#6474FD]">换一批</span>
            </el-button>
            <el-button v-if="!isPreview && !isLamination"
                       class="h-30px bg-[#FFFFFF] text-[#5864F8] text-15px font-500 br-[4px] border border-[#6474FD] ml-0"
                       @click="goXKW"
            >
              去学科网选题
            </el-button>
          </div>
          <div
            v-if="questionList.length"
            class="h-30px text-15px font-400 w-143px   flex items-center justify-center cursor-pointer myButton rounded-[4px]"
            :class="isAllInBasket ? 'border border-[#FF4646] text-[#FF4646]' : 'bg-[#6474FD] text-white'"
            @click="dealAllQuestion"
          >
            <img :src="isAllInBasket ? $g.tool.getFileUrl('syncClass/minus.png') : $g.tool.getFileUrl('syncClass/plus.png')"
                 alt=""
                 class="w-13px h-13px flex-shrink-0 mr-[4px]"
            >
            <span>{{ isAllInBasket ? '整卷移除试题篮' : '整卷加入试题篮' }}</span>
          </div>
        </div>
        <g-loading v-if="showLoading" class="h-200px"></g-loading>

        <div v-else-if="questionList.length">
          <g-mathjax v-if="bookName"
                     :text="`以下题目来源: ${bookName}`"
                     class="!text-15px text-[#33333375] my-17px"
          />
          <QuestionItem
            v-for="(questionItem, index) in questionList"
            :key="questionItem.id"
            :question-item="questionItem"
            :index="index + 1"
            class="mt-13px"
          >
            <template #footer>
              <div class="mt-17px mb-13px">
                <div v-if="questionItem.isShowAnswer">
                  <div class="mb-13px flex items-start">
                    <div class="text-[#6474FD] mr-13px">
                      [详情]
                    </div>
                    <div>
                      <div
                        v-for="(v, i) in questionItem.subQuestions"
                        :key="i"
                        :class="{
                          'mt-13px': i !== 0,
                        }"
                        class="flex items-start"
                      >
                        <div v-if="questionItem.subQuestions.length > 1" class="mr-5px">
                          ({{ i + 1 }})
                        </div>
                        <g-mathjax :text="v.subQuestionParse" class="text-15px" />
                      </div>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <div class="text-[#6474FD] mr-13px">
                      [答案]
                    </div>
                    <div>
                      <div
                        v-for="(v, i) in questionItem.subQuestions"
                        :key="i"
                        :class="{
                          'mt-13px': i !== 0,
                        }"
                        class="flex items-start"
                      >
                        <div v-if="questionItem.subQuestions.length > 1" class="mr-5px">
                          ({{ i + 1 }})
                        </div>
                        <g-mathjax :text="v.subQuestionAnswer" class="text-15px" />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center justify-between mt-13px">
                  <div
                    class="flex items-center cursor-pointer"
                    @click="() => {
                      questionItem.isShowAnswer = !questionItem.isShowAnswer
                      $g.tool.renderMathjax()
                    }"
                  >
                    <img :src="questionItem.isShowAnswer ? $g.tool.getFileUrl('syncClass/eye-open.png') : $g.tool.getFileUrl('syncClass/eye-close.png')"
                         alt=""
                         class="w-17px h-17px"
                    >
                    <span class="ml-4px text-15px font-500 text-[#6474FD] h-21px lh-[21px]">答案及解析</span>
                  </div>
                  <div
                    v-if="!isPreview && !isLamination"
                    class="flex items-center van-haptics-feedback border w-113px h-30px rounded-[4px] justify-center"
                    :class="questionIds.includes(questionItem.questionId) ? 'border-[#FF4646] text-[#FF4646]' : 'border-[#6474FD] text-[#6474FD]'"
                    @click="dealQuestion(questionItem)"
                  >
                    <img :src="questionIds.includes(questionItem.questionId) ? $g.tool.getFileUrl('syncClass/minus.png') : $g.tool.getFileUrl('syncClass/purple-plus.png')"
                         alt=""
                         class="w-13px h-13px mr-4px"
                    >
                    <span class="ml-4px text-15px h-17px lh-[17px]">{{ questionIds.includes(questionItem.questionId) ? '移除试题篮' : '加入试题篮' }}</span>
                  </div>
                </div>
              </div>
            </template>
          </QuestionItem>
        </div>
        <g-empty v-else></g-empty>
      </div>
    </div>
    <QuestionBasket v-if="!isPreview && !isLamination"
                    :question-basket-list="questionBasketList"
                    @clear-question="questionBasketList = []"
    />
  </div>
</template>

<style scoped lang="scss">
.myButton {
  transition: opacity 0.2s;
&:hover {
opacity: 0.8;
}

}
.active {
  background: #6474FD !important;
  color: #fff !important;
}
</style>
