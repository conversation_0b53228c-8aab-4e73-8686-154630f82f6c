<script setup lang="ts">
import { submitTeacherCorrect } from '@/api/correctionPage'
import { CorrectStatus } from '../type'

const props = defineProps({
  answerList: {
    type: Array as PropType<any[]>,
    required: true,
  },
  activeSubQuestionId: {
    type: Number,
    required: true,
  },
})
const emit = defineEmits(['correctStatusChange'])

// 小题切换时，当前小问的批改按钮滚动到视口
watch(() => props.activeSubQuestionId, (newVal) => {
  const container = document.getElementById(`correction_btn-container_${newVal}`)
  if (container) {
    container.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
  }
})

/** 批改结果按钮 */
const correctOptions = $ref([
  {
    label: '答对了',
    value: CorrectStatus.RIGHT,
    className: 'border-[#00DA8F]',
    activeClassName: '!border-[transparent] !bg-[#00DA8F1A] !text-[#00DA8F]',
  },
  {
    label: '答错了',
    value: CorrectStatus.ERROR,
    className: 'border-[#FF4446]',
    activeClassName: '!border-[transparent] !bg-[#FF44461A] !text-[#FF4446]',
  },
  {
    label: '部分对',
    value: CorrectStatus.HALF_RIGHT,
    className: 'border-[#FFBC29]',
    activeClassName: '!border-[transparent] !bg-[#FFBC2926] !text-[#FFBC29]',
  },
])

// 提交批改结果
async function handleClick(subQuestion, value) {
  if (subQuestion.teacherCorrect === value)
    return

  console.log('批改结果', subQuestion, value)
  try {
    await submitTeacherCorrect({
      exerciseSubId: subQuestion.exerciseSubId,
      isCorrect: value,
    })
    subQuestion.teacherCorrect = value
    emit('correctStatusChange', value)
  }
  catch (err) {
    console.log('请求失败', err)
  }
}
</script>

<template>
  <div class="student-answer-container " :class="!$g.isPC && 'no-bar'">
    <div
      v-for="(subQuestion, index) in answerList"
      :id="`correction_btn-container_${subQuestion.subQuestionId}`"
      :key="subQuestion.subQuestionId"
      class=" pb-10px mb-10px"
      :class="index !== answerList.length - 1 && 'border-b border-[#E8E8E8]'"
    >
      <div v-if="answerList.length > 1" class="text-[#333333] text-[17px] mb-9px font-600">
        第{{ index + 1 }}问
      </div>

      <div class="text-[#999999]  mb-13px">
        学生自评：{{ ['未自评', '答错了', '部分对', '答对了', '我不会'][subQuestion.studentCorrect || 0] }}
      </div>
      <div class="mb-9px">
        请选择批改结果
      </div>
      <!-- 批改按钮 -->
      <div class="flex">
        <button
          v-for="(option, i) in correctOptions"
          :key="option.value"
          class="flex-1 h-30px text-[13px] border br-[6px] text-[#999] flex-cc bg-[white] mr-13px"
          :class="[
            option.className,
            subQuestion.teacherCorrect === option.value
              && option.activeClassName,
            i === correctOptions.length - 1 && 'mr-0',
          ]"
          @click="handleClick(subQuestion, option.value)"
        >
          <svg-task-resultRight
            v-if="option.value === CorrectStatus.RIGHT"
            width="1.3em"
            class="mr-[9px]"
          />
          <svg-task-resultHalfRight
            v-if="option.value === CorrectStatus.HALF_RIGHT"
            width="1.3em"
            class="mr-[9px]"
          />
          <svg-task-resultError
            v-if="option.value === CorrectStatus.ERROR"
            width="1.3em"
            class="mr-[9px]"
          />
          <span class="!text-[#333333]">{{ option.label }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.student-answer-container {
  flex: 1;
  padding: 17px;
  background-color: #fff;
  border-radius: 6px;
  overflow-y: auto;

  &::-webkit-scrollbar-thumb {
    background-color: #e8e8e8;
  }
}
</style>
