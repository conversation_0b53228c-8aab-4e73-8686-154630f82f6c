import type { PluginOption } from 'vite'
import fs from 'node:fs'
import path, { resolve } from 'node:path'

import inject from '@rollup/plugin-inject'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import { VantResolver } from '@vant/auto-import-resolver'
import legacy from '@vitejs/plugin-legacy'
import ReactivityTransform from '@vue-macros/reactivity-transform/vite'
import progress from 'cc-vite-progress'
import { visualizer } from 'rollup-plugin-visualizer'
import AutoImport from 'unplugin-auto-import/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import vueSetupExtend from 'unplugin-vue-setup-extend-plus/vite'
import { Plugin as importToCDN } from 'vite-plugin-cdn-import-async'
import { createPrintUrlsPlugin } from './printUrls'
import versionPlugin from './version'

const svgDirs = getSubDirectories('../src/assets/svg')

export function setupVitePlugins({ ENV, version }): PluginOption[] {
  const { VITE_APP_ENV, VITE_REPORT, VITE_SOURCEMAP } = ENV
  let plugins: PluginOption = [
    ReactivityTransform(),
    createPrintUrlsPlugin(),
    Icons({
      compiler: 'vue3',
      autoInstall: true,
      customCollections: iconsCustomCollections(),
    }),
    AutoImport({
      imports: ['vue', 'pinia', 'vue-router', '@vueuse/core'],
      resolvers: [ElementPlusResolver(), VantResolver()],
      dts: 'types/auto-imports.d.ts',
    }),
    Components({
      dirs: ['src/components'],
      resolvers: [
        ElementPlusResolver(),
        VantResolver(),
        IconsResolver({
          prefix: 'svg',
          // 自定义图标集合的前缀
          customCollections: svgDirs,
          enabledCollections: ['ri'],
        }),
      ],
      dts: 'types/components.d.ts',
    }),
    inject({
      include: ['src/**/**'],
      exclude: ['**/*.scss'],
      modules: {
        $g: resolve('src/utils/index'),
      },
    }),
    vueSetupExtend({}),
  ]
  if (VITE_REPORT == 'open') {
    plugins = [
      ...plugins,
      visualizer({ open: true, gzipSize: true }),
    ] as PluginOption[]
  }

  // 非开发环境
  if (!VITE_APP_ENV?.includes('development')) {
    plugins = [
      ...plugins,
      versionPlugin({ version }),
      importToCDN({
        modules: [
          {
            name: 'lodash',
            var: 'lottie',
            path: 'https://frontend-cdn.qimingdaren.com/common/lottie.min.js',
          },
        ],
      }),
      progress({ projectName: '新版教师用户端' }),
    ]
  }

  // 生产环境
  if (['production'].includes(VITE_APP_ENV)) {
    plugins = [
      ...plugins,
      legacy({
        targets: ['Android >= 6', 'iOS >= 9'],
        modernPolyfills: true,
      }),
    ]
  }

  // souremap
  if (VITE_SOURCEMAP == 'open') {
    plugins = [
      ...plugins,
      sentryVitePlugin({
        org: 'qmdr',
        project: 'teacher-user',
        authToken:
          '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',
        debug: false, // 禁用调试信息打印
        silent: true,
      }),
    ]
  }

  return plugins
}

function iconsCustomCollections() {
  const collections = svgDirs.reduce(
    (acc, dirName) => {
      acc[dirName] = FileSystemIconLoader(
        `./src/assets/svg/${dirName}`,
        svg => svg.replace(/^<svg /, '<svg fill="currentColor" '),
      )
      return acc
    },
    {} as Record<string, ReturnType<typeof FileSystemIconLoader>>,
  )
  return collections
}

function getSubDirectories(folderPath: string): string[] {
  const absolutePath = path.resolve(__dirname, folderPath)

  try {
    return fs.readdirSync(absolutePath).filter((file) => {
      const fullPath = path.join(absolutePath, file)
      return fs.statSync(fullPath).isDirectory()
    })
  }
  catch (error) {
    console.error(`Error reading directory ${folderPath}:`, error)
    return []
  }
}
