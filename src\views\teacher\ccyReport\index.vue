<script setup lang="ts" name="ActivityList">
import { getActivityList } from '@/api/activity'

let keyword = $ref<any>('')
let pageOption = $ref<any>({
  page: 1,
  page_size: 10,
  total: 0,
})
const router = useRouter()
let dataList = $ref<any>([])
let scrollRef = $ref<any>(null)
let scrollTop = $ref<any>(0)
fetchList()
/* 获取活动列表 */
async function fetchList(addToEnd = false) {
  try {
    let res = await getActivityList({
      page: pageOption.page,
      pageSize: pageOption.page_size,
      keyword,
      isDelete: 1,
    })
    pageOption.total = res.total
    dataList = addToEnd ? dataList.concat(res.list) : res.list
  }
  catch (err) {
    dataList = []
    console.log('获取活动列表出错', err)
  }
}
async function pullup() {
  pageOption.page += 1
  await fetchList(true)
}
async function pulldown() {
  pageOption.page = 1
  await fetchList()
}
/* 搜索 */
async function search(reset = false) {
  keyword = reset ? '' : keyword
  pageOption.page = 1
  await fetchList()
}
/* 跳转到报告详情 */
function toReport(item) {
  scrollTop = scrollRef?.scrollTop
  router.push({
    name: 'ReportDetail',
    query: {
      activityId: item.activityId,
      activityName: item.activityName,
    },
  })
}
onActivated(() => {
  if (scrollTop) scrollRef.scrollTop = scrollTop
})
</script>

<template>
  <div class=" pt-26px">
    <div class="px-26px">
      <g-navbar title="活动报告查询"></g-navbar>
      <div class="flex items-center mb-17px mt-21px">
        <div class="text-[16px]">
          活动名称
        </div>
        <el-input
          v-model="keyword"
          style="width: 257px"
          class="mx-13px"
          clearable
          placeholder="请输入活动名称"
        />
        <el-button color="#6474FD"
                   class="w-64px ml-5px"
                   @click="search(false)"
        >
          搜索
        </el-button>
        <el-button class="w-64px ml-5px" @click="search(true)">
          重置
        </el-button>
      </div>
    </div>
    <!-- 活动列表 -->
    <div ref="scrollRef" class="h-[calc(100vh-26px-34px-70px)] overflow-auto px-26px no-bar">
      <g-list

        v-model:data="dataList"
        :page-option="pageOption"
        url="/tutoring/admin/activity/list"
        @pulldown="pulldown"
        @pullup="pullup"
      >
        <div
          v-for="item in dataList"
          :key="item.activityId"
          class="flex items-center justify-between cursor-pointer px-25px py-15px mb-13px rounded-[4px] border-1px border-solid border-[#DADDE8] bg-white"
          @click="toReport(item)"
        >
          <div class="flex-1">
            <div class="text-[17px]">
              {{ item.activityName }}
            </div>
            <div class="text-[#666] my-13px">
              {{ item.sysSubjectList.map(v => v.sysSubjectName).join('、') }}
            </div>
            <div class="bg-[#F4F5FA] border-1px border-solid border-[#D7DDE9] text-[#6C6C74] leading-[24px] rounded-[5px] w-[34px] text-[13px] text-center">
              {{ item.sysStageName }}
            </div>
          </div>
          <img
            :src="$g.tool.getFileUrl('common/right-circle.png')"
            alt=""
            srcset=""
            class="w-21px h-21px flex-shrink-0"
          >
        </div>
      </g-list>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
