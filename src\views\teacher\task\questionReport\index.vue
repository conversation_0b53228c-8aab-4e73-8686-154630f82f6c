<script setup lang="ts" name="QuestionReport">
import { getClassSelectApi, getGradeSelectApi, getStudentReportApi } from '@/api/taskCenter'
import { useUserStore } from '@/stores/modules/user'
import IndicatorDrawer from './components/IndicatorDrawer.vue'
import PaperResult from './components/PaperResult.vue'
import RevisedReport from './components/RevisedReport.vue'
import StudentReport from './components/StudentReport.vue'

const route = useRoute()
const userStore = useUserStore()
let containerRef = $ref<any>(null)
let loading = $ref(true)
let classList = $ref<any>([])
let gradeList = $ref<any>([])
let currentClass = $ref<any>(null)
let currentGrade = $ref<any>(null)
let activeName = $ref<any>(1)
let showDrawer = $ref(false)
let reportAll = $ref<any>() // 三个tab接口一次性返回

const tabList = $ref<any>([
  {
    label: '学生报告',
    name: 1,
  },
  {
    label: '整卷答题结果',
    name: 2,
  },
  {
    label: route.query.exerciseSourceType == '1' ? '错题重做报告' : '错题订正报告',
    name: 3,
  },
])

const params = $computed(() => {
  return {
    sysGradeId: currentGrade,
    schoolClassId: currentClass,
  }
})
async function init(initId?) {
  try {
    if (!route.query.exerciseSourceType || !route.query.exerciseSourceId) {
      loading = false
      reportAll = null
      return
    }
    if (!route.query.schoolId && !userStore.userInfo.schoolList?.[0].schoolId && !userStore.userInfo.schoolId) return
    let res = await getGradeSelectApi({
      exerciseSourceType: route.query.exerciseSourceType,
      exerciseSourceId: route.query.exerciseSourceId,
      schoolId: route.query.schoolId ?? userStore.userInfo.schoolList?.[0].schoolId,
    // exerciseSourceType: 2,
    // exerciseSourceId: 320,
    // schoolId: 10,
    })
    gradeList = res || []
    currentGrade = initId ?? res[0]?.sysGradeId
  }
  catch {
    loading = false
    reportAll = null
  }
}

onBeforeMount(async () => {
  if (route.query.activeName)
    activeName = Number(route.query.activeName)

  await init(route.query.gradeId && Number(route.query.gradeId))
  await getClass(route.query.classId && Number(route.query.classId))
  // await init()
  // await getClass()
  getReport()
})

onMounted(() => {
  $g.tool.renderMathjax()
})
async function getClass(initId?) {
  if (!route.query.schoolId && !userStore.userInfo.schoolList?.[0].schoolId && !userStore.userInfo.schoolId) return
  let res = await getClassSelectApi({
    exerciseSourceType: route.query.exerciseSourceType,
    exerciseSourceId: route.query.exerciseSourceId,
    schoolId: route.query.schoolId ?? userStore.userInfo.schoolList?.[0].schoolId ?? userStore.userInfo.schoolId,
    sysGradeId: currentGrade,
    // exerciseSourceType: 2,
    // exerciseSourceId: 320,
    // schoolId: 10,
    // sysGradeId: 12,
  })
  classList = res || []
  currentClass = initId ?? res[0]?.schoolClassId
}

async function getReport() {
  if (!route.query.schoolId && !userStore.userInfo.schoolList?.[0].schoolId && !userStore.userInfo.schoolId) return
  try {
    loading = true
    let res = await getStudentReportApi({
      exerciseSourceType: route.query.exerciseSourceType,
      exerciseSourceId: route.query.exerciseSourceId,
      schoolId: route.query.schoolId ?? userStore.userInfo.schoolList?.[0].schoolId ?? userStore.userInfo.schoolId,
      sysGradeId: currentGrade,
      schoolClassId: currentClass,
      beginDateTime: route.query.startTime,
      endDateTime: route.query.endTime,
      // exerciseSourceType: 1,
      // exerciseSourceId: 2618,
      // schoolId: 3,
      // sysGradeId: 12,
      // schoolClassId: 2167,
    })
    reportAll = res || {}
  }
  catch {
    reportAll = null
  }
  finally {
    loading = false
  }
}

async function changeTab() {
  loading = true
  setTimeout(() => {
    loading = false
  }, 200)
  await nextTick()
  if (containerRef)
    containerRef.scrollTop = 0
}
/* 刷新数据 */
async function refresh() {
  if (!route.query.schoolId && !userStore.userInfo.schoolList?.[0].schoolId && !userStore.userInfo.schoolId) return
  try {
    let res = await getStudentReportApi({
      exerciseSourceType: route.query.exerciseSourceType,
      exerciseSourceId: route.query.exerciseSourceId,
      schoolId: route.query.schoolId ?? userStore.userInfo.schoolList?.[0].schoolId ?? userStore.userInfo.schoolId,
      sysGradeId: currentGrade,
      schoolClassId: currentClass,
      beginDateTime: route.query.startTime,
      endDateTime: route.query.endTime,
    })
    reportAll = res || {}
  }
  catch {
    reportAll = null
  }
}
async function changeGrade() {
  await getClass()
  getReport()
}
onActivated(() => {
  refresh()
})
</script>

<template>
  <div class="p-26px flex flex-col h-[100vh] " style="width: 100vw">
    <g-navbar>
      <template #title>
        <g-mathjax :text="(route.query.title as any)" class="text-17px font-600" />
      </template>
      <template #right>
        <div class="flex ml-[22px] items-center flex-shrink-0">
          <div class="mr-[10px] text-[14px] text-[#333333]">
            年级
          </div>
          <el-select
            v-model="currentGrade"
            class="w-128px h-34px br-[5px] mr-17px"
            @change="changeGrade"
          >
            <el-option
              v-for="item in gradeList"
              :key="item.sysGradeId"
              :label="item.sysGradeName"
              :value="item.sysGradeId"
            />
          </el-select>
          <div class="mr-[10px] text-[14px] text-[#333333]">
            班级
          </div>
          <el-select
            v-model="currentClass"
            class="w-140px h-34px br-[5px]"
            @change="getReport"
          >
            <el-option
              v-for="item in classList"
              :key="item.schoolClassId"
              :label="item.className"
              :value="item.schoolClassId"
            />
          </el-select>
          <div class="text-[#666666] text-15px flex-cc ml-16px van-haptics-feedback" @click="showDrawer = true">
            <img :src="$g.tool.getFileUrl('taskCenter/gray-info.png')"
                 alt=""
                 class="w-19px h-19px"
            >
            <span class="ml-5px">指标说明</span>
          </div>
        </div>
      </template>
    </g-navbar>
    <div class="mt-21px">
      <el-tabs v-model="activeName" @tab-change="changeTab">
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="index"
          :label="item?.label"
          :name="item?.name"
        >
        </el-tab-pane>
      </el-tabs>
    </div>

    <g-loading v-if="loading && !(activeName === 3)" class="h-200px"></g-loading>
    <div
      v-if="reportAll"
      v-show="!loading"
      ref="containerRef"
      class="flex-1 overflow-auto "
    >
      <StudentReport v-if="activeName == 1"
                     :report-all="reportAll"
                     :params="params"
      ></StudentReport>
      <PaperResult v-if="activeName == 2"
                   :report-all="reportAll"
                   :params="params"
      ></PaperResult>
      <RevisedReport v-if="activeName == 3"
                     :report-all="reportAll"
                     :params="params"
      ></RevisedReport>
    </div>
    <g-empty v-else-if="!loading"></g-empty>
    <IndicatorDrawer v-model="showDrawer"></IndicatorDrawer>
  </div>
</template>

<style scoped lang="scss">
:deep(){
  .el-tabs__nav-wrap::after {
    background-color:transparent;
  }
  .el-tabs__item {
    color: #666666;
    font-size: 15px;
    height: 29px;
  }
  .el-tabs__item.is-active {
    color: #333333;
    font-size: 15px;
    font-weight: 500;
  }
  .el-tabs__header{
  margin:0 0 13px !important
  }
}
</style>
