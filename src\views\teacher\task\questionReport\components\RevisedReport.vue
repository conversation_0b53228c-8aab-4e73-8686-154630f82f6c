<script setup lang="ts">
import { getQuestionListDetailApi } from '@/api/taskCenter'
import { useSettingStore } from '@/stores/modules/setting'
import QuestionPanel from './QuestionPanel.vue'
import QuestionStemItem from './QuestionStemItem.vue'

const props = defineProps({
  reportAll: {
    type: Object,
    required: true,
  },
  params: {
    type: Object,
    required: true,
  },
})

let curStudentId = $ref<any>()
let checked = $ref(2)
let keyword = $ref('')
let loading = $ref(true)
let curStudentObj = $ref<any>({})
const settingStore = useSettingStore()
const router = useRouter()
const route = useRoute()
const studentList = $computed(() => {
  if (!keyword) return props.reportAll.amendReport.studentList
  return props.reportAll.amendReport.studentList.filter(v => v.studentName.includes(keyword))
})
const isActivity = $computed(() => {
  return (route.query.exerciseSourceType as any) == 1
})
const questionList: any = $computed(() => {
  return curStudentObj.errorList?.filter(v => v.correctiveState === checked) || []
})

watch(() => curStudentId, (val) => {
  if (val) {
    curStudentObj = studentList.find(v => v.schoolStudentId == curStudentId)
    getQuestion()
  }
}, {
  immediate: true,
})
async function getQuestion() {
  if (!curStudentObj.errorList.length) {
    loading = false
    return
  }
  try {
    let res = await getQuestionListDetailApi({
      questionIdList: curStudentObj.errorList.map(v => v.questionId),
    })
    curStudentObj.errorList.forEach((item) => {
      let find = res?.find(v => v.questionId == item.questionId) || {}
      Object.assign(item, find)
    })
    loading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    console.log(e)
  }
  finally {
    loading = false
  }
}
function changeStudent(item) {
  if (curStudentId == item.schoolStudentId) return
  loading = true
  curStudentId = item.schoolStudentId
}

function goDetail(item, index) {
  router.push({
    name: 'CorrectionDetail',
    query: {
      ...route.query,
      ...props.params,
      exerciseId: item.exerciseId,
      schoolStudentId: curStudentId,
      quesIndex: index,
      questionId: item?.questionId ?? '',
      checked,
    },
  })
}
function onQuesSelect([item, index]) {
  const dom = document.getElementById(`revised-ques-item-${index}`)
  dom?.scrollIntoView?.()
}
watch(() => props.reportAll.amendReport.studentList, (val) => {
  if (val.length)
    curStudentId = val[0].schoolStudentId
}, {
  immediate: true,
})
</script>

<template>
  <div>
    <div v-if="reportAll.amendReport.studentList.length" class="flex">
      <div
        style="width:14.55vw"
        class="flex-shrink-0 bg-white p-13px pb-0 br-[9px] text-14px  self-start   flex flex-col"
        :style="{
          height: `calc(100vh - 41px - ${settingStore.navBarTotalHeight}px - 52px)`,
        }"
      >
        <el-input v-model="keyword"
                  placeholder="姓名检索"
                  class="mb-9px flex-shrink-0"
        ></el-input>
        <div class="flex-1 overflow-auto">
          <div
            v-for="item in studentList"
            :key="item.schoolStudentId"
            class="pl-11px h-30px lh-[30px] mb-6px cursor-pointer"
            :class="{ active: item.schoolStudentId == curStudentId }"
            @click="changeStudent(item)"
          >
            {{ item.studentName }}
          </div>
          <g-empty v-if="!studentList.length"></g-empty>
        </div>
      </div>
      <div class="flex-1 ml-12px">
        <div class="flex justify-between mb-13px">
          <div class="text-[#6C6C74]">
            共<span class="text-[#6474FD] mx-4px">{{ questionList?.length }}</span>道题
          </div>
          <van-radio-group v-model="checked"
                           direction="horizontal"
                           @change="() => { $g.tool.renderMathjax() }"
          >
            <van-radio :name="2"
                       icon-size="13px"
                       class="mr-32px"
            >
              只看已{{ isActivity ? '重做' : '订正' }}
            </van-radio>
            <van-radio :name="1" icon-size="13px">
              只看未{{ isActivity ? '重做' : '订正' }}
            </van-radio>
          </van-radio-group>
        </div>
        <g-loading v-if="loading" class="h-200px"></g-loading>

        <div v-else>
          <QuestionStemItem
            v-for="(item, index) in questionList"
            :id="`revised-ques-item-${index}`"
            :key="item.questionId"
            :question-item="item"
            :index="index + 1"
          >
            <template #footer>
              <div class="flex justify-end mt-12px">
                <div class="flex items-center">
                  <div v-if="item.correctiveState === 1" class="w-77px h-30px flex-cc text-[#FF4646] bg-[#fdeaea] br-[4px]">
                    未{{ isActivity ? '重做' : '订正' }}
                  </div>
                  <div v-if="item.correctiveNum && item.correctiveState === 1" class="w-1px h-11px bg-[#DDDDDD] mx-16px"></div>
                </div>
                <!-- <span class="text-[#FF4646] ml-4px " style="letter-spacing: 2px;">{{ item.correctiveNum }}次</span> -->
                <div v-if="item.correctiveNum"
                     class="border border-[#6474FD] text-[#6474FD] w-158px h-30px flex-cc br-[4px] cursor-pointer"
                     @click="goDetail(item, index)"
                >
                  查看{{ isActivity ? '重做' : '订正' }}记录
                </div>
              </div>
            </template>
          </QuestionStemItem>
        </div>
        <g-empty v-if="!questionList.length && !loading"></g-empty>
      </div>
      <QuestionPanel :ques-list="questionList" @select="onQuesSelect"></QuestionPanel>
    </div>
    <g-empty v-else></g-empty>
  </div>
</template>

<style scoped lang="scss">
.active{
background: rgba(100,116,253,0.14) ;
border-radius: 4px;
font-weight: 600;
}

:deep(){
.van-radio__label{
color: #74788D;
}

.van-radio__icon--checked .van-icon{

  border-color: #6474FD;
}

}
</style>
