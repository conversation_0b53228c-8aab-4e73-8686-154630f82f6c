import type { studentDataObj } from './types'

// 根据字段去重数组
export function deduplicateByKey(arr, key) {
  const map = new Map()
  return arr.filter((item) => {
    if (!map.has(item[key])) {
      map.set(item[key], true)
      return true
    }
    return false
  })
}

// 组合学生和组的数组
export function combineStudentAndGroup(classList) {
  return classList.reduce((res, item) => {
    res = [
      ...res,
      ...(item.selectStudentArr || []),
      ...(item.selectGroupArr?.flatMap(v => v.list) || []),
    ]
    return res
  }, [])
}

export function studentListInit(data) {
  const studentData = {
    classList: [], // 自定义已选择班级列表，
    specialClass: null, // 非自定义班级
    selectStudentList: [], // 所有选择的学生总和，已去重
    disabledStudentIds: [], // 需要禁止操作的学生id集合
    disabledGroupIds: [], // 需要禁止操作的组id集合
  } as studentDataObj

  const classList = data.taskClassList
  // 分离组和班级
  let [groupList, reallyClassList] = classList.reduce(
    (acc, item) => {
      acc[item.classType === 3 ? 0 : 1].push(item)
      return acc
    },
    [[], []],
  )
  let belongsSchoolClassIdList = groupList
    .flatMap(item => item.belongsSchoolClassIdList)
    .map((v) => {
      return {
        ...v,
        arrangeObjectType: 2,
        schoolStudentList: [],
      }
    })
  reallyClassList = deduplicateByKey(
    [...reallyClassList, ...belongsSchoolClassIdList.filter(Boolean)],
    'schoolClassId',
  )
  for (const classItem of reallyClassList) {
    classItem.selectStudentArr = $g._.cloneDeep(classItem.schoolStudentList)
    // 处理组信息
    classItem.selectGroupArr = groupList
      .filter(group =>
        group.belongsSchoolClassIdList.find(
          v => v.schoolClassId == classItem.schoolClassId,
        ))
      .map((group) => {
        studentData.disabledGroupIds.push(group.schoolClassId)
        group.list = $g._.cloneDeep(group.schoolStudentList)
        return group
      })

    // 添加班级学生禁止操作id以及所有选择的学生总和
    for (const student of classItem.selectStudentArr)
      studentData.disabledStudentIds.push(student.schoolStudentId)

    studentData.selectStudentList = [...studentData.selectStudentList,
...classItem.selectStudentArr.map(h => h.schoolStudentId),
...classItem.selectGroupArr.flatMap(h => h.list.map(v => v.schoolStudentId))]
  }

  studentData.selectStudentList = [...new Set(studentData.selectStudentList)]
  studentData.disabledStudentIds = [...new Set(studentData.disabledStudentIds)]
  studentData.disabledGroupIds = [...new Set(studentData.disabledGroupIds)]
  studentData.classList = reallyClassList

  return studentData
}

/* 根据文件类型返回对应图片地址 */
export function getFileTypeUrl(ext) {
  let type = ext ? ext.toLowerCase() : null
  if (['doc', 'docx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/docx.png')

  if (['xls', 'xlsx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/excel.png')

  if (['pdf',
'ppt',
'pptx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/pdf.png')

  if (['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8'].includes(type))
    return $g.tool.getFileUrl('taskCenter/video.png')

  if (['jpg',
'jpeg',
'png',
'gif',
'bmp',
'svg'].includes(type))
    return $g.tool.getFileUrl('taskCenter/img.png')

  return $g.tool.getFileUrl('taskCenter/default-file.png')
}
