import chalk from 'chalk'

export function createPrintUrlsPlugin() {
  return {
    name: 'vite-plugin-extend-print-urls',
    configureServer(server) {
      const originalPrintUrls = server.printUrls
      if (typeof originalPrintUrls === 'function') {
        server.printUrls = function () {
          originalPrintUrls.call(this)
          // 使用正则表达式匹配IP地址并替换为域名
          const ipRegex = /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/
          console.log(
            `  ${chalk.green('➜')}  域名(修改hosts): ${chalk.cyan(this.resolvedUrls?.network[0].replace(ipRegex, 'vite-template-dev.qimingdaren.com'))}`,
          )
        }
      }
    },
  }
}
