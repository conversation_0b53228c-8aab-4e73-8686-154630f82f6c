{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "jsx": "preserve", "lib": ["ES2015", "DOM"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "/#/*": ["./types/*"]}, "types": ["node", "@vue-macros/reactivity-transform/macros-global"], "noImplicitAny": false}, "include": ["types/**/*", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"]}