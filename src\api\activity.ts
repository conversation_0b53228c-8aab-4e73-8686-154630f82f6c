import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config

// 获取活动列表
export function getActivityList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/list`, data)
}
// 活动科目列表
export function getActivitySubjectList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/themeSubject`, data)
}
// 学校列表
export function getSchoolList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/schoolList`, data, {
    delay: false,
  })
}
// 年级
export function getGradeList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/gradeList`, data, {
    delay: false,
  })
}
// 班级列表
export function getClassList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/classList`, data, {
    delay: false,
  })
}
// 统计-学生列表
export function getActivityStudentList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/student/listV2`, data, {
    delay: true,
  })
}
// 统计-学生模块学习进度统计
export function getActivityStudentModuleList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/student/module/list`, data)
}
// 学生数量统计
export function getActivityStudentCount(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/student/count`, data, {
    delay: false,
  })
}
// 统计-模块学习进度列表(只统计题目数据)
export function getStatisticsModule(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/module/question/list`, data, {
    delay: true,
  })
}
// 统计-资源学习进度统计列表(只统计题目数据)
export function getModuleQuestionList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/module/resource/question/list`, data, {
    delay: false,
  })
}
// 统计-学生模块学习进度统计V2
export function getActivityStudentModuleListV2(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/student/module/listV2`, data, {
    delay: false,
  })
}
// 测验完成情况-后台
export function getStudentList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/module/student/task/list`, data, {
    delay: true,
  })
}
/* 周考、月考统计 */
export function getExamStatistics(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/weekMonth/list`, data, {
    delay: true,
  })
}

/* 阶段测验-学生题目列表 */
export function getPeriodTestStudentQuestionList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/periodTest/studentQuestionList`, data, {
    delay: true,
  })
}
/* 阶段测试分页列表 */
export function getPeriodTestList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/periodTest/page`, data, {
    delay: true,
  })
}
/* 创建阶段测试 */
export function createPeriodTest(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/activity/periodTest/create`, data, {
    delay: false,
  })
}
/* 获取测验名称 */
export function getPeriodTestNum(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/periodTest/periodTestNumber`, data, {
    delay: false,
  })
}
/* 删除任务 */
export function deletePeriodTest(data) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/activity/periodTest/delete`, data, {
    delay: false,
  })
}
/* 禁用/启用 */
export function updateEnable(data) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/activity/periodTest/updateEnable`, data, {
    delay: false,
  })
}
/* 统计-异常答题数班级导出 */
export function exportClassAbnormalQuestion(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/classAbnormalQuestion/export`, data, {
    delay: false,
  })
}
/* 统计-异常答题数个人导出 */
export function exportStudentActivityAbnormal(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/studentActivityAbnormal/export`, data, {
    delay: false,
  })
}
/* 统计-测验类型列表 */
export function getExerciseTaskType(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/exerciseTaskType/list`, data, {
    delay: false,
  })
}
/* 统计-学生模块学习进度统计 汇总统计 */
export function getActivityStudentModuleListSummary(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/student/module/summary`, data, {
    delay: false,
  })
}
/* 统计-订正报告 */
export function getCorrectionReport(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/amend/list`, data, {
    delay: false,
  })
}
/* 订正报告详情 */
export function getCorrectionReportDetail(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/amend/detail`, data, {
    delay: false,
  })
}
/* 订正流水 */
export function getCorrectionFlow(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/activity/statistics/amend/record`, data)
}
