<script setup lang="ts" name="SyncClassIndex">
import {
  delSyncClassResource,
  getChangeList,
  getKnowledgeExplainList,
  getKnowledgePracticeList,
  getKnowledgeResource,
  getSyncClassBookList,
  getSyncClassChapterList,
  getSyncClassGrade,
  getSyncClassList,
  getSyncClassSubjectList,
  getVideoResource,
  saveTreeDataSort,
  setSyncClassResourceSort,
  syncClassResourceToOther,
} from '@/api/syncClass'
import Draggable from 'vuedraggable'
import NestedDraggable from './components/NestedDraggable.vue'
import Preview from './courseLearning/components/Preview.vue'

let scrollTop = 0
const route = useRoute()
const router = useRouter()

let draggingBoxIndex = $ref(-1)
let btnDisabled = $ref(false)

let leftLoading = $ref(true)
let rightLoading = $ref(true)
let currentFile = $ref<any>(null)
let showFile = $ref(false)
let showTitle = $ref(false)
let articleInfo = $ref<any>({})
let previewType = $ref<any>(1)

let tempData: any = null
let tempRow: any = null
let showMenu = $ref(false)
let showAddTrain = $ref(false)
let trainName = $ref('')

let layerList = $ref<any[]>([])

let showSync = $ref(false)
let syncClassListSelected = $ref<any[]>([])
const formData = $ref({
  items: {
    subject: {
      label: '学科',
      options: [] as any[],
    },
    grade: {
      label: '年级',
      options: [] as any[],
    },
    className: {
      label: '班级',
      options: [] as any[],
    },
  },
  data: {
    subject: null,
    grade: null,
    className: null,
  } as any,
})

const dropdown = $ref<any>(null)
let currentBookId = $ref<any>(null)
let textbookList = $ref<any[]>([])

let currentChapter = $ref<any>(null)

let treeData = $ref<any[]>([])

let studyList = $ref<any[]>([]) // 知识精讲 && 实战演练

const currentSubject = $computed(() => {
  return formData.items.subject.options.find(item => item.value === formData.data.subject) || null
})

const currentGrade = $computed(() => {
  return formData.items.grade.options.find(item => item.value === formData.data.grade) || null
})

const currentClass = $computed(() => {
  return formData.items.className.options.find(item => item.value === formData.data.className) || null
})

const currentBookData = $computed(() => {
  return textbookList.find(item => item.value === currentBookId) || null
})

const defaultExpandedKey = $computed(() => {
  return currentChapter?.bookCatalogId || null
})

const syncClassList = $computed(() => {
  return formData.items.className.options.filter(v => v.value !== formData.data.className)
})

function nodeClick(data) {
  if (data.bookCatalogId !== currentChapter?.bookCatalogId && !data.children?.length) {
    currentChapter = data
    getSyncClassChapterContentApi()
    getChangeListApi()
  }
}

async function getChangeListApi() {
  try {
    if (!currentChapter) return
    const data = await getChangeList({
      bookCatalogId: currentChapter.bookCatalogId,
    })
    layerList = data.slice(0, 5)
  }
  catch (e) {
    console.error(e)
  }
}

function onselect(id: number) {
  if (currentBookId !== id) {
    currentBookId = id
    getSyncClassChapterListApi()
  }
}

function onDragStart() {
  dropdown?.handleClose()
}

async function preview(item, element) {
  tempData = item
  tempRow = element
  if (element.bookCatalogResourceType === 1) {
    router.push({
      name: 'AfterTraining',
      query: {
        bookCatalogId: element.bookCatalogId,
        isPreview: 1,
        bookCatalogName: `${currentChapter?.bookCatalogName || ''} · 实战演练 · ${element.resourceName || ''}`,
        bookCatalogClassResourceId: element.bookCatalogClassResourceId,
        isDefault: element.isDefault,
        schoolClassId: formData.data.className,
      },
    })
  }
  else if (element.bookCatalogResourceType === 2) {
    const res = await getKnowledgeResource({
      bookCatalogArticleId: element.bookCatalogArticleId,
    })
    articleInfo = res
    if (res?.bookCatalogArticleFormatType == 3)
      articleInfo.content = JSON.parse(articleInfo.content)

    previewType = 2
    showTitle = false
    showFile = true
  }
  else if (element.bookCatalogResourceType === 3) {
    const res = await getVideoResource({
      videoResourceId: element.videoResourceId,
    })
    const obj = {
      fileAbsoluteUrl: res,
      fileAbsoluteUrlM3u8: res,
      fileExtension: res.split('.').pop(),
      fileName: element.resourceName,
      videoCoverUrl: `${res}?x-oss-process=video/snapshot,t_0,f_jpg,m_fast`,
    }
    previewType = 3
    currentFile = obj
    showTitle = true
    showFile = true
  }
}

function handleLayerPreview(item) {
  router.push({
    name: 'AfterTraining',
    query: {
      bookCatalogId: currentChapter.bookCatalogId,
      bookCatalogName: `${currentChapter?.bookCatalogName || ''} · ${item.layeredName || ''}`,
      isLamination: 1,
      bookId: item.bookId,
    },
  })
}

function edit(item, element) {
  if (element.bookCatalogResourceType === 1) {
    router.push({
      name: 'AfterTraining',
      query: {
        bookCatalogId: element.bookCatalogId,
        bookCatalogName: `${currentChapter?.bookCatalogName || ''} · 实战演练 · ${element.resourceName || ''}`,
        bookCatalogClassResourceId: element.bookCatalogClassResourceId,
        schoolClassId: formData.data.className,
        isDefault: element.isDefault,
      },
    })
  }
  else {
    let content = element.bookCatalogResourceType == 3 ? '更换课程学习' : '更换知识卡片'
    router.push({
      name: 'CourseLearning',
      query: {
        title: `您正在给知识精讲${content}`,
        bookCatalogId: item.bookCatalogId,
        schoolClassId: formData.data.className,
        bookId: item.bookId,
        type: element.bookCatalogResourceType,
      },
    })
  }
}

function del(element) {
  $g.showConfirmDialog({
    title: '确定删除该资源？',
    message: '删除后学生端将不展示该资源',
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    confirmButtonColor: '#FF4646',
    zIndex: 100,
  }).then(async () => {
    await delSyncClassResource({
      schoolClassId: formData.data.className,
      bookCatalogClassResourceId: element.bookCatalogClassResourceId,
      bookId: element.bookId,
      bookCatalogId: element.bookCatalogId,
      bookCatalogResourceType: element.bookCatalogResourceType,
      bookCatalogResourceFromId: element.bookCatalogResourceFromId,
    })
    $g.showToast('删除成功')
    getSyncClassChapterContentApi(false)
  }).catch((err) => {
      console.error(err)
    })
}

function add(item) {
  tempData = item
  if (item.title === '知识精讲') {
    showMenu = true
  }
  else {
    trainName = ''
    showAddTrain = true
  }
}

function toErrorBook() {
  console.log('错题本')
}

function confirmAdd(type) {
  if (type === 1) {
    if (trainName.trim() === '') {
      $g.showToast('请输入训练名称')
    }
    else {
      router.push({
        name: 'AfterTraining',
        query: {
          bookCatalogId: tempData.bookCatalogId,
          isAdd: 1,
          bookCatalogName: `${currentChapter?.bookCatalogName || ''} · 实战演练 · ${tempData.resourceName || ''}`,
          schoolClassId: formData.data.className,
          trainName: trainName.trim(),
        },
      })
      showAddTrain = false
    }
  }
  else {
    let content = type == 3 ? '新增课程学习' : '关联知识卡片'
    router.push({
      name: 'CourseLearning',
      query: {
        title: `您正在给知识精讲${content}`,
        bookCatalogId: tempData.bookCatalogId,
        schoolClassId: formData.data.className,
        bookId: tempData.bookId,
        type,
      },
    })
    showMenu = false
  }
}

function onDragEndLeft(e) {
  if (e.newIndex !== e.oldIndex)
    leftDrop()
}

async function leftDrop() {
  try {
    btnDisabled = true
    await nextTick()
    $g.tool.renderMathjax()
    await saveTreeDataSort({
      schoolClassId: formData.data.className,
      bookId: currentBookId,
      tree: treeData,
    })
    $g.showToast('保存成功')
    btnDisabled = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    $g.showToast('保存失败')
    btnDisabled = false
    console.error(e)
  }
}

async function saveSync() {
  try {
    if (syncClassListSelected.length === 0) {
      $g.showToast('请选择同步的班级')
      return
    }
    showSync = false
    btnDisabled = true
    await syncClassResourceToOther({
      bookId: currentBookId,
      currentSchoolClassId: formData.data.className,
      originSchoolClassIds: syncClassListSelected,
    })
    $g.showToast('同步成功')
    btnDisabled = false
  }
  catch (e) {
    $g.showToast('同步失败')
    btnDisabled = false
    console.error(e)
  }
}

async function getSyncClassSubjectListApi() {
  try {
    const data = await getSyncClassSubjectList()
    if (!data?.length) throw new Error('没有数据')
    formData.items.subject.options = data?.map(item => ({
      label: item.sysSubjectName,
      value: item.sysSubjectId,
    })) || []
    formData.data.subject = data[0]?.sysSubjectId || null
    getSyncClassGradeApi()
  }
  catch (e) {
    formData.items.subject.options = []
    formData.data.subject = null
    formData.items.grade.options = []
    formData.data.grade = null
    formData.items.className.options = []
    formData.data.className = null
    textbookList = []
    currentBookId = null
    treeData = []
    currentChapter = null
    leftLoading = false
    rightLoading = false
    studyList = []
    layerList = []
    console.error(e)
  }
}

async function getSyncClassGradeApi() {
  try {
    const data = await getSyncClassGrade({
      sysSubjectId: formData.data.subject,
    })
    if (!data?.length) throw new Error('没有数据')
    formData.items.grade.options = data?.map(item => ({
      label: item.sysGradeName,
      value: item.sysGradeId,
      stageId: item.sysStageId,
    })) || []
    formData.data.grade = data[0]?.sysGradeId || null
    await getSyncClassBookListApi()
    getSyncClassListApi()
  }
  catch (e) {
    formData.items.grade.options = []
    formData.data.grade = null
    formData.items.className.options = []
    formData.data.className = null
    textbookList = []
    currentBookId = null
    treeData = []
    currentChapter = null
    leftLoading = false
    rightLoading = false
    studyList = []
    layerList = []
    console.error(e)
  }
}

async function getSyncClassListApi() {
  try {
    const data = await getSyncClassList({
      sysSubjectId: formData.data.subject,
      sysGradeId: formData.data.grade,
    })
    if (!data?.length) throw new Error('没有数据')
    formData.items.className.options = data?.map(item => ({
      label: item.className,
      value: item.schoolClassId,
    })) || []
    formData.data.className = data[0]?.schoolClassId || null
    getSyncClassChapterListApi()
  }
  catch (e) {
    formData.items.className.options = []
    formData.data.className = null
    textbookList = []
    currentBookId = null
    treeData = []
    currentChapter = null
    leftLoading = false
    rightLoading = false
    studyList = []
    layerList = []
    console.error(e)
  }
}

async function getSyncClassBookListApi() {
  try {
    const { list } = await getSyncClassBookList({
      sysGradeId: currentGrade?.value,
      sysSubjectId: currentSubject?.value,
      sysStageId: currentGrade?.stageId,
      page: 1,
      pageSize: 9999,
    })
    if (!list?.length) throw new Error('没有数据')
    textbookList = list.map(item => ({
      label: item.bookName,
      value: item.bookId, // 教材id
    }))
    currentBookId = list[0]?.bookId || null
  }
  catch (e) {
    textbookList = []
    currentBookId = null
    treeData = []
    currentChapter = null
    leftLoading = false
    rightLoading = false
    studyList = []
    layerList = []
    console.error(e)
  }
}

async function getSyncClassChapterListApi() {
  try {
    if (!formData.data.className || !currentBookId)
      return

    leftLoading = true
    rightLoading = true
    layerList = []
    const data = await getSyncClassChapterList({
      bookId: currentBookId,
      schoolClassId: formData.data.className,
    })
    if (!data?.length) throw new Error('没有数据')
    treeData = data || []
    getCurrentChapter(treeData)
    leftLoading = false
    await nextTick()
    $g.tool.renderMathjax()
    getSyncClassChapterContentApi()
    getChangeListApi()
  }
  catch (e) {
    treeData = []
    currentChapter = null
    leftLoading = false
    rightLoading = false
    studyList = []
    layerList = []
    console.error(e)
  }
}

function getCurrentChapter(list) {
  return list.some((v) => {
    if (!v.children?.length) {
      currentChapter = v
      return true
    }
    else if (v.children?.length) {
      return getCurrentChapter(v.children)
    }
  })
}

async function getSyncClassChapterContentApi(isLoading = true) {
  try {
    if (!formData.data.className || !currentChapter?.bookCatalogId) return
    if (isLoading)
      rightLoading = true

    const res: any = []
    const res1 = await getKnowledgeExplainList({
      schoolClassId: formData.data.className,
      bookCatalogId: currentChapter?.bookCatalogId,
    })
    if (res1) {
      res.push({
        ...res1,
        resourceList: res1.resourceList || [],
        title: '知识精讲',
      })
    }
    const res2 = await getKnowledgePracticeList({
      schoolClassId: formData.data.className,
      bookCatalogId: currentChapter?.bookCatalogId,
    })
    if (res2) {
      res.push({
        ...res2,
        resourceList: res2.resourceList || [],
        title: '实战演练',
      })
    }
    studyList = res
    rightLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    rightLoading = false
    studyList = []
    layerList = []
    console.error(e)
  }
}

function getTitle(element) {
  const prefix = element.bookCatalogResourceType === 3 ? '课程学习 · ' : element.bookCatalogResourceType === 2 ? '知识卡片 · ' : ''
  return `${prefix}${element.resourceName || ''} ${element.bookCatalogResourceType === 3 ? `(${secondsToMinutes(element)}分钟)` : ''}`
}

/* 秒钟转换为分钟，向上取整 */
function secondsToMinutes(item) {
  return Math.ceil(item?.resourceLearn?.fileDuration / 60) || 0
}

function syncData() {
  if (formData.items.className.options.length < 2)
    return

  syncClassListSelected = []
  showSync = true
}

function onSortStart(index) {
  draggingBoxIndex = index
}

async function onSortEnd(e, item) {
  try {
    if (e.newIndex === e.oldIndex) {
      draggingBoxIndex = -1
      return
    }
    await nextTick()
    $g.tool.renderMathjax()
    await setSyncClassResourceSort({
      schoolClassId: formData.data.className,
      resourceList: item?.resourceList?.map(v => ({
        bookCatalogClassResourceId: v.bookCatalogClassResourceId,
        bookId: v.bookId,
        bookCatalogId: v.bookCatalogId,
        bookCatalogResourceType: v.bookCatalogResourceType,
        bookCatalogResourceFromId: v.bookCatalogResourceFromId,
      })) || [],
    })
    draggingBoxIndex = -1
    $g.showToast('操作成功')
    getSyncClassChapterContentApi(false)
  }
  catch (e) {
    draggingBoxIndex = -1
    $g.showToast('操作失败')
    console.error(e)
  }
}

function selectClassList(value) {
  const index = syncClassListSelected.findIndex(item => item === value)
  if (index === -1)
    syncClassListSelected.push(value)

  else
    syncClassListSelected.splice(index, 1)
}

async function handleChange(key) {
  if (key === 'subject') {
    getSyncClassGradeApi()
  }
  else if (key === 'grade') {
    await getSyncClassBookListApi()
    getSyncClassListApi()
  }
  else if (key === 'className') {
    getSyncClassChapterListApi()
  }
}

onMounted(() => {
  getSyncClassSubjectListApi()
})

watch(
  () => showFile,
  (val) => {
    if (!val) {
      setTimeout(() => {
        currentFile = {}
        articleInfo = {}
      }, 350)
    }
  },
)

watch(() => route.name, async (val) => {
  if (val === 'SyncClassIndex') {
    await getSyncClassChapterContentApi()
    nextTick(() => {
      const dom = document.querySelector('.sync-scrollbar > .el-scrollbar__wrap')
      if (dom)
        dom.scrollTo(0, scrollTop)
    })
  }
  else {
    scrollTop = document.querySelector('.sync-scrollbar > .el-scrollbar__wrap')?.scrollTop || 0
  }
})
</script>

<template>
  <div v-loading="btnDisabled"
       class="h-screen bg-[#F3F4F9] py-21px px-26px tracking-tight"
       style="width: 100vw;"
  >
    <div
      class="w-full h-full bg-[#FFFFFF66] border border-solid border-[#fff] rounded-[17px] backdrop:blur-[18px] p-21px"
    >
      <div class="h-43px flex items-center mb-21px">
        <div
          class="flex-shrink-0 w-34px h-34px rounded-[34px] overflow-hidden border border-solid border-[#EEEEEE] mr-27px hover:opacity-80"
        >
          <g-navbar>
          </g-navbar>
        </div>
        <div class="flex items-center text-13px text-[#333] flex-grow">
          <div
            v-for="(item, key, index) in formData.items"
            :key="key"
            class="flex items-center"
            :class="{
              'ml-26px': index > 0,
            }"
          >
            <div class="font-600 mr-13px whitespace-nowrap">
              {{ item.label }}
            </div>
            <el-select
              v-model="formData.data[key]"
              :placeholder="`请选择${item.label}`"
              class="w-148px sync-select"
              @change="handleChange(key)"
            >
              <el-option
                v-for="option in item.options"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-grow flex items-center justify-end">
            <div
              class="text-13px text-[#6474FD] select-none"
              :class="{
                'grayscale-[100%] cursor-not-allowed': formData.items.className.options.length < 2,
                'van-haptics-feedback hover:opacity-80': formData.items.className.options.length > 1,
              }"
              @click="syncData"
            >
              数据同步
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center h-30px text-13px text-[#00000080] mb-17px select-none">
        <div class="mr-11px">
          当前操作生效学科为
        </div>
        <div
          v-if="currentSubject"
          class="h-30px px-13px leading-[30px] bg-white border border-solid border-[#DADDE8] rounded-[6px] text-13px text-[#333]"
        >
          {{ currentSubject?.label }}
        </div>
        <template v-else>
          -
        </template>
        <div class="ml-34px mr-11px">
          生效范围
        </div>
        <template v-if="currentGrade && currentClass">
          <div
            class="h-30px px-11px leading-[30px] bg-white border border-solid border-[#DADDE8] rounded-[6px] text-13px text-[#333] relative"
          >
            {{ currentGrade.label + currentClass.label }}
          </div>
        </template>
        <template v-else>
          -
        </template>
      </div>
      <div class="h-17px leading-[17px] mb-17px text-[#00000080] whitespace-nowrap text-13px">
        支持拖拽进行排序，学生端与当前排序一致
      </div>
      <div class="box-h flex w-full">
        <div class="flex-shrink-0 w-[379px] h-full rounded-[13px] bg-white mr-18px">
          <div
            class="w-full h-46px px-17px leading-[46px] border-b border-solid border-[#EEEEEE] text-15px text-[#333] font-600"
          >
            知识目录
          </div>
          <div class="px-17px mt-13px mb-6px h-19px">
            <el-dropdown
              ref="dropdown"
              placement="bottom-start"
              class="sync-dropdown"
              trigger="click"
              :disabled="!textbookList.length"
              @command="onselect"
            >
              <div class="flex items-center text-[#999]">
                <div class="max-w-200px truncate mr-4px text-13px select-none">
                  {{ currentBookData?.label || '请选择教材' }}
                </div>
                <img src="@/assets/img/syncClass/arrow-gray.png"
                     alt="arrow"
                     class="w-11px h-11px"
                >
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="item in textbookList"
                                    :key="item.value"
                                    :command="item.value"
                  >
                    <div
                      :style="{
                        color: item.value === currentBookId ? '#6474FD' : '#333',
                      }"
                    >
                      {{ item.label }}
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="box-tree-h px-13px">
            <el-scrollbar>
              <g-loading v-if="leftLoading" class="h-200px"></g-loading>
              <template v-else>
                <g-empty v-if="!treeData.length"></g-empty>
                <NestedDraggable
                  v-else
                  v-model="treeData"
                  :current-key="defaultExpandedKey"
                  @dragstart="onDragStart"
                  @dragend="onDragEndLeft"
                  @item-click="nodeClick"
                />
              </template>
            </el-scrollbar>
          </div>
        </div>
        <div class="right-w h-full">
          <el-scrollbar class="sync-scrollbar">
            <g-loading v-if="rightLoading" class="h-200px"></g-loading>
            <template v-else>
              <template v-if="studyList.length">
                <div
                  v-for="(item, index) in studyList"
                  :key="item.bookCatalogId"
                  class="rounded-[13px] bg-[#F0F4FA91] border border-solid border-[#FFFFFF] p-13px pt-17px"
                  :class="{
                    'mt-17px': index > 0,
                  }"
                >
                  <div class="w-full px-9px h-26px leading-[26px] text-15px text-[#333] font-600 mb-13px truncate">
                    {{ item.title }}
                  </div>
                  <div
                    :class="{
                      'dragging-box-mask': draggingBoxIndex !== -1 && draggingBoxIndex !== index,
                    }"
                  >
                    <Draggable
                      v-model:list="item.resourceList"
                      ghost-class="opacity-25"
                      handle=".handle"
                      :animation="150"
                      @start="onSortStart(index)"
                      @end="(e) => onSortEnd(e, item)"
                    >
                      <template #item="{ element }">
                        <div
                          class="w-full h-51px bg-white rounded-[11px] flex items-center px-13px mb-13px select-none"
                        >
                          <div
                            class="w-19px h-19px mr-9px"
                            :class="{
                              'icon-book': element.bookCatalogResourceType === 2,
                              'icon-play': element.bookCatalogResourceType === 3,
                              'icon-roll': element.bookCatalogResourceType === 1,
                            }"
                          >
                          </div>
                          <div
                            class="text-15px text-[#333] font-600 leading-[20px] mr-15px"
                            :class="{
                              'title-w': item.title === '知识精讲',
                              'title-w-ii': item.title === '实战演练',
                            }"
                          >
                            <g-mathjax :text="getTitle(element)" class="title-row-math truncate" />
                          </div>
                          <div v-if="item.title === '实战演练'" class="w-50px whitespace-nowrap font-600 text-14px flex-grow">
                            ({{ element.questionCount || 0 }}题)
                          </div>

                          <div
                            class="w-62px h-30px flex items-center justify-center rounded-[6px] border border-solid border-[#6474FD] mr-9px select-none van-haptics-feedback hover:opacity-80"
                            @click="preview(item, element)"
                          >
                            <img src="@/assets/img/syncClass/eye.png"
                                 alt="eye"
                                 class="w-15px h-15px mr-4px"
                            >
                            <div class="text-13px text-[#6474FD] font-600 pt-2px">
                              预览
                            </div>
                          </div>
                          <div
                            class="w-62px h-30px flex items-center justify-center rounded-[6px] border border-solid border-[#6474FD] mr-9px select-none van-haptics-feedback hover:opacity-80"
                            @click="edit(item, element)"
                          >
                            <img src="@/assets/img/syncClass/pen.png"
                                 alt="pen"
                                 class="w-15px h-15px mr-4px"
                            >
                            <div class="text-13px text-[#6474FD] font-600 pt-2px">
                              修改
                            </div>
                          </div>
                          <div
                            class="w-62px h-30px flex items-center justify-center rounded-[6px] border border-solid border-[#FF4646] mr-13px select-none van-haptics-feedback hover:opacity-80"
                            @click="del(element)"
                          >
                            <img src="@/assets/img/syncClass/del.png"
                                 alt="del"
                                 class="w-15px h-15px mr-4px"
                            >
                            <div class="text-13px text-[#FF4646] font-600 pt-2px">
                              删除
                            </div>
                          </div>
                          <div class="menu-icon w-15px h-15px cursor-move handle">
                          </div>
                        </div>
                      </template>
                    </Draggable>
                  </div>
                  <div
                    class="w-full h-51px bg-white rounded-[11px] flex items-center justify-center van-haptics-feedback select-none hover:opacity-80"
                    @click="add(item)"
                  >
                    <img src="@/assets/img/syncClass/add.png"
                         alt="add"
                         class="w-19px h-19px mr-9px"
                    >
                    <div class="text-15px text-[#6474FD] font-600 leading-[20px]">
                      新增
                    </div>
                  </div>
                </div>
              </template>
              <g-empty v-else></g-empty>
            </template>
            <div class="h-19px leading-[19px] text-15px text-[#333] font-600 mb-11px mt-26px">
              学习工具
            </div>
            <div class="w-full overflow-hidden">
              <div class="flex items-center flex-wrap box-w">
                <div
                  class="w-168px h-83px bg-white rounded-[11px] flex items-center justify-center select-none mr-13px mb-13px"
                  @click="toErrorBook"
                >
                  <div class="text-15px text-[#333] font-600 leading-[20px]">
                    错题本
                  </div>
                  <img src="@/assets/img/syncClass/tick.png"
                       alt="tick"
                       class="w-20px h-23px ml-8px"
                  >
                </div>
                <div
                  v-for="item in layerList"
                  :key="item.bookId"
                  class="w-168px h-83px bg-white rounded-[11px] flex items-center justify-center select-none mr-13px mb-13px van-haptics-feedback hover:brightness-110"
                  @click="handleLayerPreview(item)"
                >
                  <div class="text-15px text-[#333] font-600 leading-[20px]">
                    {{ item.layeredName }}({{ item.questionIdList?.length || 0 }}题)
                  </div>
                  <img src="@/assets/img/syncClass/tick.png"
                       alt="tick"
                       class="w-20px h-23px ml-8px"
                  >
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <van-popup v-model:show="showMenu" class="p-21px rounded-[6px] w-300px">
      <div class="w-full flex items-center justify-between mb-20px">
        <div class="text-17px text-[#333] font-600">
          请选择要新增的学习内容
        </div>
        <img
          src="@/assets/img/taskCenter/close.png"
          alt="close"
          class="w-17px h-17px select-none van-haptics-feedback"
          @click="showMenu = false"
        >
      </div>
      <div
        class="w-full h-38px text-center leading-[38px] rounded-[6px] bg-[#F9F9F9] select-none van-haptics-feedback border border-solid border-[#F9F9F9] hover:!border-[#6474FD] hover:!text-[#6474FD]"
        @click="confirmAdd(2)"
      >
        知识卡片
      </div>
      <div
        class="w-full h-38px text-center leading-[38px] rounded-[6px] bg-[#F9F9F9] select-none van-haptics-feedback mt-10px border border-solid border-[#F9F9F9] hover:!border-[#6474FD] hover:!text-[#6474FD]"
        @click="confirmAdd(3)"
      >
        课程学习
      </div>
    </van-popup>
    <van-popup v-model:show="showAddTrain" class="p-21px pb-26px rounded-[6px] w-395px">
      <div class="w-full h-24px text-center mb-38px relative">
        <div class="text-17px text-[#333] font-600">
          课后训练名称
        </div>
        <img
          src="@/assets/img/taskCenter/close.png"
          alt="close"
          class="w-17px h-17px select-none van-haptics-feedback absolute right-0 top-0"
          @click="showAddTrain = false"
        >
      </div>
      <div class="flex items-center px-9px mb-38px">
        <div class="text-15px text-[#F2494A] mr-6px translate-y-4px">
          *
        </div>
        <el-input
          v-model="trainName"
          placeholder="请输入课后训练名称"
          class="flex-grow h-34px"
          :maxlength="50"
        />
      </div>
      <div class="w-full flex items-center justify-center">
        <div class="w-134px h-30px text-15px text-[#fff] font-600 leading-[30px] text-center bg-[#6474FD] rounded-[4px] select-none van-haptics-feedback hover:brightness-110" @click="confirmAdd(1)">
          确定，去选题
        </div>
      </div>
    </van-popup>
    <van-popup v-model:show="showSync" class="p-21px rounded-[6px] w-300px">
      <div class="w-full flex items-center justify-between mb-20px">
        <div class="text-17px text-[#333] font-600">
          请选择要同步的班级
        </div>
        <img
          src="@/assets/img/taskCenter/close.png"
          alt="close"
          class="w-17px h-17px select-none van-haptics-feedback"
          @click="showSync = false"
        >
      </div>
      <div
        v-for="item in syncClassList"
        :key="item.value"
        class="w-full h-38px text-center leading-[38px] rounded-[6px] bg-[#F9F9F9] select-none van-haptics-feedback border border-solid border-[#F9F9F9] hover:opacity-80"
        :class="{
          '!border-[#6474FD] !text-[#6474FD]': syncClassListSelected.includes(item.value),
        }"
        @click="selectClassList(item.value)"
      >
        {{ (currentGrade?.label || '') + item.label }}
      </div>
      <div class="w-full flex items-center justify-center my-20px">
        <div
          class="w-120px h-38px text-15px text-[#fff] font-600 leading-[38px] text-center bg-[#6474FD] rounded-[9px] select-none van-haptics-feedback hover:opacity-80"
          @click="saveSync"
        >
          确定
        </div>
      </div>
      <div class="h-17px leading-[17px] text-center text-[#00000080] whitespace-nowrap text-13px">
        点击确定后选中将按照当前页面展示
      </div>
    </van-popup>
    <Preview
      v-model:show="showFile"
      :current-file="currentFile"
      :show-edit="true"
      :show-close="false"
      :show-title="showTitle"
      :type="previewType"
      :article-info="articleInfo"
      @edit="() => edit(tempData, tempRow)"
    />
  </div>
</template>

<style lang="scss" scoped>
.sync-select {
  --el-border-color: #fff;
  --el-border-radius-base: 9px;

  :deep() {
    .el-select__wrapper {
      min-height: 43px;
    }
  }
}

.box-h {
  height: calc(100% - 43px - 21px - 30px - 17px - 17px - 17px);
}

.menu-icon {
  background: url(@/assets/img/syncClass/menu.png) center / contain no-repeat;
}

.box-tree-h {
  height: calc(100% - 46px - 13px - 19px - 6px);

  :deep() {
    .el-tree-node__content {
      position: relative;
      border-radius: 9px;
      --el-tree-node-content-height: 43px;

      &:hover {
        background: none;
      }
    }

    .el-tree-node {
      &:focus {
        &>.el-tree-node__content {
          background: transparent;
        }
      }
    }

    .tree-item-active {
      &>.el-tree-node__content {
        background: #6474FD10 !important;
      }
    }

    .el-tree-node__expand-icon {
      top: -1px;

      &>svg {
        opacity: 0;
      }

      &::before {
        display: inline-block;
        content: '';
        width: 11px;
        height: 11px;
        left: 50%;
        top: 50%;
        position: absolute;
        transform: translate(-50%, -50%);
        background: url(@/assets/img/syncClass/arrow-gray-light.png) center / contain no-repeat;
      }
    }

    .element-tree-node-label-wrapper {
      display: inline-block;
      width: 100%;
    }
  }
}

.sync-dropdown {
  outline: none !important;

  * {
    outline: none !important;
  }
}

.icon-book {
  background: url(@/assets/img/syncClass/book.png) center / contain no-repeat;
}

.icon-play {
  background: url(@/assets/img/syncClass/play.png) center / contain no-repeat;
}

.icon-roll {
  background: url(@/assets/img/syncClass/roll.png) center / contain no-repeat;
}

.right-w {
  width: calc(100% - 379px - 18px);
}

.title-w {
  max-width: calc(100% - 19px - 9px - (62px * 3) - (9px * 3) - 13px - 5px - 15px);
}

.title-w-ii {
  max-width: calc(100% - 19px - 9px - (62px * 3) - (9px * 3) - 13px - 5px - 15px - 50px);
}

.dragging-box-mask {
  position: relative;

  &::after {
    position: absolute;
    display: inline-block;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    content: '';
  }
}

.box-w{
  width: calc(100% + 20px);
}
</style>
