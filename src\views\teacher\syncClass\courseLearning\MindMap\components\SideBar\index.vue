<script setup lang="ts">
import FormulaSidebar from './FormulaSidebar.vue'
import ShortcutKeys from './ShortcutKeys.vue'
import Structure from './Structure.vue'
import Theme from './Theme.vue'

const props = defineProps({
  mindMap: {
    type: Object as any,
  },
})
let show = $ref(true)
let activeSidebar = $ref<any>('')
let showStructure = $ref(false)
let showDrawer = $ref(false)
let showTheme = $ref(false)
let showFormulaSidebar = $ref(false)
let sideBarList = [
  {
    label: '主题',
    value: 'theme',
    icon: 'ri-t-shirt-line',
  },
  {
    label: '结构',
    value: 'structure',
    icon: 'ri-node-tree',
  },
  {
    label: '快捷键',
    value: 'shortcutKeys',
    icon: 'ri-keyboard-line',
  },
]

/* 点击菜单 */
function toggle(val, isClose = false) {
  if (isClose && val == 'FormulaSidebar' && activeSidebar == 'FormulaSidebar') {
    showFormulaSidebar = false
    activeSidebar = ''
    return
  }
  if (isClose) return
  if (activeSidebar == val) return
  activeSidebar = val

  if (val == 'structure') {
    showDrawer = false
    showTheme = false
    showStructure = true
    showFormulaSidebar = false
  }
  else if (val == 'shortcutKeys') {
    showStructure = false
    showTheme = false
    showDrawer = true
    showFormulaSidebar = false
  }
  else if (val == 'theme') {
    showStructure = false
    showDrawer = false
    showTheme = true
    showFormulaSidebar = false
  }
  else if (val == 'FormulaSidebar') {
    showStructure = false
    showDrawer = false
    showTheme = false
    showFormulaSidebar = true
  }
}
/* 重置选中菜单 */
function resetSidebar() {
  activeSidebar = ''
}
/* 切换主题 */
function changeTheme(theme) {
  props.mindMap.setThemeConfig({}, true)
  props.mindMap.setTheme(theme)
}
/* 切换结构 */
function changeStructure(val) {
  props.mindMap.setLayout(val)
}
onMounted(() => {
  $g.bus.on('mind_map_close_sidebar', handleCloseSidebar)
  $g.bus.on('mind_map_click_bar', toggle)
})
onUnmounted(() => {
  $g.bus.off('mind_map_close_sidebar', handleCloseSidebar)
  $g.bus.off('mind_map_click_bar', toggle)
})
function handleCloseSidebar() {
  show = false
  resetSidebar()
}
</script>

<template>
  <div>
    <div
      class="sidebarTriggerContainer"
      :class="{
        hasActive:
          show
          && activeSidebar
          && (showDrawer || showStructure || showTheme || showFormulaSidebar),
        show,
      }"
      @click.stop
    >
      <!-- 滑块 -->
      <div class="toggleShowBtn"
           :class="{ hide: !show }"
           @click="show = !show"
      >
        <span class="iconfont iconjiantouyou"></span>
      </div>
      <!-- 菜单 -->
      <div class="trigger" :class="{ '': !show }">
        <div
          v-for="item in sideBarList"
          :key="item.value"
          class="triggerItem"
          :class="{ active: activeSidebar === item.value }"
          @click="toggle(item.value)"
        >
          <g-icon :name="item.icon"
                  size=""
                  color=""
          />
          <div class="triggerName">
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <!-- 快捷键 -->
    <ShortcutKeys v-model="showDrawer" @reset-sidebar="resetSidebar" />
    <Structure
      v-model="showStructure"
      :mind-map="mindMap"
      @reset-sidebar="resetSidebar"
      @change-structure="changeStructure"
    />
    <Theme
      v-model="showTheme"
      :mind-map="mindMap"
      @reset-sidebar="resetSidebar"
      @change-theme="changeTheme"
    />
    <FormulaSidebar
      v-model="showFormulaSidebar"
      :mind-map="mindMap"
      @reset-sidebar="resetSidebar"
    ></FormulaSidebar>
  </div>
</template>

<style lang="scss" scoped>
.sidebarTriggerContainer {
  position: absolute;
  right: -65px;
  margin-top: 110px;
  transition: all 0.3s;
  top: 50%;
  z-index: 1;
  transform: translateY(-50%);

  &.show {
    right: 0;
  }
  &.hasActive {
    right: 305px;
  }
  .toggleShowBtn {
    position: absolute;
    left: -6px;
    width: 30px;
    height: 60px;
    background: #409eff;
    top: 50%;
    z-index: 1;
    transform: translateY(-50%);
    cursor: pointer;
    transition: left 0.1s linear;
    z-index: 0;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    display: flex;
    align-items: center;
    padding-left: 4px;
    &.hide {
      left: -10px;
      span {
        transform: rotateZ(180deg);
      }
    }
    &:hover {
      left: -25px;
    }
    span {
      color: #fff;
      transition: all 0.1s;
    }
  }
  .trigger {
    position: relative;
    width: 60px;
    border-color: #eee;
    background-color: #fff;
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
    border-radius: 6px;
    overflow: hidden;
    .triggerItem {
      height: 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #464646;
      user-select: none;
      white-space: nowrap;
      &:hover {
        background-color: #ededed;
      }
      &.active {
        color: #409eff;
        font-weight: bold;
      }
      .triggerIcon {
        font-size: 18px;
        margin-bottom: 5px;
      }
      .triggerName {
        font-size: 13px;
      }
    }
  }
}
</style>
