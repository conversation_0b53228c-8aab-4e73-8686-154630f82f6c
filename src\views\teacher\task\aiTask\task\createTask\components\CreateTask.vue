<script setup lang="ts">
import {
  getStudentList,
  getTeacherClassType,
  getTeacherLastClass,
} from '@/api/taskCenter'

import CustomSelectionDialog from '@/views/teacher/task/createTask/components/CustomSelectionDialog.vue'
import StudentDrawer from '@/views/teacher/task/createTask/components/StudentDrawer.vue'

const route = useRoute()
let isChecked = $ref<any>([])
let list = $ref<any>([])
let showDialog = $ref<boolean>(false)
let showStudentDrawer = $ref<boolean>(false)
let studentArr = $ref<any>([])
let classType = $ref(1)
let classTypeList = $ref<any>([])
const isHilight = $computed(() => {
  return !!studentData.value.classList.length
})
const studentData = inject<Ref<any>>('studentData', ref({}))

async function getLastClass() {
  if (!classType) return
  let res = await getTeacherLastClass({
    classType,
    taskType: 5,
    sysSubjectId: route.query.subjectId,
  })
  if (!res) {
    list = []
    return
  }

  list = [
    {
      ...res,
      id: res.schoolClassId,
      name: res.sysGradeName + res.className,
    },
  ]
  isChecked = [list?.[0]?.id]
  await getStudent()
  handleRadioChecked()
}
function handleRadioChecked() {
  if (isChecked.length) {
    studentData.value.specialClass = {
      ...list[0],
      selectStudentArr: studentArr,
      arrangeObjectType: 1,
    }
    studentData.value.selectStudentList = [
      ...new Set([
        ...studentData.value.selectStudentList,
        ...studentArr.map(v => v.schoolStudentId),
      ]),
    ]
  }
  else {
    studentData.value.specialClass = null
    studentData.value.selectStudentList = [
      ...new Set(
        [
          ...combineStudentAndGroup(studentData.value.classList),
          ...(studentData.value.specialClass?.selectStudentArr || []),
        ].map(v => v.schoolStudentId),
      ),
    ] // 如果一个学生既在特定的班级，也在自定义选择里被选了，那么取消选中特定的班级不会取消这个学生
  }
}
function combineStudentAndGroup(classList) {
  return classList.reduce((res, item) => {
    res = [
      ...res,
      ...(item.selectStudentArr || []),
      ...(item.selectGroupArr?.flatMap(v => v.list) || []),
    ]
    return res
  }, [])
}

async function handleChecked(val, item) {
  let find = classTypeList.find(
    h => h.classType !== item.classType && h.checked,
  )
  if (find && val) {
    if (studentData.value.selectStudentList.length) {
      $g.showConfirmDialog({
        title: '确认切换班级类型',
        message: '切换班级类型，所有选择的学生会清空，是否继续',
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        zIndex: 100,
      })
        .then(async () => {
          studentData.value = {
            classList: [],
            specialClass: null,
            selectStudentList: [],
            disabledStudentIds: [],
            disabledGroupIds: [],
            selectedResource: [],
          }
          isChecked = []
          find.checked = false
          classType = item.classType
          await getLastClass()
          getStudent()
        })
        .catch(() => {
          item.checked = false
        })
    }
    else {
      find.checked = false
      classType = item.classType
      await getLastClass()
      getStudent()
    }
    return
  }
  if (!val && !find) {
    item.checked = true
    classType = item.classType
  }
}
async function getStudent() {
  if (!list[0]?.id || !route.query.subjectId) return
  let res = await getStudentList({
    schoolClassId: list[0]?.id,
    sysSubjectId: route.query.subjectId,
  })
  studentArr = res || []
}
async function getClassType() {
  if (!route.query.subjectId) return
  let res = await getTeacherClassType({
    sysSubjectId: route.query.subjectId,
  })
  classTypeList = (res || []).map((h) => {
    return {
      ...h,
      checked: false,
    }
  }) || []
  if (!classTypeList.length) return

  classTypeList[0].checked = true
  classType = classTypeList[0].classType
}
watch(() => classType, (newVal) => {
  if (newVal) studentData.value.classType = newVal
}, {
  immediate: true,
})
onBeforeMount(async () => {
  await getClassType()
  await getLastClass()
})
</script>

<template>
  <div>
    <div>
      <div class="flex items-center">
        <span class="text-16px leading-[24px] font-600">1.布置对象</span>
        <div v-if="classTypeList.length > 1" class="ml-39px myCheckbox">
          <el-checkbox
            v-for="item in classTypeList"
            :key="`${item.classType}classType`"
            v-model="item.checked"
            :label="item.classTypeName"
            class="text-[#74788D] mr-36px"
            @change="($event) => handleChecked($event, item)"
          />
        </div>
      </div>

      <div class="my-17px flex items-center" :class="{ '!my-10px': !$g.isPC }">
        <g-radio
          v-model="isChecked"
          :option="list"
          item-class="px-15px  border border-[#E6E6E6] bg-[#FBFBFB] text-[#666666] text-15px h-34px flex items-center br-[6px]"
          active-item-class="!border-[#646AB4] !bg-[#ECEFFF]"
          multiply
          show-marker
          @change="handleRadioChecked"
        />
        <el-button
          class="ml-5px border-[#DADDE8] bg-[#FBFBFB] text-[#666666] h-34px text-15px br-[6px]"
          :class="[
            isHilight
              ? ' !bg-[#ECEFFF]  !text-[#6474FD] !border-[#6474FD]'
              : '',
          ]"
          @click="showDialog = true"
        >
          自定义选择
        </el-button>

        <span
          class="ml-13px text-15px  van-haptics-feedback"
          :class="studentData?.selectStudentList?.length ? 'text-[#6474FD]' : 'text-[#74788D]'"
          @click="showStudentDrawer = true"
        >
          查看已选学生({{ studentData?.selectStudentList?.length }})
        </span>
      </div>
      <CustomSelectionDialog v-model:show="showDialog" :class-type="classType" />
      <StudentDrawer v-model="showStudentDrawer"></StudentDrawer>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.myCheckbox) {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #74788d;
  }
  .el-checkbox__label {
    font-size: 15px !important;
  }
}
</style>
