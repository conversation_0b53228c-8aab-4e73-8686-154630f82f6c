<script setup lang="ts">
import Export from './Export.vue'
import ToolbarNodeBtnList from './ToolbarNodeBtnList.vue'

const props = defineProps({
  mindMap: { type: Object },
  isFIB: { type: Boolean },
})
const emit = defineEmits(['changStatus'])
// "back", "forward"
const list = ['deleteNode',
'formula',
'fib',
'summary']
</script>

<template>
  <div class="toolbarContainer">
    <div ref="toolbarRef" class="toolbar">
      <!-- 节点操作 -->
      <div class="toolbarBlock h-62px">
        <ToolbarNodeBtnList
          :list="list"
          :mind-map="mindMap"
          :is-f-i-b="isFIB"
          @chang-status="emit('changStatus')"
        ></ToolbarNodeBtnList>
      </div>
      <!-- 导出 -->
      <div class="toolbarBlock h-62px">
        <div class="toolbarBtn" @click="$g.bus.emit('showExport')">
          <span class="icon iconfont iconexport"></span>
          <span class="text">导出</span>
        </div>
      </div>
    </div>
    <Export :mind-map="mindMap"></Export>
  </div>
</template>

<style lang="scss" scoped>
.toolbarContainer {
  .toolbar {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 10px;
    height: 62px;
    display: flex;
    padding: 0 20px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(26, 26, 26, 0.8);
    z-index: 2;

    .toolbarBlock {
      display: flex;
      background-color: #fff;
      padding: 10px 20px;
      border-radius: 6px;
      box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.06);
      margin-right: 20px;

      &:last-of-type {
        margin-right: 0;
      }
    }

    .toolbarBtn {
      display: flex;
      justify-content: center;
      flex-direction: column;
      cursor: pointer;
      margin-right: 20px;
      flex-shrink: 0;

      &:last-of-type {
        margin-right: 0;
      }

      &:hover {
        &:not(.disabled) {
          .icon {
            background: #f5f5f5;
          }
        }
      }

      &.active {
        .icon {
          background: #f5f5f5;
        }
      }

      &.disabled {
        color: #bcbcbc;
        cursor: not-allowed;
        pointer-events: none;
      }

      .icon {
        display: flex;
        height: 26px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #e9e9e9;
        justify-content: center;
        flex-direction: column;
        text-align: center;
        padding: 0 5px;
      }

      .text {
        margin-top: 3px;
      }
    }
  }
}
</style>
