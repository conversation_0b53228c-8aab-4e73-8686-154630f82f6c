export const useAiTaskStore = defineStore('aiTask', {
  state: () => ({
    // 课程编辑状态
    modifyMap: {},
    // 留言状态
    leaveMsg: {},
  }),
  actions: {
    // 标记为已编辑
    setModified(id: any) {
      this.modifyMap[id] = true
    },
    // 判断是否已编辑过
    isModified(id: any) {
      return !!this.modifyMap[id]
    },
    // 标记为已编辑
    setLeaveMsg(id: any) {
      this.leaveMsg[id] = true
    },
    // 判断是否已编辑过
    isLeaveMsg(id: any) {
      return !!this.leaveMsg[id]
    },
  },
  persist: {
    storage: localStorage,
  },
})
