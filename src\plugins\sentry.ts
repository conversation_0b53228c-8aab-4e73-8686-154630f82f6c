import config from '@/config/index'
import {
  addIntegration,
  browserTracingIntegration,
  init,
  setUser,
} from '@sentry/vue'

export function registerSentry(app) {
  if (import.meta.env.MODE === 'production') {
    // 只在生产环境中开启sentry，调试时可以先去掉
    init({
      app,
      dsn: 'https://<EMAIL>/4508805569904640',
      integrations: [browserTracingIntegration()],
      ignoreErrors: [
        /timeout/i,
        /network error/i,
        /dynamically imported module/i,
        /Unable to preload CSS/i,
        /Loading chunk .+ failed/i,
        /token错误/i,
        /登录已过期/,
        /请重新登录/,
        /token无效/i,
        /没有该操作权限/,
        /Request aborted/i,
        /第三方账号不存在/,
        /schoolTeacher不存在/,
      ],
      environment: import.meta.env.VITE_APP_ENV,
      tracesSampleRate: 1.0,
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1,
      normalizeDepth: 5,
      release: $g.dayjs(__APP_VERSION__).format('YYYY-MM-DD HH:mm'),
      beforeSend: (event, hint: any) => {
        const originalException = hint.originalException
        if (originalException?.data?.code) {
          const ignoreArr = [...config.resetArr,
500,
502]
          if (ignoreArr.includes(originalException?.data?.code))
            return null

          // 给不同消息添加签名
          event.fingerprint = [
            '{{ default }}', String(originalException.config.url),
          ]
        }

        return event
      },
    })

    setTimeout(async () => {
      const { replayIntegration } = await import('@sentry/vue')
      addIntegration(
        replayIntegration({
          maskAllText: false,
          blockAllMedia: false,
        }),
      )
      setUser({
        账号信息: JSON.parse(localStorage.getItem('user') as string)?.userInfo,
      })
      console.log('⚡[ replayIntegration初始化成功 ] ')
    }, 2222)
  }
}
