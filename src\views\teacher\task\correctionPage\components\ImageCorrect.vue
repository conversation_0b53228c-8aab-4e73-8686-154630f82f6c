<script setup lang="ts">
import { submitTeacherCorrect } from '@/api/correctionPage'
import CorrectionCnp from './CorrectionCnp/index.vue'

const props = defineProps({
  imgList: {
    type: Array as PropType<any[]>,
    required: true,
  },
})

const CorrectionCnpRef = useTemplateRef<typeof CorrectionCnp>('CorrectionCnpRef')

const currentImgIndex = ref(0)

const currentImgObject = computed(() => {
  return props.imgList[currentImgIndex.value]
})

watch(() => props.imgList, () => currentImgIndex.value = 0, { immediate: true })

// 切换图片
async function handleChangeImageIndex(index) {
  try {
    if (index === currentImgIndex.value) return
    await saveCanvasData()
    currentImgIndex.value = index
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
}

/** 保存画板变更 */
async function saveCanvasData() {
  let isModified = CorrectionCnpRef.value?.isModified()
  if (!isModified)
    return

  saveCanvasJson()
  await saveCanvasImage()
}

/** 保存画板json数据 */
function saveCanvasJson() {
  let noteData = CorrectionCnpRef.value?.exportJsonString()
  if (noteData) {
    let currentObject = props.imgList[currentImgIndex.value]
    currentObject.noteData = noteData
  }
}

/** 保存画板图片 */
async function saveCanvasImage() {
  let currentIndex = currentImgIndex.value
  let currentObject = props.imgList[currentIndex]
  let imageArr = props.imgList.map(item => item.thumbnailUrl)
  let exportSrc = await CorrectionCnpRef.value?.exportImage()
  if (exportSrc) {
    // 更改略缩图显示
    currentObject.thumbnailUrl = exportSrc
    imageArr[currentIndex] = exportSrc

    submitTeacherCorrect({
      exerciseSubId: currentObject.exerciseSubId,
      comments: imageArr.join(','),
    })
  }
}

// 一键清除所有痕迹
async function handleClear() {
  let currentIndex = currentImgIndex.value
  let currentObject = props.imgList[currentIndex]
  let originUrl = currentObject.originUrl

  if (originUrl !== currentObject.commentUrl) {
    // 重置为学生初始作答图片
    currentObject.thumbnailUrl = originUrl
    currentObject.commentUrl = originUrl
    currentObject.noteData = null

    submitTeacherCorrect({
      exerciseSubId: currentObject.exerciseSubId,
      comments: props.imgList.map(item => item.thumbnailUrl).join(','),
    })
  }
}

defineExpose({
  saveCanvasData,
})
</script>

<template>
  <div class="h-full w-full overflow-auto flex flex-col justify-center">
    <!-- 正在批改的图片 -->
    <CorrectionCnp
      ref="CorrectionCnpRef"
      :img-src="currentImgObject.commentUrl || ''"
      :note-data="currentImgObject.noteData"
      @clear="handleClear"
    />
    <!-- 缩略图列表 -->
    <div v-if="imgList.length > 1" class=" overflow-x-auto no-bar mt-16px text-center">
      <div class="inline-flex items-center">
        <img
          v-for="(item, index) in imgList"
          :key="index"
          class="w-150px h-90px object-cover rounded-[6px] flex-shrink-0 mr-10px border-[2px] border-solid border-[#E8E8E8FF] cursor-pointer"
          :class="{ '!border-[#6474FDFF]': currentImgIndex === index }"
          :src="item.thumbnailUrl"
          @click="handleChangeImageIndex(index)"
        >
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
