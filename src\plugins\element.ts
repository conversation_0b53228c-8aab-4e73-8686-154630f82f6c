export const elementFunction = {
  msg: (
    msg: string,
    type: 'success' | 'warning' | 'error' | 'info' = 'success',
    options: Record<string, any> = { duration: 3000 },
  ) => {
    ElMessage({
      message: msg,
      type,
      dangerouslyUseHTMLString: true,
      grouping: true,
      ...options,
    })
  },
  confirm: (config) => {
    let {
      title = '温馨提示',
      content = '内容',
      confirmButtonText = '确定',
      cancelButtonText = '取消',
      distinguishCancelAndClose = false,
      showCancelButton = true,
    } = config
    return ElMessageBox.confirm(content, title, {
      confirmButtonText,
      cancelButtonText,
      showCancelButton,
      closeOnClickModal: false,
      type: 'warning',
      lockScroll: false,
      distinguishCancelAndClose, // 是否区分取消按钮和关闭
    })
  },
}
