<script setup lang="ts">
interface Option {
  id?: string | number
  name?: string
  [key: string]: any
}

const props = defineProps({
  /** 选项列表 */
  option: {
    type: Array as PropType<Option[]>,
    default: () => [],
  },
  /** 选项替换的键 */
  replaceKeys: {
    type: Object as PropType<{ id: string
      name: string }>,
    default: () => ({
      id: 'id',
      name: 'name',
    }),
  },
  /** 是否支持多选，多选时选中的值为数组形式 */
  multiply: {
    type: Boolean,
    default: false,
  },

  /** 初始化值 */
  modelValue: {
    type: [String,
Number,
Array] as PropType<string | number | Array<string | number>>,
    default: '',
  },

  itemClass: {
    type: String,
    default: '',
  },

  activeItemClass: {
    type: String,
    default: '',
  },

  /** 是否显示底部角标 */
  showMarker: {
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['update:modelValue', 'change'])

/** 选中的值 */
let checkedValue: any = $ref(undefined)

watchEffect(() => {
  if (props.multiply) {
    checkedValue = !props.modelValue ? [] : props.modelValue
    return
  }
  checkedValue = props.modelValue
})

/** 判断选项是否选中 */
function optionIsChecked(val) {
  if (props.multiply)
    return checkedValue.includes(val)

  return checkedValue === val
}

/** 选项选中逻辑 */
function handleOptionClick(item) {
  let val = item[props.replaceKeys.id]
  if (!props.multiply)
    checkedValue = val

  else
    checkedValue.includes(val) ? (checkedValue = checkedValue.filter(v => v !== val)) : checkedValue.push(val)

  emit('update:modelValue', checkedValue)
  emit('change', checkedValue, item)
}
</script>

<template>
  <div class="inline-flex flex-wrap items-center">
    <div
      v-for="item in option"
      :key="item[replaceKeys.id]"
      :class="[`option-item ${itemClass} `, optionIsChecked(item[props.replaceKeys.id]) && `active ${activeItemClass}`]"
      @click="handleOptionClick(item)"
    >
      <span>{{ item[replaceKeys.name] }}</span>

      <slot v-if="showMarker" name="marker">
        <img
          src="@/assets/img/common/check.png"
          class="bg-[#DADDE8] w-12px rounded-tl-[6px] absolute right-0 bottom-0"
          :class="[optionIsChecked(item[props.replaceKeys.id]) && '!bg-[#6775FB]']"
        >
      </slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.option-item{
  @apply relative overflow-hidden text-13px leading-[13px] px-9px py-6px  mr-8px br-[4px] cursor-pointer text-[#6C6C74FF] transition-all select-none;
}

.option-item.active{
  @apply font-500 text-[#5864F8FF] bg-[#ecefff];
}
</style>
