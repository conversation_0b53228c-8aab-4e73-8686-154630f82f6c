<script setup lang="ts">
import Draggable from 'vuedraggable'

const emits = defineEmits(['scrollIntoView'])

const showDialog = defineModel('show', { default: false })

const isSortByType = $ref(false)

// 选择的题目列表数据
const questionList = inject<Ref<any[]>>('questionList', ref([]))
// 用于展示的题目列表数据
const showList = ref<any[]>([])

/** 切换类型时的处理函数 */
function handleSwitchChange() {
  // 按类型排序
  if (isSortByType) {
    const arr: any = []
    questionList.value.forEach((question) => {
      const existingType = arr.find(v => v.typeId === question.sysQuestionTypeId)
      if (!existingType) {
        arr.push({
          typeId: question.sysQuestionTypeId,
          typeName: question.sysQuestionTypeName,
          children: [question],
        })
      }
      else {
        existingType.children.push(question)
      }
    })
    showList.value = arr
  }
  else {
    // 单独排序
    showList.value = questionList.value
  }
  handleUpdateList()
}

/** 更新列表 */
function handleUpdateList() {
  questionList.value = isSortByType ? showList.value.flatMap(child => child.children) : showList.value
}

// 获取题目的序号
function getOrder(questionId) {
  return questionList.value.findIndex(question => question.questionId === questionId) + 1
}

defineExpose({
  handleSwitchChange,
})
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    :overlay="false"
    position="right"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="w-256px h-[80vh] flex flex-col py-17px px-20px overflow-hidden shadow-lg rounded-tl-[13px] rounded-bl-[13px]"
    teleport="#app"
    @open="handleSwitchChange"
  >
    <div class="flex items-center justify-between font-600 text-17px">
      <span>自定义排序</span>
      <img class="cursor-pointer w-15px"
           src="@/assets/img/taskCenter/close.png"
           @click="showDialog = false"
      />
    </div>

    <span class="mt-9px mb-17px text-14px text-[#929296]">拖动卡片可调整顺序</span>

    <div class="flex items-center justify-between">
      <span>按题型分类</span>
      <el-switch
        v-model="isSortByType"
        @change="handleSwitchChange"
      />
    </div>

    <el-scrollbar
      class="flex-1  mt-16px -mr-16px pr-16px"
    >
      <Draggable
        v-if="showList.length"
        v-model="showList"
        item-key="questionId"
        handle=".handle"
        animation="150"
        ghost-class="opacity-[0.5]"
        @change="handleUpdateList"
      >
        <template #item="{ element }">
          <div class="border border-[#e8e8e8] rounded-[6px] pl-13px pt-10px mb-9px bg-[white] cursor-default select-none ">
            <!-- 非题型分类 -->
            <div
              v-if="!isSortByType"
              class="flex justify-between items-center pr-13px pb-10px"
              @click="emits('scrollIntoView', element.questionId)"
            >
              <span class="flex-1">
                {{ getOrder(element.questionId) }}.{{ element.sysQuestionTypeName }}
              </span>
              <img src="@/assets/img/taskCenter/drag.png"
                   class="handle cursor-move w-19px h-15px pl-10px"
                   @click.stop
              />
            </div>

            <!-- 题型分类 -->
            <div v-else>
              <div class="flex justify-between items-center mb-12px pr-13px">
                <span>{{ element.typeName }}</span>
                <img src="@/assets/img/taskCenter/drag.png"
                     class="handle cursor-move w-19px h-15px pl-10px"
                     @click.stop
                />
              </div>

              <Draggable
                v-model="element.children"
                class="flex items-center flex-wrap"
                item-key="typeId"
                animation="150"
                ghost-class="opacity-[0.5]"
                @change="handleUpdateList"
              >
                <template #item="{ element: childElement }">
                  <div
                    class="cursor-move w-37px h-37px leading-[35px] text-center border border-[#6474FD24] bg-[#F3F4F9FF] rounded-full mr-13px mb-10px"
                    @click="emits('scrollIntoView', childElement.questionId)"
                  >
                    {{ getOrder(childElement.questionId) }}
                  </div>
                </template>
              </Draggable>
            </div>
          </div>
        </template>
      </Draggable>

      <g-empty v-else />
    </el-scrollbar>
  </van-popup>
</template>

<style lang="scss" scoped>

</style>
