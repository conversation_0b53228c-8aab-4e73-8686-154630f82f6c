import config from '@/config/index'
import request from '@/utils/request/index'

const {
  VITE_PAD_API,
  VITE_WORKBENCH_API,
  VITE_JZT_API,
  VITE_APP_BASE_API,
} = config

// 获取oss签名(需要验证该接口)
export function getOssSignature(data) {
  return request.put(`${VITE_JZT_API}/tutoring/oss/policy`, data, { delay: false })
}

// 工作台导航登陆接口-用于调试页面
export async function workbenchLogin(data) {
  return request.post(`${VITE_WORKBENCH_API}/login/pc`, data, {
    delay: false,
    headers: {
      version: '',
      platform: '',
    },
  })
}

// 教师端-登录加密
export async function getEncrypt() {
  return request.post(`${VITE_APP_BASE_API}/v3/teacher/jzt/login/encrypt`, {}, { delay: false })
}

// 智习室教师端-登录解密
export async function login(data) {
  return request.post(`${VITE_JZT_API}/tutoring/login/zhixishi/teacher`, data, { delay: false })
}

/* 学生账号列表---测试用 */
export function getListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/accountManage/listStudentAccount`,
    data,
  )
}

/* 获取任务模块配置 */
export function getTaskModuleConfig() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/getTaskModuleConfig`,
  )
}
