<script setup lang="ts">
import type { PropType } from 'vue'

const props = defineProps({
  questionItem: {
    type: Object,
    required: true,
  },
  curPaper: {
    type: Object,
    default: () => ({}),
  },
  questionIndex: {
    type: Number,
    default: 0,
  },
  renderIfChange: {
    type: Boolean,
    default: false,
  },
  // 是否需要分割线
  needLine: {
    type: Boolean,
    default: true,
  },
  currentQuestionListAll: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})

const emit = defineEmits<{
  change: [any]
}>()

// 用在虚拟列表中时，需要监听题目数据变化，重新渲染数学公式
if (props.renderIfChange) {
  watch(() => props.questionItem, () => {
    nextTick($g.tool.renderMathjax)
  }, { deep: false })
}

let containerRef = $ref<any>(null)

let subQuestion = $computed(() => props.questionItem.subQuestions?.map((item) => {
  return {
    ...item,
    currentSubParseIndex: 0,
    optionArr: Object.keys(item)
      .filter(
        key =>
          key.includes('option') && item[key] && key !== 'optionNumber',
      )
      .map((realKey) => {
        return {
          name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
          title: item[realKey].toString(),
        }
      }),
  }
}))

const questionList = inject<Ref<any[]>>('questionList', ref([]))
function add(item) {
  item.isAdd = !item.isAdd
  if (item.isAdd) {
    questionList.value = [...questionList.value, item]
    // 将currentQuestionListAll中item.questionId设置为true
    props.currentQuestionListAll.find(v => v.questionId == item.questionId).isAdd = true
  }
  else {
    const index = $g._.findIndex(
      questionList.value,
      e => e.questionId == item.questionId,
    )
    questionList.value.splice(index, 1)
    // 将currentQuestionListAll中item.questionId设置为false
    props.currentQuestionListAll.find(v => v.questionId == item.questionId).isAdd = false
  }
  emit('change', [item.questionId, item.isAdd])
}
function openParse() {
  props.questionItem.showParse = !props.questionItem.showParse
  if (props.questionItem.showParse) {
    containerRef.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
  nextTick($g.tool.renderMathjax)
}
</script>

<template>
  <div ref="containerRef" class="h-auto p-16px question-item">
    <div class="flex items-start">
      <div class="flex-1 overflow-hidden">
        <!-- 大题题干 -->
        <g-mathjax
          v-if="questionItem.questionTitle"
          class="mt-10px"
          :text="questionItem.questionTitle"
          :order="`${questionIndex + 1}. `"
        />

        <div
          v-for="(value, subIndex) in subQuestion"
          :key="value.subQuestionId"
          class="mt-10px"
        >
          <!-- 子题题干，如果大题没有题干时，需要充当大题题干,多个子题时，如果子题没有题干又要显示序号，则传入一个空的html元素 -->
          <g-mathjax
            :text="value.subQuestionTitle || (subQuestion.length > 1 && value.optionArr.length > 1 ? '<span></span>' : '')"
            :class="questionItem.questionTitle ? 'ml-16px' : ''"
            :order="!questionItem.questionTitle ? `${questionIndex + 1}. `
              : subQuestion.length > 1 ? value.structureNumber || `(${subIndex + 1})`
                : undefined"
          />
          <!-- 选项 -->
          <div
            v-for="item in value.optionArr"
            :key="item.name"
            :class="questionItem.questionTitle && value.subQuestionTitle ? 'pl-32px' : 'pl-16px'"
            class="flex items-center py-10px"
          >
            <span class="flex-shrink-0 mr-4px"> {{ item.name }}. </span>
            <g-mathjax :text="item.title" />
          </div>
        </div>
        <slot name="tagList">
          <!-- 标签列表 -->
          <div class="flex items-center flex-wrap mt-[50px]">
            <!-- 年份 -->
            <div v-if="questionItem.year" class="tag">
              {{ questionItem.year }}
            </div>
            <!-- 题型 -->
            <div class="tag">
              {{ questionItem.sysQuestionTypeName || '其他' }}
            </div>
            <!-- 难度 -->
            <div class="tag">
              {{ questionItem.sysQuestionDifficultyName || '其他' }}
            </div>
            <!-- 知识点 -->
            <template v-if="questionItem?.knowledgePoints?.length">
              <div
                v-for="item in questionItem.knowledgePoints"
                :key="item.commonKnowledgePointsId"
                class="tag"
              >
                {{ item.sysKnowledgePointName }}
              </div>
            </template>
          </div>
        </slot>
      </div>
      <slot name="extraData"></slot>
    </div>

    <!-- 分割线 -->
    <div class="w-full h-[1px] bg-[#E8E8E8] mt-[18px]" />

    <slot name="footer" :question-item="questionItem">
      <div class="flex items-center justify-end mt-10px">
        <div
          v-if="!questionItem.showParse"
          class="flex cursor-pointer items-center"
          @click="openParse"
        >
          <div class="text-[#6C6C74] text-[13px]">
            答案及解析
          </div>
          <svg-common-expand class="w-[16px] ml-[4px] h-[16px]" />
        </div>
        <div v-else
             class="flex items-center cursor-pointer"
             @click="openParse"
        >
          <div
            class="text-[#6474FD] text-[13px] p-[6px] w-fit br-[5px] flex items-center bg-[#ECEFFF]"
          >
            收起答案<img
              src="@/assets/img/taskCenter/pack.png"
              class="w-16px ml-[4px] h-16px"
              alt=""
            />
          </div>
        </div>
        <div v-if="needLine" class="w-[1px] h-[11px] mr-[27px] ml-[14px] bg-[#DDDDDD]"></div>
        <slot name="footerAction">
          <div
            :class="
              questionItem?.isAdd
                ? 'border-[#FF4646] text-[#FF4646]'
                : 'border-[#646AB4]  bg-[#ECEFFF] text-[#6474FD]'
            "
            class="cursor-pointer border px-[13px] py-[4px] w-fit br-[5px]"
            @click="add(questionItem)"
          >
            {{ questionItem?.isAdd ? '取消加入' : '加入' }}
          </div>
        </slot>
      </div>
    </slot>
    <!-- 解析/答案 -->
    <div v-show="questionItem.showParse" class="mt-13px bg-[#F3F4F9] br-[6px] p-16px">
      <div class="flex">
        <span class="text-[13px] mr-[20px] text-[#6C6C74] flex-shrink-0">
          答案
        </span>
        <div>
          <div
            v-for="(
              answerItem, answerIndex
            ) in questionItem?.subQuestionAnswerList"
            :key="answerIndex"
            class="flex mb-[10px]"
          >
            <span
              v-if="questionItem?.subQuestionAnswerList?.length > 1"
              class="mr-[10px]"
            >
              ({{ answerIndex + 1 }})
            </span>
            <g-mathjax :text="answerItem"></g-mathjax>
          </div>
        </div>
      </div>
      <div class="flex mt-[13px]">
        <div class="mr-[20px] text-[13px] text-[#6C6C74] flex-shrink-0">
          解析
        </div>
        <div>
          <div
            v-for="(
              parseItem, parseIndex
            ) in questionItem?.subQuestionParseList"
            :key="parseIndex"
            class="flex mb-[10px]"
          >
            <span
              v-if="questionItem?.subQuestionParseList?.length > 1"
              class="mr-[10px]"
            >
              ({{ parseIndex + 1 }})
            </span>
            <div>
              <div
                v-for="(subParseItem, subParseIndex) in parseItem"
                :key="subParseIndex"
              >
                <span v-if="parseItem?.length > 1" class="mr-[10px]">
                  解法{{ subParseIndex + 1 }}:
                </span>
                <g-mathjax
                  class="ml-[4px]"
                  :text="subParseItem.content"
                ></g-mathjax>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter,
.fade-leave-active {
  opacity: 0.3;
}

.tag {
  @apply mr-8px text-[12px] border bg-[#F4F5FA] p-[3px] w-fit br-[5px] mt-4px text-[#6C6C74] border-[#D7DDE9];
}

.question-item {
  :deep() {
    .g-mathjax {
      overflow: hidden;
      * {
        white-space: pre-wrap;
      }

      & > :first-child::before {
        font-size: 15px;
      }
    }
  }
}
</style>
