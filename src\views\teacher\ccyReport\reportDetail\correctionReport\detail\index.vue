<script setup lang="ts" name="CorrectionReport">
import { getCorrectionReportDetail } from '@/api/activity'
import { getQuestionListDetailApi } from '@/api/taskCenter'
import QuestionStemItem from '@/views/teacher/task/questionReport/components/QuestionStemItem.vue'
import RecordDialog from './components/RecordDialog.vue'

const route = useRoute()
const title = $computed(() => {
  return `${route.query.userName}学生·${route.query.courseName}·错题订正记录`
})
let currentType = $ref<any>('')
let questionList = $ref<any>([])
let pageOption = reactive<any>({
  page: 1,
  page_size: 10,
  total: 0,
})
let showLoading = $ref<boolean>(false)
let showRecordDialog = $ref<boolean>(false)
let currentQuestion = $ref<any>(null)
const objectTypeList = $ref<any>([
  {
    id: '2',
    title: '只看已订正',
  },
  {
    id: '1',
    title: '只看未订正',
  },
])
async function handleChoose(item: any) {
  questionList = []
  if (currentType === item.id)
    currentType = ''

  else
    currentType = item.id

  pageOption.page = 1
  await fetchCorrectionReportDetail()
}
async function pulldown() {
  pageOption.page = 1
  await fetchCorrectionReportDetail()
}
async function pullup() {
  if (pageOption.total === questionList.length)
    return

  pageOption.page += 1
  await fetchCorrectionReportDetail(true)
}
function openAnswer(item: any) {
  item.showAnswer = !item.showAnswer
}
/* 获取订正报告详情 */
async function fetchCorrectionReportDetail(addToEnd?: boolean) {
  try {
    showLoading = true
    let params = {
      accountId: route.query.accountId,
      sysCourseId: route.query.sysCourseId,
      beginDateTime: route.query.beginDateTime,
      endDateTime: route.query.endDateTime,
      page: pageOption.page,
      pageSize: pageOption.page_size,
      isAmend: currentType,
    }
    let res = await getCorrectionReportDetail(params)
    pageOption.total = res.total
    if (res.list.length) {
      let res1 = await getQuestionListDetailApi({
        questionIdList: res.list.map((item: any) => item.questionId),
      })
      questionList = addToEnd
        ? [...questionList,
...res1.map((v, i) => {
            return {
              ...v,
              errorBookId: res.list[i].errorBookId,
            }
          })]
        : res1.map((v, i) => {
            return {
              ...v,
              errorBookId: res.list[i].errorBookId,
            }
          })
    }
    else {
      questionList = []
    }
    showLoading = false
  }
  catch (err) {
    questionList = []
    console.log('获取订正报告详情失败', err)
  }
}
function openRecordDialog(item: any) {
  currentQuestion = item
  showRecordDialog = true
}
onBeforeMount(() => {
  fetchCorrectionReportDetail()
})
</script>

<template>
  <div class="px-26px pt-26px">
    <g-navbar :title="title" class="mb-17px">
      <template #right>
        <div class="flex items-center">
          <div
            v-for="item in objectTypeList"
            :key="item.id"
            class="flex items-center ml-32px cursor-pointer"
            @click="handleChoose(item)"
          >
            <svg-common-checked v-if="item.id == currentType" class="w-13px h-13px" />
            <svg-common-circle v-else class="w-13px h-13px" />
            <div class=" text-[#74788D] ml-4px">
              {{ item.title }}
            </div>
          </div>
        </div>
      </template>
    </g-navbar>
    <!-- 试题列表 -->
    <div class="h-[calc(100vh-100px)] overflow-y-auto no-bar">
      <g-list
        v-model:data="questionList"
        :page-option="pageOption"
        url="/tutoring/admin/activity/statistics/amend/detail"
        :show-loading="showLoading"
        @pulldown="pulldown"
        @pullup="pullup"
      >
        <QuestionStemItem
          v-for="(item, index) in questionList"
          :key="item.id"
          :question-item="item"
          :index="index + 1"
        >
          <template #footer>
            <div class="mt-12px">
              <div class="flex items-center justify-between">
                <div
                  class="flex items-center mt-15px w-100px cursor-pointer  "
                  @click="openAnswer(item)"
                >
                  <svg-menu-eye-close v-if="!item.showAnswer" class="w-17px h-17px"></svg-menu-eye-close>
                  <svg-menu-eye-open v-else class="w-17px h-17px"></svg-menu-eye-open>
                  <div class="text-15px text-[#6474FD] ml-5px ">
                    答案及解析
                  </div>
                </div>
                <div class="text-[#6474FD] cursor-pointer border border-[#6474FD] rounded-[5px] px-17px py-6px van-haptics-feedback" @click="openRecordDialog(item)">
                  查看订正记录
                </div>
              </div>
              <div v-if="item.showAnswer" class="mt-13px">
                <div class="flex items-start">
                  <div class="text-[15px] text-[#6474FD] mr-13px flex-shrink-0">
                    【详情】
                  </div>
                  <div class="text-16px text-[#333] pb-18px ">
                    <div
                      v-for="(v, i) in item?.subQuestions"
                      :key="i"
                      class="flex items-start"
                      :class="{
                        'mt-10px': i !== 0,
                      }"
                    >
                      <div v-if="item?.subQuestions.length > 1" class="mr-5px">
                        ({{ i + 1 }})
                      </div>
                      <g-mathjax :text="v.subQuestionParse" class="text-16px" />
                    </div>
                  </div>
                </div>
                <div class="flex items-start mt-13px">
                  <div class="text-[15px] text-[#6474FD] mr-13px flex-shrink-0">
                    【答案】
                  </div>
                  <div class="text-16px text-[#333] pb-18px   mb-17px">
                    <div
                      v-for="(v, i) in item?.subQuestions"
                      :key="i"
                      class="flex items-start"
                      :class="{
                        'mt-10px': i !== 0,
                      }"
                    >
                      <div v-if="item?.subQuestions.length > 1" class="mr-5px">
                        ({{ i + 1 }})
                      </div>
                      <g-mathjax :text="v.subQuestionAnswer" class="text-16px" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </QuestionStemItem>
      </g-list>
    </div>
    <!-- 订正详情 -->
    <RecordDialog v-model:show="showRecordDialog" :question-item="currentQuestion" />
  </div>
</template>

<style lang="scss" scoped>

</style>
