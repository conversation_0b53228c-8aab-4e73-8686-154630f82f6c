<script setup lang="ts" name="CorrectionDetail">
import { getCorrectionDetail, getQuestionListDetailApi, getStudentReportApi } from '@/api/taskCenter'
// import QuestionPanel from '../../questionReport/components/QuestionPanel.vue'

const route = useRoute()

let showLoading = $ref(true)
let quesList = $ref<any[]>([])
let currentQuesIndex = $ref(-1)
let quesDetail = $ref<any>(null)

let currentSubIndex = $ref(0)

let showQues = $ref(true)
let showExplain = $ref(false)

let correctionData = $ref<any>(null)

const currentQues = $computed(() => {
  return quesList[currentQuesIndex] || null
})

const currentSubQues = $computed(() => {
  return correctionData?.subQuestionResultList?.[currentSubIndex] || null
})

const currentQuesNumTitle = $computed(() => {
  if (currentQues) {
    const title = Number.parseFloat(currentQues.qnum)
    return `第${$g.tool.numberToChinese(title)}题`
  }
  return '---'
})

function toggleQues() {
  if (showQues) {
    showQues = false
  }
  else {
    showQues = true
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}

function toggleExplain() {
  if (showExplain) {
    showExplain = false
  }
  else {
    showExplain = true
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}

function getTitle(item, number) {
  const isSingle = quesDetail.subQuestions.length === 1
  const title = item.subQuestionTitle || ''
  const subTitle = Object.keys(item)
    .filter(
      key => key.includes('option') && key !== 'optionNumbers' && item[key],
    )
    .map((key) => {
      const option = key.replace('option', '')
      const value = item[key]
      return `<div class="ques-text-row"><span class="text-option">${option}.</span>${value}</div>`
    })
    .join('\n')
  const numberPrefix = isSingle
    ? ''
    : `<span class="ques-num">(${number})</span>`
  return (
    `<div class='ques-row'>${
      numberPrefix
    }<div><div>${
      title
    }</div><div>${
      subTitle
    }</div></div>`
  )
}

function handleSelect(index) {
  if (currentSubIndex !== index)
    currentSubIndex = index
}

function prev() {
  if (currentQuesIndex !== 0 && quesList.length !== 0) {
    currentQuesIndex--
  }
}

function next() {
  if (currentQuesIndex !== quesList.length - 1 && quesList.length !== 0) {
    currentQuesIndex++
  }
}

async function getStudentReport() {
  try {
    showLoading = true
    const { amendReport } = await getStudentReportApi({
      exerciseSourceType: route.query.exerciseSourceType,
      exerciseSourceId: route.query.exerciseSourceId,
      schoolId: route.query.schoolId,
      sysGradeId: route.query.sysGradeId,
      schoolClassId: route.query.schoolClassId,
      beginDateTime: route.query.startTime,
      endDateTime: route.query.endTime,
    })

    quesList = (amendReport.studentList.find(v => v.schoolStudentId === Number(route.query.schoolStudentId))?.errorList || [])
      .filter(item => item?.correctiveState == route?.query?.checked && item.exerciseId)
    currentQuesIndex = quesList.findIndex(item => item.questionId == route?.query?.questionId) || 0
  }
  catch (e) {
    showLoading = false
    console.error(e)
  }
}

async function getQuestionListDetail() {
  try {
    showLoading = true
    const data = await getQuestionListDetailApi({
      questionIdList: [currentQues.questionId],
    })

    quesDetail = data[0]
    nextTick(() => {
      $g.tool.renderMathjax()
    })
    showLoading = false
  }
  catch (e) {
    showLoading = false
    console.error(e)
  }
}

function getTitleText(status, isCorrect) {
  if (status === 1) return '未作答'
  return {
    1: '答错了',
    2: '部分对',
    3: '答对了',
    4: '我不会',
  }[isCorrect] || '未自查'
}

// function onQuesSelect([item, index]) {
//   currentQuesIndex = index
// }

async function getCorrectionDetailApi() {
  const data = await getCorrectionDetail({
    exerciseId: currentQues.exerciseId,
  })
  correctionData = data
}

function previewImg(urls, index) {
  $g.flutter('previewImage', {
    urls,
    index,
  })
}

onBeforeMount(() => {
  getStudentReport()
})

watch(() => currentQuesIndex, () => {
  currentSubIndex = 0
  showQues = true
  showExplain = false
  getQuestionListDetail()
  getCorrectionDetailApi()
})
</script>

<template>
  <div class="p-26px">
    <div class="w-full flex items-center justify-between mb-21px">
      <g-navbar :title="`答题结果详情 - ${route.query.title}`">
      </g-navbar>
      <div class="flex items-center text-17px select-none">
        <div
          class="flex items-center arrow-left-icon"
          :class="{
            'grayscale-[100%] cursor-not-allowed': currentQuesIndex === 0 || quesList.length === 0,
            'van-haptics-feedback': currentQuesIndex !== 0 && quesList.length !== 0,
          }"
          @click="prev"
        >
          <div class="text-[#6474FD] ml-10px">
            上一题
          </div>
        </div>
        <div
          class="flex items-center ml-29px arrow-right-icon"
          :class="{
            'grayscale-[100%] cursor-not-allowed': currentQuesIndex === quesList.length - 1 || quesList.length === 0,
            'van-haptics-feedback': currentQuesIndex !== quesList.length - 1 && quesList.length !== 0,
          }"
          @click="next"
        >
          <div class="text-[#6474FD] mr-10px">
            下一题
          </div>
        </div>
      </div>
    </div>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <template v-else>
      <g-empty v-if="!quesList.length"></g-empty>
      <template v-else>
        <div class="w-full bg-white rounded-[9px]">
          <div class="p-17px border-b border-solid border-[#F3F4F9]">
            <div
              class="w-full flex items-center justify-between"
              :class="{
                'mb-10px': showQues,
              }"
            >
              <div class="text-17px text-[#333] font-600">
                {{ currentQuesNumTitle }}
              </div>
              <div class="flex items-center text-15px text-[#6474FD] van-haptics-feedback" @click="toggleQues">
                <div class="mr-6px">
                  {{ showQues ? '收起' : '展开' }}
                </div>
                <img
                  src="@/assets/img/question/arrow.png"
                  alt="arrow"
                  class="w-15px h-9px"
                  :class="{
                    'rotate-180': !showQues,
                  }"
                >
              </div>
            </div>
            <template v-if="showQues">
              <div v-if="quesDetail?.sysQuestionTypeName" class="w-fit h-21px leading-[21px] rounded-[4px] bg-[#F3F4F9] px-3px text-13px text-[#6C6C74] mb-11px">
                {{ quesDetail?.sysQuestionTypeName }}
              </div>
              <div>
                <g-mathjax
                  :text="quesDetail?.questionTitle || ''"
                  class="text-16px font-600"
                ></g-mathjax>
                <div class="mt-5px">
                  <g-mathjax
                    v-for="(item, index) in quesDetail?.subQuestions"
                    :key="item.subQuestionId"
                    :text="getTitle(item, index + 1)"
                    class="text-16px"
                  ></g-mathjax>
                </div>
              </div>
            </template>
          </div>
          <div class="p-17px">
            <div
              class="w-full flex items-center justify-between"
              :class="{
                'mb-15px': showExplain,
              }"
            >
              <div class="text-17px text-[#333] font-600">
                答案解析
              </div>
              <div class="flex items-center text-15px text-[#6474FD] van-haptics-feedback" @click="toggleExplain">
                <div class="mr-6px">
                  {{ showExplain ? '收起' : '展开' }}
                </div>
                <img
                  src="@/assets/img/question/arrow.png"
                  alt="arrow"
                  class="w-15px h-9px"
                  :class="{
                    'rotate-180': !showExplain,
                  }"
                >
              </div>
            </div>
            <template v-if="showExplain">
              <div class="text-17px text-[#333] font-600 mb-11px">
                答案：
              </div>
              <div class="text-16px text-[#333] pb-18px border-b border-dashed border-[#CCCCCC] mb-17px">
                <div
                  v-for="(item, index) in quesDetail?.subQuestions"
                  :key="index"
                  class="flex items-start"
                  :class="{
                    'mt-10px': index !== 0,
                  }"
                >
                  <div v-if="quesDetail?.subQuestions.length > 1" class="mr-5px">
                    ({{ index + 1 }})
                  </div>
                  <g-mathjax :text="item.subQuestionAnswer" class="text-16px" />
                </div>
              </div>
              <div class="text-17px text-[#333] font-600 mb-11px">
                解析：
              </div>
              <div class="text-16px text-[#333] pb-18px border-b border-dashed border-[#CCCCCC] mb-17px">
                <div
                  v-for="(item, index) in quesDetail?.subQuestions"
                  :key="index"
                  class="flex items-start"
                  :class="{
                    'mt-10px': index !== 0,
                  }"
                >
                  <div v-if="quesDetail?.subQuestions.length > 1" class="mr-5px">
                    ({{ index + 1 }})
                  </div>
                  <g-mathjax :text="item.subQuestionParse" class="text-16px" />
                </div>
              </div>
              <div class="text-17px text-[#333] font-600">
                知识点：
              </div>
              <div class="flex items-center flex-wrap">
                <div v-for="(item, index) in quesDetail?.knowledgePoints"
                     :key="index"
                     class="h-21px leading-[21px] rounded-[4px] bg-[#F3F4F9] px-11px text-13px text-[#6C6C74] mt-11px mr-11px"
                >
                  {{ item.sysKnowledgePointName }}
                </div>
                <div v-if="!quesDetail?.knowledgePoints?.length" class="text-[#b9b9b9] text-14px mt-11px h-21px">
                  暂无知识点
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="w-full bg-white rounded-[9px] p-17px mt-17px">
          <div class="text-17px text-[#333] font-600">
            订正记录
            <!-- <span class="font-400">（{{ correctionData?.correctiveNum || 0 }}次）</span> -->
          </div>
          <div v-if="correctionData?.subQuestionResultList?.length > 1" class="flex flex-wrap">
            <div
              v-for="(item, index) in correctionData?.subQuestionResultList"
              :key="index"
              class="mt-13px mr-11px w-32px h-32px rounded-[32px] border border-solid border-[#DDDDDD] text-center leading-[30px] text-14px text-[#333] van-haptics-feedback"
              :class="{
                '!text-[#6474FD] !border-[#6474FD] font-600': currentSubIndex === index,
              }"
              @click="handleSelect(index)"
            >
              {{ index + 1 }}
            </div>
          </div>
          <div v-for="(item, key) in currentSubQues?.subQuestionRecordList"
               :key="key"
               class="mt-13px bg-[#F3F4F9] rounded-[9px] p-17px"
          >
            <div class="w-full flex items-center justify-between text-[#333] mb-13px">
              <div class="flex items-center">
                <div class="flex items-baseline">
                  <div class="text-17px font-600">
                    作答结果
                  </div>
                  <div class="text-13px">
                    （学生自查）
                  </div>
                </div>
                <div class="flex items-center ml-18px">
                  <img
                    v-if="item.status === 1"
                    src="@/assets/img/question/info.png"
                    alt="info"
                    class="w-17px h-17px"
                  >
                  <img
                    v-else-if="item.isCorrect === 1"
                    src="@/assets/img/question/wrong.png"
                    alt="wrong"
                    class="w-17px h-17px"
                  >
                  <img
                    v-else-if="item.isCorrect === 2"
                    src="@/assets/img/question/half.png"
                    alt="half"
                    class="w-17px h-17px"
                  >
                  <img
                    v-else-if="item.isCorrect === 3"
                    src="@/assets/img/question/right.png"
                    alt="right"
                    class="w-17px h-17px"
                  >
                  <img
                    v-else
                    src="@/assets/img/question/not.png"
                    alt="not"
                    class="w-17px h-17px"
                  >
                  <div class="ml-6px text-19px font-600">
                    {{ getTitleText(item.status, item.isCorrect) }}
                  </div>
                </div>
              </div>
              <div class="text-[#666] text-15px flex items-center">
                <div>作答用时：{{ item.seconds || 0 }}秒</div>
                <div class="w-1px h-11px bg-[#CCCCCC] mx-12px"></div>
                <div>提交时间：{{ item.submitTime }}</div>
              </div>
            </div>
            <div class="text-15px text-[#6C6C74] flex items-center flex-wrap">
              <div v-if="item?.answerType === 1 || item?.answerType === 3" class="text-15px text-[#6C6C74] mt-13px">
                <g-mathjax :text="item?.answer || item?.keyboard || '未作答'" />
              </div>
              <template v-else-if="item?.answerType === 2">
                <div
                  v-for="(sub, subIndex) in item?.whiteBoard"
                  :key="subIndex"
                  class="mt-13px w-96px h-96px rounded-[4px] overflow-hidden relative"
                  :class="{
                    'ml-10px': subIndex !== 0,
                  }"
                  @click="previewImg(item?.whiteBoard, subIndex)"
                >
                  <img
                    src="@/assets/img/question/full.png"
                    alt="full"
                    class="absolute right-0 top-0 w-17px h-17px z-[2] pointer-events-none"
                  >
                  <img :src="sub"
                       alt="img cover"
                       class="w-full h-full object-cover"
                  >
                </div>
                <div v-if="!item?.whiteBoard?.length" class="text-15px text-[#ddd]">
                  没有答题数据
                </div>
              </template>
              <template v-else-if="item?.answerType === 4">
                <div
                  v-for="(sub, subIndex) in item?.image"
                  :key="subIndex"
                  class="mt-13px w-96px h-96px rounded-[4px] overflow-hidden relative"
                  :class="{
                    'ml-10px': subIndex !== 0,
                  }"
                  @click="previewImg(item?.image, subIndex)"
                >
                  <img
                    src="@/assets/img/question/full.png"
                    alt="full"
                    class="absolute right-0 top-0 w-17px h-17px z-[2] pointer-events-none"
                  >
                  <img :src="sub"
                       alt="img cover"
                       class="w-full h-full object-cover"
                  >
                </div>
                <div v-if="!item?.image?.length" class="text-15px text-[#ddd]">
                  没有答题数据
                </div>
              </template>
            </div>
          </div>
          <div v-if="!currentSubQues?.subQuestionRecordList?.length" class="w-full flex-cc h-200px">
            <g-empty description="没有订正数据"></g-empty>
          </div>
        </div>
      </template>
    </template>
    <!-- <QuestionPanel :ques-list="quesList" @select="onQuesSelect" /> -->
  </div>
</template>

<style lang="scss" scoped>
.arrow-left-icon{
  &::before{
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-left-color: #6474FD!important;
    border-bottom-color: #6474FD!important;
    transform: rotate(45deg);
  }
}
.arrow-right-icon{
  &::after{
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-right-color: #6474FD!important;
    border-bottom-color: #6474FD!important;
    transform: rotate(-45deg);
  }
}

:deep() {
  .ques-num,
  .text-option {
    margin-right: 4px;
    flex-shrink: 0;
  }
  .ques-row,
  .ques-text-row {
    display: flex;
    align-items: flex-start;
  }
}
</style>
