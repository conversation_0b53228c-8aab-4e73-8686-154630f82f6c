<script setup lang="ts">
import {
  getClassSubject,
  getClassType,
  getObjectType,
  getPublishTime,
  getSchoolClass,
  getTaskList,
  getTaskType,
} from '@/api/taskCenter'
import TaskItem from './TaskItem.vue'

const props = defineProps({
  subject: {
    type: [String, Number],
    default: '',
  },
  type: {
    type: [String, Number],
    default: -1,
  },
})

const isCreate = defineModel<boolean>('isCreate')
const classRef: any = $ref(null)
let classTypeList: any = $ref([]) // 班级类型列表
let currentClassType: any = $ref('') // 当前班级类型
let subjectList: any = $ref([]) // 学科列表
let currentSubject: any = $ref('') // 当前学科
let schoolClassList: any = $ref([]) // 班级列表
let currentSchoolClass: any = $ref(-1) // 当前班级
let objectTypeList: any = $ref([]) // 布置对象列表
let currentType: any = $ref(-1) // 当前布置对象
let taskTypeList: any = $ref([]) // 任务类型列表
let currentTaskType: any = $ref(-1) // 当前任务类型
let publishTimeList: any = $ref([]) // 布置时间列表
let currentPublishTime: any = $ref(-1) // 当前布置时间
let publishTime: any = $ref([]) // 具体布置时间
const pickerRef: any = $ref(null)
let isShowPicker = $ref(false)
let sortValue = $ref(1)
let isHidden = $ref(true) // 是否隐藏年级班级多余行数
let oneLine = $ref(true) // 年级班级是否为一行

let loading = $ref(true)
let taskList: any = $ref([])
let pageOption = $ref({
  page: 1,
  page_size: $g.isFlutter ? 9 : 16,
  total: 0,
})

function toCreate() {
  isCreate.value = true
}

// 获取班级类型
async function getClassTypeApi() {
  try {
    const res = await getClassType()
    classTypeList = res
    currentClassType = res?.[0]?.classType
    getClassSubjectApi()
  }
  catch (error) {
    loading = false
    console.log('⚡[ error ] >', error)
  }
}

// 获取老师教学学科
async function getClassSubjectApi() {
  try {
    if (!classTypeList.length) {
      taskList = []
      loading = false
      return
    }
    const res = await getClassSubject({ classType: currentClassType })
    subjectList = res
    currentSubject = res?.[0]?.sysSubjectId
    getSchoolClassApi()
  }
  catch (error) {
    loading = false
    console.log('⚡[ error ] >', error)
  }
}

// 获取老师教学班级
async function getSchoolClassApi() {
  try {
    if (!subjectList.length) {
      taskList = []
      loading = false
      return
    }
    const res = await getSchoolClass({
      classType: currentClassType,
      sysSubjectId: currentSubject,
    })
    schoolClassList = res
    schoolClassList.unshift({
      schoolClassId: -1,
      className: '全部',
    })
    schoolClassList = schoolClassList.map((item) => {
      item.showTitle = `${item?.sysGradeName || ''}${item.className}`
      return item
    })
    currentSchoolClass = -1
    classBeyond()
    initPage()
  }
  catch (error) {
    loading = false
    console.log('⚡[ error ] >', error)
  }
}

// 年级班级是否超出一行
function classBeyond() {
  nextTick(() => {
    if (classRef && classRef.scrollHeight > classRef.clientHeight)
      oneLine = false

    else
      oneLine = true
  })
}

useEventListener(window, 'resize', classBeyond)

// 获取布置对象
async function getObjectTypeApi() {
  const res = await getObjectType()
  objectTypeList = res
  objectTypeList.unshift({
    id: -1,
    title: '全部',
  })
}

// 获取任务类型
async function getTaskTypeApi() {
  const res = await getTaskType()
  taskTypeList = res
  taskTypeList.unshift({
    id: -1,
    title: '全部',
  })
  currentTaskType = -1
}

// 获取布置时间
async function getPublishTimeApi() {
  try {
    const res = await getPublishTime()
    publishTimeList = res
    currentPublishTime = res[0].id
    publishTime[0] = res[0].beginTime
    publishTime[1] = res[0].endTime
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
}

// 更多
async function timeMore() {
  isShowPicker = true
  await nextTick()
  if (pickerRef)
    pickerRef?.focus()
}

// 布置时间切换
function timeChange(index, item) {
  if (!publishTime)
    publishTime = []

  publishTime[0] = item.beginTime
  publishTime[1] = item.endTime
  initPage()
}

// 日期选择
function dateChoose() {
  if (!publishTime)
    currentPublishTime = publishTimeList[0].id

  else
    currentPublishTime = null

  initPage()
}

async function getTaskListApi(addToEnd?: boolean) {
  try {
    if (!schoolClassList.length) {
      taskList = []
      return
    }
    const {
      list,
      total,
    } = await getTaskList({
      classType: currentClassType,
      taskType: currentTaskType,
      arrangeObjectType: currentType,
      arrangeTime: currentPublishTime,
      beginTime: publishTime?.[0] || null,
      endTime: publishTime?.[1] || null,
      schoolClassId: currentSchoolClass,
      sysSubjectId: currentSubject,
      createTimeSort: sortValue == 1 ? 'DESC' : null,
      requireCompleteTimeSort: sortValue == 2 ? 'DESC' : null,
      page: pageOption.page,
      pageSize: pageOption.page_size,
    })
    pageOption.total = total
    taskList = addToEnd ? [...taskList, ...list] : list
  }
  catch (error) {
    taskList = []
    console.log('⚡[ error ] >', error)
  }
  finally {
    loading = false
  }
}

// 更新列表数据
async function updateList(obj) {
  const {
    id,
    isDelete,
  } = obj
  let page = 1
  let pageSize = pageOption.page * pageOption.page_size

  // 删除任务时更新列表
  if (isDelete) {
    const itemIndex = taskList.findIndex(item => item.taskId === id)
    taskList.splice(itemIndex, 1)
    page = pageOption.page
    pageSize = 16
  }

  if (!schoolClassList.length) return
  // 获取最新任务列表
  const { list } = await getTaskList({
    taskType: currentTaskType,
    arrangeObjectType: currentType,
    arrangeTime: currentPublishTime,
    beginTime: publishTime?.[0] || null,
    endTime: publishTime?.[1] || null,
    schoolClassId: currentSchoolClass,
    sysSubjectId: currentSubject,
    createTimeSort: sortValue == 1 ? 'DESC' : null,
    requireCompleteTimeSort: sortValue == 2 ? 'DESC' : null,
    page,
    pageSize,
  })

  // 删除任务后补充列表
  if (isDelete) {
    if (!list.length) return

    const lastItem = list[list.length - 1]
    const exists = taskList.some(item => item.taskId === lastItem.taskId)

    if (!exists)
      taskList.push(lastItem)

    return
  }

  // 更新单个任务数据
  const updatedItem = list.find(item => item.taskId === id)
  const itemIndex = taskList.findIndex(item => item.taskId === id)
  taskList[itemIndex] = updatedItem
}

function pullup() {
  pageOption.page++
  getTaskListApi(true)
}

// 页码重置为1请求列表
function initPage() {
  pageOption.page = 1
  taskList = []
  loading = true
  getTaskListApi()
}

// 排序切换
function handleCommand(command) {
  sortValue = command
  initPage()
}

onBeforeMount(() => {
  getObjectTypeApi()
  getTaskTypeApi()
  getPublishTimeApi()
  getClassTypeApi()
})

const noRefresh = $ref(['/teacher/taskCenter/report/resourceReport', '/teacher/groupManage'])

onActivated(() => {
  let forwardUrl = window.history?.state?.forward?.split('?')[0] || ''
  if (!noRefresh.includes(forwardUrl) || !forwardUrl)
    initPage()
})
</script>

<template>
  <div>
    <div class="mb-17px flex items-center">
      <div
        class="w-21px h-21px bg-white br-[50%] mr-11px -mt-[2px] flex-cc cursor-pointer"
        @click="toCreate"
      >
        <img
          src="@/assets/img/taskCenter/left-back.png"
          class="w-13px h-13px"
        />
      </div>
      <span class="text-19px font-600 leading-[29px]">已布置任务</span>
    </div>
    <div class="mb-30px">
      <!-- 班级类型 -->
      <div class="flex mb-21px items-center h-25px">
        <div class="w-70px text-right text-[#636772] text-14px flex-shrink-0">
          班级类型：
        </div>
        <g-radio
          v-model="currentClassType"
          :option="classTypeList"
          :replace-keys="{ id: 'classType', name: 'classTypeName' }"
          item-class="px-9px py-6px"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="getClassSubjectApi"
        />
      </div>
      <!-- 学科 -->
      <div class="flex mb-21px items-center h-25px">
        <div class="w-70px text-right text-[#636772] text-14px flex-shrink-0">
          学科：
        </div>
        <g-radio
          v-model="currentSubject"
          :option="subjectList"
          :replace-keys="{ id: 'sysSubjectId', name: 'sysSubjectName' }"
          item-class="px-9px py-6px"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="getSchoolClassApi"
        />
      </div>
      <!-- 年级班级 -->
      <div
        ref="classRef"
        class="flex mb-15px overflow-hidden"
        :class="isHidden ? 'h-25px mb-21px' : 'min-h-0px mb-15px'"
      >
        <div
          class="w-70px text-right text-[#636772] text-14px flex-shrink-0 leading-[25px]"
        >
          年级班级：
        </div>
        <g-radio
          v-model="currentSchoolClass"
          :option="schoolClassList"
          :replace-keys="{ id: 'schoolClassId', name: 'showTitle' }"
          item-class="px-9px py-6px mb-6px"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="initPage"
        />
        <div
          v-if="!oneLine"
          class="flex-shrink-0 cursor-pointer"
          @click="isHidden = !isHidden"
        >
          <img
            src="@/assets/img/taskCenter/left-back.png"
            class="w-26px h-26px mr-10px"
            :class="!isHidden ? 'rotate-90' : '-rotate-90'"
            alt="more"
          />
        </div>
      </div>
      <!-- 任务类型 -->
      <div class="flex mb-21px items-center h-25px">
        <div class="w-70px text-right text-[#636772] text-14px flex-shrink-0">
          任务类型：
        </div>
        <g-radio
          v-model="currentTaskType"
          :option="taskTypeList"
          :replace-keys="{ id: 'id', name: 'title' }"
          item-class="px-9px py-6px"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="initPage"
        />
      </div>
      <!-- 布置时间 -->
      <div class="flex mb-21px items-center h-25px">
        <div class="w-70px text-right text-[#636772] text-14px flex-shrink-0">
          布置时间：
        </div>
        <g-radio
          v-model="currentPublishTime"
          :option="publishTimeList"
          :replace-keys="{ id: 'id', name: 'title' }"
          item-class="px-9px py-6px"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="timeChange"
        />
        <div
          v-if="!isShowPicker"
          class="flex items-center cursor-pointer"
          @click="timeMore"
        >
          <div class="text-13px">
            更多
          </div>
          <img
            :src="$g.tool.getFileUrl('taskCenter/more.png')"
            class="w-14px h-14px mt-[-2px]"
            alt="dmore"
          />
        </div>
        <div v-else class="w-[240px] h-25px">
          <el-date-picker
            ref="pickerRef"
            v-model="publishTime"
            class="-mt-[4px]"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="dateChoose"
          />
        </div>
      </div>
      <!-- 布置对象 -->
      <div class="flex items-center h-25px">
        <div class="w-70px text-right text-[#636772] text-14px flex-shrink-0">
          布置对象：
        </div>
        <g-radio
          v-model="currentType"
          :option="objectTypeList"
          :replace-keys="{ id: 'id', name: 'title' }"
          item-class="px-9px py-6px"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="initPage"
        />
      </div>
    </div>
    <div class="flex items-center justify-between">
      <span class="mr-11px text-17px font-600">任务列表</span>
      <div class="cursor-pointer flex items-center text-13px">
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="el-dropdown-link flex items-center">
            <span>
              {{
                sortValue == 1 ? '按布置时间排序' : '按任务到期时间排序'
              }}
            </span><img
              :src="$g.tool.getFileUrl('taskCenter/more.png')"
              class="w-12px h-12px -mt-4px ml-2px rotate-90"
              alt="more"
            />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="1">
                按布置时间排序
              </el-dropdown-item>
              <el-dropdown-item command="2">
                按任务到期时间排序
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <g-list
      v-model:data="taskList"
      :show-loading="loading"
      :page-option="pageOption"
      :pulldown="false"
      @pullup="pullup"
    >
      <div
        v-if="taskList.length"
        class="w-full grid gap-x-[19px] gap-y-[16px] justify-between mt-18px"
        :class="!$g.isPC ? 'grid-cols-3' : 'grid-cols-4'"
      >
        <TaskItem
          v-for="item in taskList"
          :key="item.taskId"
          :data="item"
          @get-list-api="updateList"
        ></TaskItem>
      </div>
    </g-list>
  </div>
</template>

<style lang="scss" scoped></style>
