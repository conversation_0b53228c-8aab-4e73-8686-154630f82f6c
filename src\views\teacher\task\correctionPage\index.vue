<script setup lang="ts" name="CorrectionPage">
import { getStudentAnswerDetail, getStudentList } from '@/api/correctionPage'
import AnswerCorrect from './components/AnswerCorrect.vue'
import CorrectedAnswer from './components/CorrectedAnswer.vue'
import ImageCorrect from './components/ImageCorrect.vue'
import QuestionCheckout from './components/QuestionCheckout.vue'
import { AnswerType, TeacherCorrectStatus } from './type'
/**
 * 批改页面
 * @param {string} taskId 任务id
 * @param {string} exerciseTaskId 练习id（可选）
 * @param {string} studentId 默认选中的学生ID
 * @param {string} questionId 默认选中的大题ID
 */
const route = useRoute()
const router = useRouter()
//  学生列表
const studentList = ref<any[]>([])
//  根据状态归类以后的学生列表, 生成
const formatStudentList = computed(() => studentList.value.reduce((acc, item) => {
  acc[item.teacherCorrectStatus - 1].list.push(item)
  return acc
}, [
  {
    status: '未批改',
    list: [],
    color: '#F2494AFF',
  },
  {
    status: '批改中',
    list: [],
    color: '#3398F7FF',
  },
  {
    status: '已批改',
    list: [],
    color: '#00CF6AFF',
  },
]))

//  当前激活的学生,从本地缓存中获取，如果路由携带，优先取路由上的学生
const activeStudentId = useStorage<number>(`correction_task_${route.query.taskId}`, 0)
if (route.query.studentId)
  activeStudentId.value = Number(route.query.studentId)

// 获取学生列表loading
const fetchStudentLoading = ref(true)

// 大题列表
const questionList = ref<any[]>([])
// 激活的大题id，默认取路由上面的id
const activeQuestionId = ref<number>(Number(route.query.questionId || 0) || 0)

const ImageCorrectRef = useTemplateRef<typeof ImageCorrect>('ImageCorrectRef')

// 教师批改状态
const teacherCorrectStatusMap = {
  [TeacherCorrectStatus.UNCORRECTED]: {
    text: '未批改',
    class: 'from-[#FF5252]  via-[#FFABAB] ',
  },
  [TeacherCorrectStatus.CORRECTING]: {
    text: '批改中',
    class: 'from-[#FF7D29]  via-[#FFB453] ',
  },
  [TeacherCorrectStatus.CORRECTED]: {
    text: '已批改',
    class: 'from-[#07CF4C]  via-[#75E590]',
  },
}

/** 获取学生列表 */
async function fetchStudentList(isInit = true) {
  try {
    isInit && (fetchStudentLoading.value = true)
    studentList.value = await getStudentList({
      taskId: route.query.taskId,
      exerciseTaskId: route.query?.exerciseTaskId,
    }) || []
    // 如果当前没有激活的学生，或者激活的学生不在学生列表中，则取学生列表的第一个
    if (!activeStudentId.value || !studentList.value.find(item => item.schoolStudentId == activeStudentId.value))
      activeStudentId.value = studentList.value[0].schoolStudentId

    questionList.value = studentList.value.find(item => item.schoolStudentId == activeStudentId.value)?.questionList || []
    // 如果当前没有激活的大题，或者激活的大题不在大题列表中，则取大题列表的第一个
    if (!activeQuestionId.value || !questionList.value.find(item => item.questionId == activeQuestionId.value))
      activeQuestionId.value = questionList.value[0]?.questionId
  }
  catch (err) {
    console.log('加载学生数据失败', err)
  }
  finally {
    fetchStudentLoading.value = false
    // 把当前选中的学生滚动到视口
    await nextTick()
    const studentItem = document.getElementById(`student_${activeStudentId.value}`)
    studentItem && studentItem.scrollIntoView({ block: 'center' })
    console.log(formatStudentList.value)
  }
}
/** 刷新学生批改的状态 */
async function refreshStudentStatus() {
  studentList.value = await getStudentList({
    taskId: route.query.taskId,
    exerciseTaskId: route.query?.exerciseTaskId,
  }) || []
}

// 学生作答数据列表
const studentAnswerDetailList = ref<any[]>([])
// 获取学生作答+批改详情loading
const fetchStudentAnswerDetailLoading = ref(false)
// 当前选择的小问ID
const activeSubQuestionId = ref<number>(0)
// 当前小问的作答+批改详情
const activeStudentAnswerDetail = computed(() => studentAnswerDetailList.value.find(item => item.subQuestionId == activeSubQuestionId.value))
/** 获取学生作答+批改详情 */
async function fetchStudentAnswerDetail() {
  try {
    fetchStudentAnswerDetailLoading.value = true
    // 获取当前学生当前大题的做题id
    let exerciseId = questionList.value.find(item => item.questionId == activeQuestionId.value)?.exerciseId
    if (!exerciseId)
      return

    let res = await getStudentAnswerDetail({ exerciseId })

    studentAnswerDetailList.value = res?.subQuestionResultList?.filter(item => item.subjectiveItem).map(processAnswerDetail)

    if (!studentAnswerDetailList.value)
      throw new Error('暂无作答数据')

    activeSubQuestionId.value = studentAnswerDetailList.value[0]?.subQuestionId
    fetchStudentAnswerDetailLoading.value = false
  }
  catch (err) {
    console.log('获取学生作答+批改详情失败', err)
    studentAnswerDetailList.value = []
    if (err !== '取消请求')
      fetchStudentAnswerDetailLoading.value = false
  }
}

/** 处理图片数据，手动处理formatImageList字段用于本地存储canvas数据 */
function processAnswerDetail(item: any) {
  const {
    answerType,
    whiteBoard,
    image,
  } = item
  if (answerType === AnswerType.SUBJECTIVE_KEYBOARD) return item
  // 学生原答案
  let stuArr = answerType === AnswerType.SUBJECTIVE_CANVAS ? whiteBoard : image
  let formatImageList = stuArr?.map((originUrl, index) => {
    return {
      index,
      exerciseSubId: item.exerciseSubId,
      subQuestionId: item.subQuestionId,
      // 学生原作答图片
      originUrl,
      // 教师批改后图片
      commentUrl: item.comments?.[index] || originUrl,
      // 缩略图上显示的图片
      thumbnailUrl: item.comments?.[index] || originUrl,
      noteData: null,
    }
  })
  return {
    ...item,
    formatImageList,
  }
}

/** 切换学生 */
async function handleStudentChange(studentId: number) {
  if (activeStudentId.value === studentId)
    return

  try {
    await ImageCorrectRef.value?.saveCanvasData?.()

    activeStudentId.value = studentId
    questionList.value = studentList.value.find(item => item.schoolStudentId == activeStudentId.value)?.questionList || []
    activeQuestionId.value = questionList.value[0]?.questionId
    fetchStudentAnswerDetail()
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
}

/** 切换大题 */
async function handleQuestionChange(questionId: number) {
  try {
    await ImageCorrectRef.value?.saveCanvasData?.()
    activeQuestionId.value = questionId
    fetchStudentAnswerDetail()
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
}

/** 切换小问 */
async function handleSubQuestionChange(subQuestionId: number) {
  if (activeSubQuestionId.value === subQuestionId)
    return

  try {
    await ImageCorrectRef.value?.saveCanvasData?.()
    activeSubQuestionId.value = subQuestionId
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
}

/** 保存并退出 */
async function handleSaveAndExit() {
  try {
    await ImageCorrectRef.value?.saveCanvasData?.()
    router.back()
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
}

onMounted(async () => {
  await fetchStudentList()
  fetchStudentAnswerDetail()
})
</script>

<template>
  <div class="correction-page-container">
    <g-navbar title="保存并退出" @back="handleSaveAndExit">
      <template #right>
        <QuestionCheckout
          v-if="questionList.length"
          :question-list="questionList"
          :active-question-id="activeQuestionId"
          @change-question="handleQuestionChange"
        />
      </template>
    </g-navbar>

    <div class="flex-1 overflow-hidden flex mt-16px">
      <g-loading v-if="fetchStudentLoading" class="h-200px m-auto" />

      <g-empty
        v-if="!fetchStudentLoading && !studentList.length"
        class="h-200px m-auto"
        description="暂无可批改学生"
      />

      <template v-if="!fetchStudentLoading && studentList.length">
        <div class="flex-1 overflow-hidden flex bg-white rounded-[6px]">
          <!-- 学生列表 -->
          <div class="border-r border-[#E8E8E8] py-13px">
            <div class="h-full overflow-auto px-13px">
              <div v-for="listItem in formatStudentList"
                   :key="listItem.status"
                   class="mb-13px"
              >
                <div class="text-13px leading-[18px] mb-4px" :style="{ color: listItem.color }">
                  {{ listItem.status }}{{ listItem.list.length }}人
                </div>
                <div
                  v-for="item in listItem.list"
                  :id="`student_${item.schoolStudentId}`"
                  :key="item.schoolStudentId"
                  class="py-10px rounded-[6px] w-94px text-16px font-500 leading-[22px] cursor-pointer flex items-center"
                  :class="{ '!bg-[#6474FD24]': activeStudentId == item.schoolStudentId }"
                  @click="handleStudentChange(item.schoolStudentId)"
                >
                  <span class="w-5px h-5px rounded-full mr-4px ml-13px" :style="{ backgroundColor: listItem.color }" />
                  <span class="flex-1 overflow-hidden truncate text-16px leading-[16px]">{{ item.studentName }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="flex-1 overflow-hidden flex flex-col">
            <g-loading v-if="fetchStudentAnswerDetailLoading" class="h-200px m-auto" />
            <g-empty v-if="!fetchStudentAnswerDetailLoading && !studentAnswerDetailList.length"
                     class="h-200px m-auto"
                     description="暂无作答数据"
            />
            <template v-if="!fetchStudentAnswerDetailLoading && studentAnswerDetailList.length">
              <!-- 小问切换 -->
              <div v-if="studentAnswerDetailList.length > 1"
                   class="border-b  border-[#E8E8E8]  px-16px pt-16px flex overflow-x-auto myContainer"
                   :class="!$g.isPC && 'no-bar'"
              >
                <div
                  v-for="item in studentAnswerDetailList"
                  :key="item.subQuestionId"
                  class="text-[#6C6C74FF] border-b-[2px] border-[transparent] pb-8px mr-20px cursor-pointer flex-shrink-0"
                  :class="{ '!border-[#6474FDFF] !font-600': activeSubQuestionId == item.subQuestionId }"
                  @click="handleSubQuestionChange(item.subQuestionId)"
                >
                  {{ item.qnum }}
                </div>
              </div>

              <!-- 小问作答回显 -->
              <div class="flex-1 overflow-hidden p-16px">
                <!-- 键盘作答回显 -->
                <span v-if="activeStudentAnswerDetail.answerType === AnswerType.SUBJECTIVE_KEYBOARD">
                  {{ activeStudentAnswerDetail.keyboard || '暂无作答数据' }}
                </span>
                <!-- 拍照和白板作答 -->
                <template v-else>
                  <ImageCorrect
                    v-if="activeStudentAnswerDetail.formatImageList?.length"
                    ref="ImageCorrectRef"
                    :img-list="activeStudentAnswerDetail.formatImageList"
                  />
                  <span v-else>暂无作答数据</span>
                </template>
              </div>
            </template>
          </div>
        </div>

        <div class="w-[34%] flex flex-col ml-16px">
          <!-- 正确答案以及解析 -->
          <CorrectedAnswer
            :question-id-list="questionList.map(item => item.questionId)"
            :active-question-id="activeQuestionId"
          />
          <!-- 小问批改 -->
          <AnswerCorrect
            v-loading="fetchStudentAnswerDetailLoading"
            :answer-list="studentAnswerDetailList"
            :active-sub-question-id="activeSubQuestionId"
            @correct-status-change="fetchStudentList(false)"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.correction-page-container {
  @apply p-26px flex flex-col h-screen  overflow-hidden bg-[#F3F4F9FF];
  width: 100%; /* apply-without-convert */
}

.myContainer{
 &::-webkit-scrollbar-thumb {
    background-color: #e8e8e8;
  }
}
</style>
