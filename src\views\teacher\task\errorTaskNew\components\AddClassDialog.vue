<script setup lang="ts">
const props = defineProps({
  classList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})
const emit = defineEmits(['addClass'])
let dialogVisible = defineModel<any>('visible', { required: true })
let currentClass = $ref<any>([])
function getStatus(item) {
  return currentClass.map(v => v.schoolClassId).includes(item.schoolClassId)
}
function handleAddClass(item) {
  if (getStatus(item))
    currentClass = currentClass.filter(v => v.schoolClassId !== item.schoolClassId)

  else
    currentClass.push(item)
}
function handleConfirm() {
  emit('addClass', currentClass)
  closeDialog()
}
function closeDialog() {
  currentClass = []
  dialogVisible.value = false
}
</script>

<template>
  <van-popup
    v-model:show="dialogVisible"
    position="center"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="h-[269px] flex flex-col p-21px overflow-hidden bg-[#fff] rounded-[8px]"
    teleport="#app"
    v-bind="$attrs"
  >
    <div class="h-full w-[395px] ">
      <div class="text-[17px] text-[#333] font-600 text-center relative">
        添加班级
        <svg-menu-dialog-close
          class="absolute top-0 bottom-0 my-auto right-0 w-15px h-15px cursor-pointer"
          @click="closeDialog"
        />
      </div>
      <div class="h-[calc(100%-60px)] overflow-y-auto no-bar mt-17px">
        <div
          v-for="(item, index) in classList"
          :key="item.schoolClassId"
          :class="{ 'border-t-[1px]': index === 0 }"
          class="px-48px py-11px text-[14px] border-b-[1px]  border-solid border-[#E8E8E8] mb-10px flex justify-between"
        >
          <div :class="getStatus(item) ? 'class-item-active' : ''">
            {{ item.className }}
          </div>
          <svg-menu-radio v-if="!getStatus(item)"
                          class="w-17px h-17px cursor-pointer"
                          @click="handleAddClass(item)"
          />
          <svg-menu-radio-clicked v-else
                                  class="w-17px h-17px cursor-pointer"
                                  @click="handleAddClass(item)"
          />
        </div>
      </div>
      <div class="flex justify-center">
        <div class="bg-[#6474FD] text-[#fff] rounded-[4px] px-22px py-4px cursor-pointer" @click="handleConfirm">
          确定
        </div>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.class-item-active{
  color: #6474FD;
}
</style>
