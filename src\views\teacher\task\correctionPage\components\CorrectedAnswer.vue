<script setup lang="ts">
import { getQuestionList } from '@/api/correctionPage'

const props = defineProps({
  questionIdList: {
    type: Array,
    required: true,
  },
  activeQuestionId: {
    type: Number,
    required: true,
  },
})
// 题目列表
let questionList = ref<any[]>([])
// 获取题目列表loading
let fetchQuestionListLoading = ref(false)
// 是否折叠
let isFold = $ref(true)
// 正确答案解析容器
let answerRef = useTemplateRef('answerRef')

// 当前激活的题目
let questionItem = computed(() => {
  return questionList.value.find(item => item.questionId === props.activeQuestionId) || null
})

watch(() => props.questionIdList, (newVal, oldVal) => {
  if (newVal.length && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    // 请求题目列表
    fetchQuestionList()
  }
}, { immediate: true })

watch(() => props.activeQuestionId, () => {
  answerRef.value?.scrollTo(0, 0)
  isFold = true
  nextTick($g.tool.renderMathjax)
}, { immediate: true })

/** 获取题目列表数据 */
async function fetchQuestionList() {
  try {
    fetchQuestionListLoading.value = true
    questionList.value = await getQuestionList({ questionIdList: props.questionIdList }) || []
  }
  catch (err) {
    console.log('获取题目列表数据失败', err)
  }
  finally {
    fetchQuestionListLoading.value = false
  }
}

function handleFold() {
  isFold = !isFold
  if (!isFold) {
    // 展开动画结束以后再渲染公式
    setTimeout($g.tool.renderMathjax, 200)
  }
}
</script>

<template>
  <div class="mb-16px bg-white rounded-[6px] p-17px transition-all duration-200 flex flex-col" :class="[isFold ? 'h-[58px]' : 'h-[300px]']">
    <div class="flex-shrink-0 flex justify-between items-center mb-11px select-none">
      <span class="font-600 text-17px h-24px lh-[24px]">正确答案及解析</span>
      <div class="flex items-center cursor-pointer" @click="handleFold">
        <span class="mr-6px text-[#6474FD] h-21px lh-[21px]">{{ isFold ? '展开' : '收起' }}</span>
        <img :src="isFold ? $g.tool.getFileUrl('taskCenter/blueBottom.png') : $g.tool.getFileUrl('taskCenter/blueTop.png')"
             alt=""
             class="w-15px h-9px"
        >
      </div>
    </div>
    <div v-if="!isFold "
         ref="answerRef"
         class="overflow-auto flex-1 corrected-answer-container"
    >
      <g-empty v-if="!questionItem" />
      <template v-else>
        <!-- 正确答案 -->
        <div class="text-[#999999FF] mb-9px">
          正确答案
        </div>
        <div v-for="(item, index) in questionItem.subQuestions"
             :key="item.subQuestionId"
             class="mb-9px flex items-start"
        >
          <span v-if="questionItem.subQuestions.length > 1" class="mr-4px">({{ index + 1 }})</span>
          <g-mathjax :text="item.subQuestionAnswer" class="!text-15px" />
        </div>

        <!-- 解析 -->
        <div class="text-[#999999FF] mb-9px">
          解析
        </div>
        <div v-for="(item, index) in questionItem.subQuestions"
             :key="item.subQuestionId"
             class="mb-9px flex items-start"
        >
          <span v-if="questionItem.subQuestions.length > 1" class="mr-4px">({{ index + 1 }})</span>
          <div>
            <div v-for="(parseItem, parseIndex) in item.subQuestionParseList"
                 :key="parseItem.subQuestionParseId"
                 class="mb-9px flex items-start"
            >
              <span v-if="item.subQuestionParseList.length > 1" class="mr-2px">{{ parseIndex + 1 }}.</span>
              <g-mathjax :text="parseItem.content" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.corrected-answer-container {

  &::-webkit-scrollbar-thumb {
    background-color: #e8e8e8;
  }
  }
</style>
