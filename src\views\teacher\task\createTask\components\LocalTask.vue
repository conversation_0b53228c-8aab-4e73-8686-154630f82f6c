<script setup lang="ts">
import {
  addFolder,
  deleteFolder as deleteFolder<PERSON><PERSON>,
  deleteResource as deleteResource<PERSON><PERSON>,
  getFolderList,
  getTaskResourceTeacherUploadList,
  sortFolder,
  uploadResource,
} from '@/api/comprehensiveTask'
import Draggable from 'vuedraggable'
import { getFileTypeUrl } from '../tool'
import PreviewFile from './PreviewFile.vue'

let props = defineProps({
  addBtnDisabled: {
    type: Boolean,
    default: false,
  },
})
const route = useRoute()

let videoType = ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8']
let folderList = $ref<Array<{
  folderName: string
  folderId: string
}>>([]) // 文件夹列表
let fileList = defineModel<any>('fileList')
let folderStatus = $ref<string>('normal') // 文件管理状态 normal edit
let taskModel = inject<Ref<any>>('taskModel', ref(null))
let activeFolder = $ref<string>('') // 选中的文件夹
let showDialog = $ref<boolean>(false) // 新增文件夹弹框
let showUploadDialog = $ref<boolean>(false) // 上传资源弹框
let tableDeleteStatus = $ref<string>('normal') // 资源table状态 normal delete

let uploadFileList = $ref<any>([])
let currentFile = $ref<any>(null)
let showFile = $ref(false)
const formRef = $ref<any>(null)
let uploadBtnLoading = $ref<boolean>(false)
let uploadRef = $ref<any>(null)
const formData = reactive<any>({
  folderName: '',
})
const rules = reactive<any>({
  folderName: [{
    required: true,
    message: '请输入文件名',
    trigger: 'blur',
  }],
})
// 表格配置
let tableOptions = $ref<any>({
  ref: null as any,
  key: 'taskResourceTeacherUploadId',
  loading: true,
  pageOptions: {
    page: 1,
    pageSize: 5,
    total: 0,
  },
  column: [
    {
      label: '资源名称',
      prop: 'fileName',
      slot: true,
    },
    {
      label: '类型',
      prop: 'fileExtension',
    },
    {
      label: '上传日期',
      prop: 'createTime',
    },
    {
      label: '操作',
      prop: 'cz',
      slot: true,
    },
  ],
  data: [],
})
let deleteTableOptions = $ref({
  ref: null as any,
  key: 'taskResourceTeacherUploadId',
  loading: false,
  pageOptions: {
    page: 1,
    pageSize: 5,
    total: 0,
  },
  column: [
    { type: 'selection' },
    {
      label: '资源名称',
      prop: 'fileName',
      slot: true,
    },
    {
      label: '类型',
      prop: 'fileExtension',
    },
    {
      label: '上传日期',
      prop: 'createTime',
    },
    {
      label: '操作',
      prop: 'cz',
      slot: true,
    },
  ],
  data: [],
  chooseTableIds: [],
})
let isJoin = $computed(() => {
  return (row) => {
    return fileList.value.find(it => it.taskResourceTeacherUploadId == row.taskResourceTeacherUploadId)
  }
})

// 加入资源按钮disabled
let btnDisabled = $computed(() => {
  return (row) => {
    if (taskModel.value)
      return Number(taskModel.value) != 1 && videoType.includes(row.fileExtension)

    else
      return false
  }
})
/* 上传成功 */
async function uploadSuccess() {
  if (!uploadFileList?.length) {
    $g.msg('上传资源不能为空', 'warning')
    return
  }
  let params = {
    taskResourceTeacherDirId: activeFolder,
    taskResourceFileList: uploadFileList?.map((it) => {
      let type = `${it?.name?.split('.')?.pop()?.toLowerCase()}`
      return {
        fileName: it?.name,
        fileAbsoluteUrl: it?.fullUrl,
        fileSize: it?.size,
        fileExtension: type,
        fileAbsoluteUrlM3u8: type == 'm3u8' ? it?.fullUrl : '',
        fileDuration: it?.duration ?? null,
        fileCoverUrl: '',
        sysCourseId: route?.query?.subjectId,
        resourceType: videoType.includes(type) ? 1 : 2,
      }
    }),
  }
  try {
    await uploadResource(params)
    $g.msg('上传资源成功')
    showUploadDialog = false
    getTaskResourceTeacherUploadListApi()
  }
  catch (error) {
    console.log(error)
  }
}

// 新增文件夹
function openAddFolder() {
  showDialog = true
}
/* 编辑文件夹 */
function edit() {
  folderStatus = folderStatus == 'normal' ? 'edit' : 'normal'
}
/* 切换文件夹 */
async function changeActive(folder) {
  activeFolder = folder?.folderId ?? ''
  await getTaskResourceTeacherUploadListApi()
}
/* 删除文件夹 */
function deleteFolder(folder) {
  $g.confirm({
    content: '确定删除文件夹吗？',
  }).then(async () => {
    try {
      await deleteFolderApi({ taskResourceTeacherDirId: folder.taskResourceTeacherDirId })
      $g.msg('删除文件成功')
      await getFolderListApi()
      if (!folderList.length) {
        activeFolder = ''
        tableOptions.pageOptions.page = 1
        tableOptions.data = []
        tableOptions.pageOptions.total = 0
        deleteTableOptions.data = []
        deleteTableOptions.pageOptions.total = 0
      }
      else if (activeFolder == folder.taskResourceTeacherDirId) {
        activeFolder = folderList?.[0]?.folderId ?? ''
        tableOptions.pageOptions.page = 1
        await getTaskResourceTeacherUploadListApi()
      }
    }
    catch (error) {
      console.log('error => ', error)
    }
  }).catch((err) => {
      console.log('error =>', err)
    })
}
/* 拖拽结束 */
function dragEnd() {
  sortFolder({
    taskResourceTeacherDirIdList: folderList.map(it => it?.folderId),
  })
}

/* 关闭新增文件夹 */
function closeDialog() {
  formData.folderName = ''
  formRef?.resetFields()
}
/* 新增文件夹 */
async function createFolder() {
  await formRef?.validate()
  await addFolder({ dirName: formData.folderName })
  showDialog = false
  await getFolderListApi()
  if (folderList?.length && !activeFolder) {
    activeFolder = folderList?.[0]?.folderId ?? ''
    await getTaskResourceTeacherUploadListApi()
  }
}

function deleteResource() {
  tableDeleteStatus = 'delete'
  deleteTableOptions.pageOptions = tableOptions.pageOptions
}
/* 删除资源 */
async function deleteFile() {
  $g.confirm({
    content: '确定删除资源吗？',
  }).then(async () => {
    let ids = deleteTableOptions?.chooseTableIds?.map((it: any) => it?.taskResourceTeacherUploadId)
    if (!ids.length) return
    // 删除资源时如有被删除的资源已被加入，需要从fileList中删除
    fileList.value = fileList.value.filter(it => !ids.includes(it?.taskResourceTeacherUploadId))
    await deleteResourceApi({ taskResourceTeacherUploadIds: ids })
    $g.msg('删除资源成功')
    tableDeleteStatus = 'normal'
    getTaskResourceTeacherUploadListApi()
  })
}

/* 预览 */
function handlePreview(row) {
  if (!disablePreview(row)) return
  if (
    videoType.includes(
      row.fileExtension,
    )
  ) {
    if ($g.isPC) {
      currentFile = row
      showFile = true
    }
    else {
      $g.flutter('previewVideo', {
        url: row.fileAbsoluteUrl,
      })
    }
  }
  else {
    currentFile = row
    showFile = true
  }
}
/* 根据文件类型禁用加入/预览 */
function disablePreview(row) {
  // 文档、图片、视频、音频
  return row.fileExtension && ['doc',
'docx',
'xls',
'xlsx',
'pdf',
'ppt',
'pptx',
'jpg',
'jpeg',
'png',
'gif',
'bmp',
'svg',
'mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8'].includes(row.fileExtension)
}
async function getFolderListApi() {
  try {
    const res = await getFolderList()
    folderList = res?.map(it => ({
      folderName: it?.dirName ?? '',
      folderId: it?.taskResourceTeacherDirId ?? '',
      ...it,
    }))
  }
  catch (error) {
    console.log(error)
  }
}
/* 获取资源列表 */
async function getTaskResourceTeacherUploadListApi() {
  if (!activeFolder) {
    tableOptions.loading = false
    return
  }
  try {
    deleteTableOptions.loading = true
    tableOptions.loading = true
    const res = await getTaskResourceTeacherUploadList({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.pageSize,
      taskResourceTeacherDirId: activeFolder,
    })
    let tempData = res?.list.map(it => ({
      ...it,
      disabled: false,
    })) ?? []
    let tempTotal = res?.total ?? 0
    tableOptions.pageOptions.total = tempTotal
    deleteTableOptions.pageOptions.total = tempTotal
    tableOptions.data = tempData
    deleteTableOptions.data = tempData
    tableOptions.loading = false
    deleteTableOptions.loading = false
  }
  catch (error) {
    console.log(error)
    tableOptions.loading = false
    deleteTableOptions.loading = false
  }
}
/* 选中数据 */
function selected(row, index, type = '') {
  if (!disablePreview(row)) return
  if (type == 'cancel')
    fileList.value = fileList.value.filter(it => it.taskResourceTeacherUploadId != row?.taskResourceTeacherUploadId)

  fileList.value.push(row)
}
/* 删除视频 */
function deleteUploadResource(item, index) {
  if (item.percentage != 100)
    uploadRef.handleRemove(item)

  uploadFileList.splice(index, 1)
}
/* 关闭上传弹框 */
function closeUploadDialog() {
  if (uploadFileList?.length) {
    uploadFileList.forEach((it) => {
      if (it.percentage != 100)
        uploadRef.handleRemove(it)
    })
  }
  uploadFileList = []
}

/* 上传按钮loading */
watch(() => uploadFileList, (val) => {
  uploadBtnLoading = val?.some(it => it.status == 'uploading')
}, {
  deep: true,
  immediate: true,
})

onMounted(async () => {
  await getFolderListApi()
  activeFolder = folderList?.[0]?.folderId ?? ''
  getTaskResourceTeacherUploadListApi()
})
</script>

<template>
  <div class="min-h-[450px] flex gap-[15px]">
    <div class="w-[291px] min-h-[450px]  bg-[#fff] rounded-[6px] border border-[#DADDE8] px-[11px] py-[13px] flex flex-col">
      <div class="w-full h-[51px] bg-[rgba(100,116,253,0.1)] rounded-[6px] flex justify-between items-center px-[11px] py-[16px]">
        <div class="font-[500] text-[14px] text-[#333] leading-[19px]">
          文件目录
        </div>
        <div class="text-[13px] text-[#6474FD] leading-[17px] cursor-pointer" @click="edit">
          {{ folderStatus == 'edit' ? '退出管理' : '管理' }}
        </div>
      </div>
      <div class="flex-1  my-[9px] overflow-auto">
        <div class="w-full h-[32px] ">
          <Draggable
            v-model="folderList"
            handle=".handle"
            item-key="folderId"
            ghost-class="ghost"
            :animation="300"
            :disabled="folderStatus !== 'edit'"
            transition="transform 0.3s ease"
            @end="dragEnd"
          >
            <template #item="{ element }">
              <div
                class="hover:bg-[rgba(100,116,253,0.05)] py-[6px] px-[19px] cursor-pointer mb-[3px]"
                :class="{ 'bg-[rgba(100,116,253,0.05)]': activeFolder == element.folderId }"
                @click="changeActive(element)"
              >
                <div class="flex items-center justify-between">
                  <div class="w-fit flex items-center">
                    <svg-task-folder class="w-15px h-15px mr-[6px] cursor-pointer" />
                    <span class="max-w-[153px] overflow-hidden text-ellipsis whitespace-nowrap text-[14px] leading-[19px] select-none" :title="element.folderName">{{ element.folderName }}</span>
                  </div>
                  <div v-if="folderStatus == 'edit'" class="flex items-center">
                    <svg-task-more class="w-15px h-15px mr-[13px] cursor-pointer handle " />
                    <svg-task-delete class="w-15px h-15px cursor-pointer " @click="deleteFolder(element)" />
                  </div>
                </div>
              </div>
            </template>
          </Draggable>
        </div>
      </div>
      <div class="w-full h-[30px] bg-[#fff] rounded-[6px] border border-[#DADDE8] flex justify-center cursor-pointer" @click="openAddFolder">
        <div class="text-[#6474FD] flex flex-cc cursor-pointer">
          <img :src="$g.tool.getFileUrl('comprehensiveTask/add.png')"
               class="w-13px h-13px mr-4px"
               alt="add"
          >
          <span class="text-[15px]">新增</span>
        </div>
      </div>
    </div>
    <div class="flex-1 min-h-[450px]  bg-[#fff] rounded-[6px] border border-[#DADDE8] p-[16px]">
      <div class="flex items-center justify-between">
        <div class="flex item-center">
          <template v-if="tableDeleteStatus == 'normal'">
            <div class="flex items-center ">
              <div class="text-[13px] text-[#6C6C74] leading-[17px]">
                {{ tableOptions.pageOptions.total }}个结果
              </div>
              <div class="w-1px h-7px bg-[#6C6C744D] mx-6px" />
              <svg-task-delete class="w-17px h-17px  cursor-pointer" @click="deleteResource" />
            </div>
          </template>
        </div>
        <div v-if="tableDeleteStatus == 'normal'" class=" border border-[#6474FD] rounded-[9px] select-none ">
          <div v-if="folderList?.length"
               class="flex flex-cc px-[11px] py-[6px] cursor-pointer"
               @click="showUploadDialog = true"
          >
            <img :src="$g.tool.getFileUrl('comprehensiveTask/add.png')"
                 class="w-13px h-13px mr-4px"
                 alt="add"
            >
            <span class="text-[13px] text-[#6474FD]">文本上传</span>
          </div>
        </div>
        <div v-else class="flex item-center">
          <el-button @click="tableDeleteStatus = 'normal'">
            取消
          </el-button>
          <el-button type="danger" @click="deleteFile">
            删除
          </el-button>
        </div>
      </div>
      <div class="text-[16px] text-[#6C6C74] leading-[22px] font-[500]">
        资源列表
      </div>
      <!-- 动态修改column不能渲染出来，所以用两个table -->
      <g-table
        v-if="tableDeleteStatus == 'normal'"
        :key="tableDeleteStatus"
        :table-options="tableOptions"
        :border="false"
        @change-page="getTaskResourceTeacherUploadListApi"
      >
        <template #fileName="{ row }">
          <div class="flex items-center">
            <div class="mt-2px flex-shrink-0">
              <img
                v-if="getFileTypeUrl(row.fileExtension)"
                :src="getFileTypeUrl(row.fileExtension)"
                alt=""
                class="w-17px h-17px"
              />
            </div>
            <g-mathjax :text="row.fileName" class="mx-5px" />
          </div>
        </template>
        <template #cz="{ row, index }">
          <div class="flex justify-center items-center">
            <el-button
              link
              type="primary"
              @click="handlePreview(row)"
            >
              预览
            </el-button>
            <el-button
              v-if="!isJoin(row)"
              link
              :disabled="addBtnDisabled || btnDisabled(row)"
              type="primary"
              @click="selected(row, index)"
            >
              加入
            </el-button>
            <el-button
              v-else
              link
              class="cursor-pointer text-[red]"
              @click="selected(row, index, 'cancel')"
            >
              取消加入
            </el-button>
          </div>
        </template>
      </g-table>
      <g-table
        v-else-if="tableDeleteStatus == 'delete'"
        :table-options="deleteTableOptions"
        :border="false"
        @change-page="getTaskResourceTeacherUploadListApi"
      >
        <template #fileName="{ row }">
          <div class="flex items-center">
            <div class="mt-2px flex-shrink-0">
              <img
                v-if="getFileTypeUrl(row.fileExtension)"
                :src="getFileTypeUrl(row.fileExtension)"
                alt=""
                class="w-17px h-17px"
              />
            </div>
            <g-mathjax :text="row.fileName" class="mx-5px" />
          </div>
        </template>
        <template #cz="{ row, index }">
          <div class="flex justify-center">
            <div class="mr-10px flex-shrink-0 cursor-pointer text-[#6474FD] " @click="handlePreview(row)">
              预览
            </div>

            <el-button
              v-if="!isJoin(row)"
              link
              :disabled="addBtnDisabled || btnDisabled(row)"
              type="primary"
              @click="selected(row, index)"
            >
              加入
            </el-button>
            <el-button
              v-else
              link
              class="cursor-pointer text-[red]"
              @click="selected(row, index, 'cancel')"
            >
              取消加入
            </el-button>
          </div>
        </template>
      </g-table>
    </div>
  </div>
  <!-- 新增文件夹 -->
  <el-dialog
    v-model="showDialog"
    title="新增文件夹"
    width="404px"
    center
    :close-on-click-modal="false"
    class="px-26px rounded-[9px]"

    @close="closeDialog"
  >
    <div class="flex items-center">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="文件名" prop="folderName">
          <el-input
            v-model="formData.folderName"
            type="text"
            maxlength="10"
            class="w-300px"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex-cc gap-13px ">
        <el-button @click="showDialog = false">
          取消
        </el-button>
        <el-button type="primary" @click="createFolder">
          创建
        </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 上传文件弹框 -->
  <el-dialog
    v-model="showUploadDialog"
    title="上传资源"
    width="404px"
    center
    :close-on-click-modal="false"
    class="px-26px rounded-[9px]"
    @close="closeUploadDialog"
  >
    <div>
      <g-upload
        ref="uploadRef"
        v-model:file-list="uploadFileList"
        mode="text"
        :max-size="1024 * 1024 * 1024 * 5"
        accept=".ppt,.pptx,.pdf,.doc,.docx,.xls,.xlsx,.mp4,.m3u8"
        :tips="false"
        :show-file-list="false"
        :multiple="true"
      >
        <template #default>
          <el-button type="primary" class="mb-10px">
            文本上传
          </el-button>
        </template>
      </g-upload>
      <div
        v-for="(item, index) in uploadFileList"
        :key="item.uid"
        class=" rounded-[6px] mr-15px mb-10px "
      >
        <div class="flex items-center  h-[20px] box-border px-17px">
          <div class="ml-10px text-[15px] font-600 flex-1 truncate">
            {{ item.name }}
          </div>
          <div
            class="text-[#FF4646] cursor-pointer  text-center  ml-13px"
            @click="deleteUploadResource(item, index)"
          >
            删除
          </div>
        </div>
        <!-- 进度条 -->
        <el-progress
          v-if="item.percentage != 100"
          :percentage="item.percentage"
          class="mx-17px"
        />
      </div>
    </div>
    <template #footer>
      <div class="flex-cc gap-13px ">
        <el-button @click="showUploadDialog = false">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="uploadBtnLoading"
          @click="uploadSuccess"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 预览文件 -->
  <PreviewFile v-model:show="showFile" :current-file="currentFile" />
</template>

<style scoped lang="scss">
</style>
