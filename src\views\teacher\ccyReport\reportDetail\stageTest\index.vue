<script setup lang="ts">
import {
  createPeriodTest,
  deletePeriodTest,
  getActivityStudentCount,
  getPeriodTestList,
  getPeriodTestNum,
  updateEnable,
} from '@/api/activity'
import { getFilesApi, getWordStateApi } from '@/api/ccyReport'

let props = defineProps<{
  sysCourseId: string
  schoolId: string
  gradeId: string
  classId: string
  dateRange: string[]
  sysSubjectId: string
  sysSubjectName: string
}>()

const route = useRoute()
const router = useRouter()
let cteateBntLoading = $ref(false)
let downBtnDisabled = $ref(true)// 下载按钮是否禁用
let downBtnLoading = $ref(false)// 下载按钮是否加载中
const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,

  column: [
    {
      label: '测试名称',
      prop: 'periodTestName',
    },
    {
      label: '创建时间',
      prop: 'createTime',
    },
    {
      label: '班级',
      prop: 'class',
      slot: true,
    },
    {
      label: '测试类型',
      prop: 'testTypeName',
    },
    {
      label: '完成情况',
      prop: 'finishSituation',
      slot: true,
    },
    {
      label: '创建人',
      prop: 'userName',
    },
    {
      label: '备注',
      prop: 'remark',
    },

    {
      label: '操作',
      prop: 'cz',
      slot: true,
      width: 170,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})
const testType = $ref<Array<{
  label: string
  value: number
}>>([
  {
    label: '周考',
    value: 12,
  },
  {
    label: '月考',
    value: 13,
  },
  {
    label: '个性模拟考',
    value: 16, // 这次没有做考试类型，所以这里默认16
  },
])// 测试类型
let testNum = $ref<string>('0000') // 测试编号
let statisticsCount = $ref<any>({}) // 学生数量统计
let form = reactive<any>({
  testType: 12,
  testName: `月考${$g.dayjs(new Date()).format('MMDD')}`,
  testSubject: props?.sysSubjectName,
  testGrade: '1年级',
  coverStudent: 0,
  testContent: '',
})

let showDialog = $ref(false)// 创建阶段测试弹窗

// 查看详情
function toDetail(row: any) {
  router.push({
    name: 'PaperDetail',
    query: {
      studentTrainPeriodTestId: row?.studentTrainPeriodTestId,
    },
  })
}
// 关闭创建阶段测试弹窗
function onCancel() {
  showDialog = false
}
// 打开创建阶段测试弹窗
async function openPopup() {
  if (!$g.tool.isTrue(statisticsCount?.usedStudentNum)) return
  await getPeriodTestNumApi()
  resetForm()
  changeTestType(form.testType)
  showDialog = true
}
/* 获取学生数量统计 */
async function fetchStudentStatistics() {
  try {
    if (!route.query.activityId || !props?.sysCourseId || !props?.schoolId || !props?.gradeId || !props?.classId || !props?.dateRange?.length) return
    let params: any = {
      activityId: route.query.activityId,
      sysCourseId: props?.sysCourseId,
      schoolId: props?.schoolId,
      sysGradeId: props?.gradeId,
      schoolClassId: props?.classId == 'all' ? null : props?.classId,
    }
    // beginDateTime: props?.dateRange?.[0] ?? null,
    //   endDateTime: props?.dateRange?.[1] ?? null,
    if (props?.dateRange?.[0] && props?.dateRange?.[1]) {
      params = {
        ...params,
        beginDateTime: props?.dateRange?.[0],
        endDateTime: props?.dateRange?.[1],
      }
    }
    let res = await getActivityStudentCount(params)
    statisticsCount = res
    form.coverStudent = statisticsCount?.usedStudentNum ?? 0
  }
  catch (err) {
    console.log(err)
  }
}
/* 获取阶段测试分页列表 */
async function fetchPeriodTestListApi() {
  try {
    if (!route.query.activityId || !props?.schoolId || !props?.gradeId) {
      tableOptions.loading = false
      return
    }
    tableOptions.loading = true
    let params: any = {
      activityId: route.query.activityId,
      sysSubjectId: props?.sysSubjectId,
      schoolId: props?.schoolId,
      sysGradeId: props?.gradeId,
      schoolClassId: props?.classId == 'all' ? null : props?.classId,
      // beginTime: props?.dateRange?.[0] ?? null,
      // endTime: props?.dateRange?.[1] ?? null,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.pageSize,
    }
    if (props?.dateRange?.[0] && props?.dateRange?.[1]) {
      params = {
        ...params,
        beginTime: props?.dateRange?.[0],
        endTime: props?.dateRange?.[1],
      }
    }
    let res = await getPeriodTestList(params)
    tableOptions.data = res?.list ?? []
    tableOptions.pageOptions.total = res?.total ?? 0
  }
  catch (err) {
    tableOptions.data = []
  }
  finally {
    tableOptions.loading = false
  }
}
// 创建阶段测试
async function createPeriodTestApi() {
  try {
    cteateBntLoading = true
    let params: any = {
      activityId: route.query.activityId,
      sysSubjectId: props?.sysSubjectId,
      sysCourseId: props?.sysCourseId,
      schoolId: props?.schoolId,
      sysGradeId: props?.gradeId,
      schoolClassId: props?.classId == 'all' ? null : props?.classId,
      // beginTime: props?.dateRange?.[0] || null,
      // endTime: props?.dateRange?.[1] || null,
      testType: form.testType,
      periodTestName: form.testName,
      remark: form.testContent,
    }
    if (props?.dateRange?.[0] && props?.dateRange?.[1]) {
      params = {
        ...params,
        beginTime: props?.dateRange?.[0],
        endTime: props?.dateRange?.[1],
      }
    }
    await createPeriodTest(params)
    showDialog = false
    tableOptions.pageOptions.page = 1
    await fetchPeriodTestListApi()
  }
  catch (err) {
    console.log(err)
  }
  finally {
    cteateBntLoading = false
  }
}
// 切换测验类型
function changeTestType(val: any) {
  let testName = testType.find(it => it.value == val)?.label
  form.testName = `${testName}${$g.dayjs(new Date()).format('MMDD')}${testNum}`
}
// 删除阶段测试
async function deletePeriodTestApi(row: any) {
  $g.confirm({
    content: '确定删除吗？',
  }).then(async () => {
    await deletePeriodTest({
      studentTrainPeriodTestId: row?.studentTrainPeriodTestId,
    })
    await fetchPeriodTestListApi()
  }).catch(() => {
    })
}
/* 获取测验名称 */
async function getPeriodTestNumApi() {
  try {
    let res = await getPeriodTestNum()
    testNum = res ?? '0000'
  }
  catch (error) {
    console.log(error)
  }
}
// 更新测验状态
async function updateStageTestStatus(row: any) {
  try {
    if (row?.btnIsEnable == 1) return
    await updateEnable({
      studentTrainPeriodTestId: row?.studentTrainPeriodTestId,
      isEnable: row?.isEnable == 1 ? 2 : 1, // ，1-否，2-是
    })
    await fetchPeriodTestListApi()
  }
  catch (err) {
    console.log(err)
  }
}
// 分页
function changePage() {
  fetchPeriodTestListApi()
}
function resetForm() {
  form.testType = 12
  form.testContent = ''
}
async function getAllData() {
  await fetchStudentStatistics()
  await fetchPeriodTestListApi()
}
// 编辑测验
async function handleCommand(command: any, row: any) {
  if (command == 'studentList') {
    // router.push({
    //   name: 'ReportDetail',
    //   query: {
    //     studentTrainPeriodTestId: row?.studentTrainPeriodTestId,
    //   },
    // })
  }
  else if (command == 'downloadPaper') {
    try {
      if (downBtnLoading || downBtnDisabled) return
      downBtnLoading = true
      const res = await getFilesApi({ studentTrainPeriodTestId: row?.studentTrainPeriodTestId })

      if (!res?.length) {
        $g.showToast('暂无可下载的文件')
        return
      }

      const fileList = res.map((it) => {
        const fileNameParts = [
          it?.studentName,
          it?.idNum,
          it?.schoolName,
          it?.gradeName && it?.className ? `${it.gradeName}${it.className}` : '',
          it?.exerciseTaskTypeName ? `【${it.exerciseTaskTypeName}】` : '',
          '试卷',
        ].filter(Boolean)

        return {
          name: `${fileNameParts.join('-')}.docx`,
          url: it?.wordUrl,
        }
      })

      const zipName = res?.[0]?.exerciseTaskName || '试卷压缩包'
      await $g.tool.downloadAndZipFiles(fileList, zipName, (loading) => {
        downBtnLoading = loading
      })
    }
    catch (error) {
      console.log(error)
    }
  }
  else {
    deletePeriodTestApi(row)
  }
}
// 下拉可见
async function visibleChange(event: any, row: any) {
  // 默认禁用下载按钮
  downBtnDisabled = true
  downBtnLoading = false
  if (event) {
    try {
      const {
        taskEnd = 1,
        success = 0,
      } = await getWordStateApi({ studentTrainPeriodTestId: row?.studentTrainPeriodTestId })
      downBtnDisabled = taskEnd !== 2 && success == 0
    }
    catch (err) {
      console.log(err)
    }
  }
}
onMounted(async () => {
  getAllData()
})
watchDebounced(props, (val) => {
  tableOptions.pageOptions.page = 1
  if (val?.sysSubjectName)
    form.testSubject = val?.sysSubjectName

  getAllData()
}, {
  debounce: 150,
})
</script>

<template>
  <div class="bg-white p-[17px] rounded-[6px]">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="text-[17px] font-600">
          学生列表
        </div>
        <div class="ml-10px text-[14px]">
          开通活动人数：<span class="text-[#65CD64]">{{ statisticsCount?.openedStudentNum ?? 0 }}</span>人,
          有使用数据的总计 <span class="text-[#65CD64]">{{ statisticsCount?.usedStudentNum ?? 0 }}</span>人,
          下方表格仅展示<span class="text-[#FF4646]">有使用数据</span>的学生
        </div>
      </div>
      <div
        :class="{ '!cursor-not-allowed !text-[#999] !border-[#999]': !$g.tool.isTrue(statisticsCount?.usedStudentNum) }"
        class="w-[124px] h-[34px] br-[17px] border border-[#6474FD] flex items-center justify-center text-[13px] text-[#6474FD] leading-[17px] cursor-pointer van-haptics-feedback"
        @click="openPopup"
      >
        <svg-ri-add-line class="w-[20px]  h-[20px] pr-[4px] text-[#6474FD]" :class="{ '!text-[#999]': !$g.tool.isTrue(statisticsCount?.usedStudentNum) }" />
        创建阶段测试
      </div>
    </div>
    <div class="text-[#636772] text-14px mt-13px">
      更新时间：实时统计
    </div>

    <div :class="$g.isPC ? 'h-[calc(100%-78px-32px)] overflow-auto no-bar' : ''">
      <g-table
        :table-options="tableOptions"
        :border="false"
        :stripe="true"
        @change-page="changePage"
      >
        <template #cz="{ row }">
          <div class="flex items-center ">
            <el-button
              link
              class="text-[#6474FD] van-haptics-feedback"
              @click="toDetail(row)"
            >
              查看
            </el-button>
            <el-button
              link
              class=" van-haptics-feedback mx-[17px]"
              :disabled="row?.btnIsEnable == 1"
              :class=" row?.isEnable == 1 ? '!text-[#999999] ' : 'text-[#6474FD]'"
              @click="updateStageTestStatus(row)"
            >
              {{ row?.isEnable == 1 ? '未启用' : '已启用' }}
            </el-button>
            <el-dropdown
              :hide-on-click="false"
              trigger="click"
              @command="handleCommand($event, row)"
              @visible-change="visibleChange($event, row)"
            >
              <div class="el-dropdown-link text-[#6474FD] van-haptics-feedback">
                编辑
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <!-- <el-dropdown-item command="studentList">
                    学生名单
                  </el-dropdown-item> -->
                  <el-dropdown-item command="downloadPaper" :loading="downBtnLoading">
                    <el-button link :disabled="downBtnDisabled || downBtnLoading">
                      {{ downBtnLoading ? '下载中...' : '下载试卷' }}
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item command="deletePeriodTest">
                    <el-button link>
                      删除测试
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <template #finishSituation="{ row }">
          <div class="flex-cc">
            <el-progress
              class="w-[60px]"
              :percentage="$g.math(row?.finishStudentNum ?? 0).divide(row?.studentNum ?? 0).multiply(100).toFixed(0).value()"
              color="#6474FD"
              :stroke-width="10"
              :show-text="false"
            />
            <div class="ml-3px">
              <span>{{ row?.finishStudentNum ?? 0 }}</span><span class="text-[#999999]">/{{ row?.studentNum ?? 0 }}</span>
            </div>
          </div>
        </template>
        <template #class="{ row }">
          <div class="">
            {{ row?.schoolClassList.map(it => it.className).join(',') }}
          </div>
        </template>
      </g-table>
    </div>
    <el-dialog
      v-model="showDialog"
      class="w-[480px]  !px-0 !py-[26px]"
      align-center
      center
      :close-on-click-modal="false"
      :lock-scroll="false"
    >
      <div>
        <div class="font-500 text-[19px] text-[#000] leading-[26px] mb-[28px] text-center">
          创建阶段测试
        </div>
        <el-form :model="form"
                 label-width="auto"
                 class="!mx-[38px] !text-[#333] !text-[15px]"
        >
          <el-form-item label="测试类型：">
            <el-radio-group v-model="form .testType" @change="changeTestType">
              <el-radio
                v-for="item in testType"
                :key="item.value"
                class="!text-[#333] !text-[15px]"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="测试名称：">
            {{ form.testName }}
          </el-form-item>
          <el-form-item label="测试学科：">
            {{ form.testSubject }}
          </el-form-item>
          <el-form-item label="覆盖学生：">
            {{ form.coverStudent }}人
          </el-form-item>
          <el-form-item v-if="form.testType == 16" label="考试介绍：">
            <div class="mt-[6px] text-[15px] text-[#333] leading-[18px]">
              <div>
                根据25新高考一诊、二诊试卷双向细目表为基础，根据学生层级，智能能成
                <span class="text-[#6474FD]">
                  考察难度个性化，考察内容个性化，试卷结构标准化
                </span>
                的模拟试卷。
              </div>
            </div>
          </el-form-item>
          <el-form-item
            label="测试备注："
            label-position="top"
          >
            <el-input
              v-model="form.testContent"
              :rows="4"
              type="textarea"
              show-word-limit
              maxlength="30"
              placeholder="请输入测试备注"
            />
          </el-form-item>
          <div v-if="form.testType == 16" class="text-[#999] leading-[18px] text-[13px] flex items-center">
            <svg-common-notice class="w-[12px] h-[12px] mr-[6px]" />
            注意：标准模考覆盖新课标全部内容，仅难度个性化
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="flex-cc gap-13px ">
          <el-button class="border-0 w-[141px] h-[34px] br-[9px] bg-[rgba(153,153,153,0.1)] text-[#666] text-[15px] font-500 text-center leading-[34px]  cursor-pointer van-haptics-feedback" @click="onCancel">
            取消
          </el-button>
          <el-button
            :loading="cteateBntLoading"
            class="border-0 w-[141px] h-[34px] br-[9px] bg-[#6474FD] text-[#fff] text-[15px] font-500 text-center leading-[34px]  cursor-pointer van-haptics-feedback"
            @click="createPeriodTestApi"
          >
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
:deep() {
  .el-dialog {
    pointer-events: auto;
    background: linear-gradient(180deg, #E6E8FF 0%, #FFFFFF 20%) !important;
    border-radius: 24px!important;
    border: 1px solid rgba(153, 153, 153, 0.4);
  }
  .el-form-item{
    margin-bottom: 10px!important;
  }
  .el-dialog__header{
    padding-bottom: 0px!important;
  }
  .el-form-item__label{
    font-weight: 400;
    font-size: 15px;
    color: #333;
    padding-right: 6px!important;
  }
}
</style>
