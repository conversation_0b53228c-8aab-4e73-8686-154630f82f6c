<script setup lang="ts">
import router from '@/router'

let props = defineProps({
  chapterReportList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  searchData: {
    type: Object as PropType<any>,
    default: () => {},
  },
})
let route = useRoute()
let getTableOptions = $computed(() => {
  return (list) => {
    return {
      loading: false,
      ref: null as any,
      column: [
        {
          label: '模块名称',
          prop: 'activityThemeModuleName',
        },
        {
          label: '测验数量',
          prop: 'paperNum',
        },
        {
          label: '试题总数',
          prop: 'totalQuestionNum',
        },
        {
          label: '平均正确率',
          prop: 'avgCorrectRate',
          formatter(row) {
            return `${row?.avgCorrectRate ?? 0}%`
          },
        },
        {
          label: '平均进度(已作答学生统计)',
          prop: 'avgSchedule',
          formatter(row) {
            return `${row?.avgSchedule ?? 0}%`
          },
        },

        {
          label: '报告',
          prop: 'cz',
          slot: true,
        },
      ],
      data: list,
    }
  }
})
function getList() {
}

/* 表头样式 */
function headerCellClassName() {
  return {
    color: '#6C6C74',
    padding: '0px',
  }
}

function goReportDetail(row, title) {
  router.push({
    name: 'ChapterReportDetail',
    query: {
      title,
      activityThemeModuleId: row?.activityThemeModuleId,
      activityId: route.query?.activityId,
      ...props.searchData,
    },
  })
}
</script>

<template>
  <div class="bg-white p-[17px] rounded-[6px] relative">
    <template v-if="chapterReportList.length">
      <div class="absolute right-[17px] top-[17px] text-[14px] text-[#636772] leading-[20px]">
        更新时间：实时统计
      </div>
      <div v-for="item in chapterReportList"
           :key="item?.activityThemeId"
           class="mb-[17px]"
      >
        <div class="font-[600] text-[15px] leading-[21px] mb-[15px] max-w-[calc(100%-140px)]">
          {{ item?.activityThemeName }}
        </div>
        <g-table
          :border="false"
          :header-cell-style="headerCellClassName"
          :table-options="getTableOptions(item?.moduleStatisticsList)"
          class="!mt-0"
          @change-page="getList"
        >
          <template #cz="{ row }">
            <div class="text-[#6474FD] van-haptics-feedback" @click="goReportDetail(row, item?.activityThemeName)">
              查看详情
            </div>
          </template>
        </g-table>
      </div>
    </template>

    <g-empty v-else></g-empty>
  </div>
</template>

<style lang="scss" scoped>

</style>
