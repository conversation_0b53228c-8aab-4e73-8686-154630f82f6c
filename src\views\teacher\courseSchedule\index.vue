<script setup lang="ts">
import { getWeekApi } from '@/api/courseSchedule'
import { getInfo } from '@/api/taskCenter'
import { useSettingStore } from '@/stores/modules/setting'
import { useUserStore } from '@/stores/modules/user'
import BScroll from '@better-scroll/core'

import Namedavatar from 'namedavatar'
import CourseTable from './components/CourseTable.vue'

const router = useRouter()
let currentWeek = $ref<any>(null)
let { menuCollapsed } = toRefs(useSettingStore())
let bs: any = $ref(null)
let userInfo: any = $ref({})
let boxRef: any = $ref(null)
let weekList = $ref<any>([])
const avatarUrl = $computed(() => {
  const svg = Namedavatar.getSVGString(userInfo?.userName, {
    nameType: 'lastName',
  })
  const url = Namedavatar.getDataURI(svg)
  return url
})
function handleCommand(command) {
  if (command == 1) {
    router.push({ name: 'GroupManage' })
    return true
  }
  else {
    useUserStore().resetAll()
    const host = import.meta.env.VITE_APP_NAVIGATION_URL
    window.location.href = `${host}/#/platform/login`
  }
}
// 获取老师信息
async function getUserInfo() {
  const info = await getInfo()
  userInfo = info
}
function onWeekClick(item) {
  currentWeek = item.weekPhaseIndex
}
async function getWeek() {
  const res = await getWeekApi()
  weekList = res || []
  currentWeek = weekList.find(item => item.isCurrentWeek)?.weekPhaseIndex
  await nextTick()
  scroll()
}
function scroll() {
  let left = 0
  const dom: any = document.getElementById('week')
  const sub: any = dom?.querySelector('.item-active')
  if (dom && sub)
    left = sub.offsetLeft - dom?.clientWidth / 2 + sub?.clientWidth / 2
  if (left > 55 * (weekList.length + 4) - dom?.clientWidth)
    left = 55 * (weekList.length + 4) - dom?.clientWidth
  if (left < 0 || sub.offsetLeft + sub?.clientWidth < dom?.clientWidth)
    left = 0
  if (bs == null && boxRef) {
    bs = new BScroll(boxRef, {
      scrollX: true,
      click: true,
      startX: left * -1,
    })
  }
  else {
    bs?.refresh()
    bs?.scrollTo(left * -1, 0, 500)
  }
}
onMounted(() => {
  getUserInfo()
  getWeek()
})
watch(() => menuCollapsed.value, () => {
  nextTick(() => {
    scroll()
  })
})
let width = $computed(() => menuCollapsed.value ? '100vw' : 'calc(100vw - 180px)')
</script>

<template>
  <div :style="{ width }" class="px-21px pt-21px w-full h-[100vh]">
    <div class="w-full flex justify-between items-center mb-5px">
      <div class="text-[21px] font-500 text-[#333]">
        你好！{{ userInfo?.userName }}老师
      </div>
      <div class="flex items-center">
        <el-tooltip
          effect="light"
          append-to="#app"
          placement="top-start"
        >
          <template #content>
            <div class="text-[10px] w-[74px]">
              课程表中任务类型为AI课任务的任务名称为<span
                class="text-[#FF7735]"
              >
                【橙色】
              </span>
            </div>
          </template>
          <img
            class="w-[10px] h-[10px] cursor-pointer mr-[10px]"
            src="@/assets/img/courseSchedule/tip.png"
          />
        </el-tooltip>
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="flex items-center">
            <div v-if="userInfo?.userName" class="br-[50%] w-27px h-27px overflow-hidden mr-9px">
              <img
                :src="avatarUrl"
                alt="refresh"
                class="w-full h-full"
              />
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="$g.isPC" command="2">
                <el-button type="danger" text>
                  退出登录
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div
      id="week"
      ref="boxRef"
      class="relative w-full overflow-hidden mt-[16px]"
    >
      <div class="w-fit bg-[#fff] br-[4px] flex pl-[3px] py-[3px]">
        <div
          v-for="(weekItem, weekIndex) in weekList"
          :key="weekIndex"
          class="mr-[4px] pt-[2px] cursor-pointer w-[55px] text-[12px] text-center border br-[4px]"
          :class="{
            'text-[#6474FD] item-active bg-[#EBF1FF] border-[#6474FD] ': currentWeek == weekItem.weekPhaseIndex,
            ' border-[transparent]': currentWeek != weekItem.weekPhaseIndex,
          }"
          @click="onWeekClick(weekItem)"
        >
          {{ weekItem.title }}
        </div>
      </div>
    </div>
    <div class="w-full mt-[18px]">
      <CourseTable :current-week="currentWeek"></CourseTable>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
