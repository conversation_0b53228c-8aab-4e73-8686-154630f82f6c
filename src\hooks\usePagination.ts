/**
 * 分页 Hook
 */
export default function usePagination({
  api, // 请求接口,delay配置需要关闭
  pageOptions, // 分页配置
  maxRequestPageSize = 999, // 最大请求页大小
}): any {
  // 存储所有数据和上一次的请求参数
  let allData = $ref([])
  let prevParams = $ref<Record<string, any>>({})
  let prevSortOptions = null

  /**
   * 比较两个参数对象是否相同(排除分页参数)
   */
  function isParamsEqual(params1, params2) {
    if (!params1 || !params2) return false

    return $g._.isEqual(
      $g._.omit(params1, ['page',
'pageSize',
'total']),
      $g._.omit(params2, ['page',
'pageSize',
'total']),
    )
  }

  /**
   * 本地排序数据
   */
  function sortData(data, sortOptions) {
    if (!sortOptions || !sortOptions.order)
      return [...data]

    return $g._.orderBy(
      data,
      [sortOptions.prop],
      [sortOptions.order],
    )
  }

  /**
   * 获取所有数据（轮询请求直到全部获取完毕）
   */
  async function fetchAllData(params) {
    try {
      let allList: any[] = [] // 累积所有数据
      let currentPage = 1 // 当前页码
      let totalCount = 0 // 总数据量
      let hasMore = true // 是否还有更多数据

      // 轮询请求直到获取全部数据
      while (hasMore) {
        const response = await api({
          ...params,
          page: currentPage,
          pageSize: maxRequestPageSize,
        })

        const {
          list = [],
          total = 0,
        } = response
        allList = [...allList, ...list]
        totalCount = total

        // 判断是否获取完全部数据
        if (allList.length >= totalCount || list.length === 0) 
          hasMore = false


        else {
          currentPage++
        }
      }

      allData = allList as typeof allData
      prevParams = params
      return {
        success: true,
        list: allList,
      }
    }
    catch (error) {
      console.error('获取所有数据失败:', error)
      return {
        success: false,
        error,
      }
    }
  }

  /**
   * 获取分页数据
   * @param {Record<string, any>} params - 请求参数
   * @param {object} [sortOptions] - 排序选项
   * @param {string} [sortOptions.prop] - 排序字段
   * @param {string} [sortOptions.order] - 排序方式('asc'|'desc'|'null')
   * @param {boolean} [showAll] - 是否显示所有数据
   * @returns  分页数据结果
   */
  async function getPageData(params, sortOptions, showAll = false) {
    try {
      // 如果有排序选项，则更新保存的排序选项
      if (sortOptions)
        prevSortOptions = sortOptions

      // 检查是否需要重新获取全部数据
      if (!allData.length || !isParamsEqual(params, prevParams) || !$g.tool.isTrue(prevParams))
        await fetchAllData(params)

      // 对数据进行排序（优先使用当前传入的排序选项，如没有则使用之前保存的排序选项）
      let sortedData = (sortOptions || prevSortOptions)
        ? sortData(allData, sortOptions || prevSortOptions)
        : allData

      // 如果showAll为true，返回所有数据
      if (showAll) {
        return {
          list: sortedData,
          total: allData.length,
          success: true,
        }
      }

      const start = (pageOptions.page - 1) * pageOptions.pageSize
      const end = start + pageOptions.pageSize

      return {
        list: sortedData.slice(start, end),
        total: allData.length,
        success: true,
      }
    }
    catch (error) {
      console.error('获取分页数据失败:', error)
      return {
        list: [],
        total: 0,
        success: false,
        error,
      }
    }
  }

  return {
    getPageData,
  }
}
