<script setup lang="ts">
import {
  getBook<PERSON>ist<PERSON><PERSON>,
  getDifficultyApi,
  getGradesApi,
  getQuestionListApi,
  getSchoolsApi,
  getTreeDataApi,
  getTypeListApi,
} from '@/api/taskCenter'
import BScroll from '@better-scroll/core'
import QuestionItem from './QuestionItem.vue'

const props = defineProps({
  book: {
    type: Object,
  },
  school: {
    type: Object,
  },
})
let questionList = $ref<any>([])
let boxRef: any = $ref(null)
let bs: any = $ref(null)
let current = $ref<any>(null)
let defaultExpandKey = $ref<any>(null)
let treeRef = $ref<any>(null)
let currentBook = $ref<any>(null)
let bookList = $ref<any>([])
let currentGrade = $ref<any>(null)
let defaultGrade = $ref<any>(null)
let gradeList = $ref<any>([])
let schoolList = $ref<any>([])
let currentSchool = $ref<any>(null)
let defaultSchool = $ref<any>(null)
let loading = $ref<any>(false)
let questionTypeList = $ref<any>([])
let currentQuestionType = $ref<any>(null)
let currentDifficulty = $ref<any>(null)
let currentDiffName = $computed(() => {
  return $g._.map(
    difficultyList?.filter(item =>
      currentDifficulty?.includes(item?.sysQuestionDifficultyId)),
    'sysQuestionDifficultyName',
  )
})
let currentTypeName = $computed(() => {
  return $g._.map(
    questionTypeList?.filter(item =>
      currentQuestionType?.includes(item?.sysQuestionTypeId)),
    'sysQuestionTypeName',
  )
})
let difficultyList = $ref<any>([])
let currentName = $computed(() => {
  return schoolList?.find(item => item?.schoolId == currentSchool)?.schoolName
})
const pageOption = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})
const pageBookOption = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})
function nodeClick(node) {
  current = node.bookCatalogId
}
let first = $ref<any>(1)
async function startRoll() {
  await nextTick()
  let left = 0
  const dom: any = document.getElementById('grade')
  const sub: any = dom?.querySelector('.item-active')
  if (dom && sub)
    left = sub.offsetLeft - dom?.clientWidth / 2 + sub?.clientWidth / 2

  if (left < 0)
    left = 0

  if (left > 58 * gradeList.length + 10 - dom?.clientWidth)
    left = 58 * gradeList.length + 10 - dom?.clientWidth

  if (bs == null && boxRef) {
    bs = new BScroll(boxRef, {
      scrollX: true,
      click: true,
      startX: left * -1,
    })
  }
  else {
    bs?.refresh()
    bs?.scrollTo(left * -1, 0, 500)
  }
}
function onBookClick(item) {
  currentBook = item
  defaultGrade = currentGrade
  defaultSchool = currentSchool
  showDrawer = false
  treeData = []
  current = null
  getTreeData()
}
async function bookPulldown() {
  pageBookOption.page = 1
  bookList = []
  loading = true
  getBookList()
}
async function bookPullup() {
  pageBookOption.page += 1
  getBookList()
}
async function getBookList() {
  try {
    const res = await getBookListApi({
      sysSubjectId: route?.query?.subjectId,
      schoolId: currentSchool,
      sysGradeId: currentGrade,
      page: pageBookOption?.page,
      pageSize: pageBookOption?.page_size,
    })
    bookList = [...bookList, ...res?.list]
    currentBook = currentBook != null ? currentBook : props.book
    pageBookOption.total = res?.total || 0
    loading = false
    nextTick($g.tool.renderMathjax)
  }
  catch (err) {
    loading = false
  }
}
watch(
  () => [currentDifficulty, currentQuestionType],
  async () => {
    pageOption.page = 1
    questionList = []
    currentQuestionListAll = []
    showLoading = true
    if (first == 1) {
      if (!treeData?.length) {
        setTimeout(() => {
          showLoading = false
        }, 100)
      }
      return
    }

    await getQuestionList()
    await getCurrentQuestionListAll()
  },
)
function onDeleteAll() {
  currentDifficulty = null
  currentQuestionType = null
}
function onDeleteDifficulty() {
  currentDifficulty = null
}
function onDeleteType() {
  currentQuestionType = null
}
const questionListAll = inject<Ref<any[]>>('questionList', ref([]))
let currentQuestionListAll = $ref<any[]>([]) // 当前章节所有的题目列表
/* 获取当前章节所有的题目列表 */
async function getCurrentQuestionListAll() {
  let res = await getQuestionListApi({
    bookCatalogId: current,
    bookId: currentBook.bookId,
    page: pageOption.page,
    pageSize: pageOption.total || pageOption.page_size,
    sysQuestionDifficultyIdList: currentDifficulty?.join(','),
    sysQuestionTypeIdList: currentQuestionType?.join(','),
  })
  // 如果在questionListAll中存在，则设置isAdd为true
  currentQuestionListAll = res?.list?.map((v) => {
    if (questionListAll.value.some(item => item.questionId == v.questionId))
      v.isAdd = true

    return v
  }) || []
}
async function getQuestionList() {
  try {
    let res = await getQuestionListApi({
      bookCatalogId: current,
      bookId: currentBook.bookId,
      page: pageOption.page,
      pageSize: pageOption.page_size,
      sysQuestionDifficultyIdList: currentDifficulty?.join(','),
      sysQuestionTypeIdList: currentQuestionType?.join(','),
    })
    questionList = res?.list?.length
      ? [...questionList, ...res?.list]
      : questionList
    questionList?.map((item) => {
      if (
        $g._.map(questionListAll.value, 'questionId').includes(item?.questionId)
      )
        item.isAdd = true

      else
        item.isAdd = false

      return item
    })
    pageOption.total = res?.total || 0
    first++
    showLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (err) {
    console.log('err', err)
    showLoading = false
  }
}
async function getTypeList() {
  try {
    const res = await getTypeListApi({
      sysSubjectId: route?.query?.subjectId,
      sysStageId: currentBook?.sysStageId,
    })
    questionTypeList = res || []
  }
  catch (err) {}
}
async function getDifficulty() {
  try {
    const res = await getDifficultyApi()
    difficultyList = res || []
  }
  catch (err) {}
}
async function getGrades() {
  try {
    const res = await getGradesApi({
      schoolId: currentSchool,
      sysSubjectId: route?.query?.subjectId,
    })
    gradeList = res || []
    currentGrade = gradeList?.some(item => item?.sysGradeId == defaultGrade)
      ? defaultGrade
      : gradeList[0]?.sysGradeId
    nextTick(() => {
      startRoll()
    })
  }
  catch (err) {}
}
let showDrawer = $ref<any>(false)
let treeLoading = $ref<any>(false)
let showLoading = $ref(false)
const route = useRoute()
watch(
  () => currentSchool,
  () => {
    gradeList = []
    currentGrade = null
    bs = null
    if (currentSchool != null)
      getGrades()
  },
)
async function getSchools() {
  try {
    const res = await getSchoolsApi({ sysSubjectId: route?.query?.subjectId })
    schoolList = res || []
    currentSchool = schoolList?.some(item => item?.schoolId == defaultSchool)
      ? defaultSchool
      : schoolList?.find(item => item?.defaultSelected)?.schoolId
  }
  catch (err) {}
}
watch(
  () => showDrawer,
  () => {
    if (showDrawer) {
      schoolList = []
      bs = null
      currentSchool = null
      getSchools()
    }
    else {
    }
  },
)
watch(
  () => currentGrade,
  () => {
    bookList = []
    pageBookOption.page = 1
    loading = true
    console.log('currentGrade', currentGrade)
    if (currentGrade != null)
      getBookList()

    else
      loading = false
  },
)
function onGradeSelect(item) {
  console.log('sysGradeId', item?.sysGradeId)
  currentGrade = item?.sysGradeId
}
const expandKey = $computed<any[]>(() => {
  return [defaultExpandKey]
})
const setStepVal = inject('setStepVal') as any
function goBack() {
  setStepVal(2)
}
function getCurrent(data) {
  if (data?.children?.length) {
    for (let i = 0; i < data?.children.length; i++) {
      if (data?.children[i]?.children?.length) {
        getCurrent(data?.children[i])
      }
      else {
        if (!current && data?.children[i]?.questionCount > 0) {
          current = data?.children[i]?.bookCatalogId
          break
        }
      }
    }
  }
  else {
    if (!current && data?.questionCount > 0)
      current = data?.bookCatalogId
  }
}
async function getTreeData() {
  treeLoading = true
  current = null
  const res = await getTreeDataApi({ bookId: currentBook?.bookId })
  treeData = res || []
  treeData.map((item) => {
    if (current)
      return

    getCurrent(item)
  })
  if (!current)
    showLoading = false

  defaultExpandKey = current
  await nextTick()
  $g.tool.renderMathjax()
  treeLoading = false
}
watch(
  () => current,
  async () => {
    showLoading = true
    questionList = []
    currentQuestionListAll = []
    first = 1
    currentDifficulty = null
    currentQuestionType = null
    pageOption.page = 1
    pageOption.total = 0
    // if (current) {
    await getQuestionList()
    await getCurrentQuestionListAll()
    // }
  },

)
onBeforeMount(async () => {
  showLoading = true
  currentBook = props.book
  defaultSchool = props?.school
  defaultGrade = props?.book?.sysGradeId
  getDifficulty()
  getTypeList()
  getTreeData()
  if (currentBook?.bookId) {
    await getQuestionList()
    await getCurrentQuestionListAll()
  }
})
let treeData = $ref<any>([])
async function pulldown() {
  pageOption.page = 1
  questionList = []
  showLoading = true
  getQuestionList()
}
async function pullup() {
  pageOption.page += 1
  getQuestionList()
}
/* 全部加入 */
async function onAddAll() {
  if (showLoading)
    return

  currentQuestionListAll.forEach((v) => {
    v.isAdd = true
  })
  // 组合在一起，并按照questionID去重
  questionListAll.value = [...questionListAll.value, ...currentQuestionListAll]
  questionListAll.value = questionListAll.value.filter((v, index, self) =>
    index === self.findIndex(t => t.questionId === v.questionId))
  // 将对应questionList的isAdd设置为true
  questionList = questionList.map((v) => {
    return {
      ...v,
      isAdd: true,
    }
  })
}
/* 全部移除 */
function onRemoveAll() {
  if (showLoading)
    return

  currentQuestionListAll.forEach((v) => {
    v.isAdd = false
  })
  questionListAll.value = questionListAll.value.filter((v) => {
    return !currentQuestionListAll.some(item => item.questionId == v.questionId)
  })
  // 将对应questionList的isAdd设置为true
  questionList = questionList.map((v) => {
    return {
      ...v,
      isAdd: false,
    }
  })
}
// 当currentQuestionListAll中所有isAdd为false时，显示全部移除按钮
// showAddAll 1 全部加入 2 全部移除
const showAddAll = $computed(() => {
  if (currentQuestionListAll.length)
    return !currentQuestionListAll.every(v => v.isAdd) ? 1 : 2

  else
    return 0

  // return currentQuestionListAll.length ? !currentQuestionListAll.every(v => v.isAdd) : true
})
</script>

<template>
  <div>
    <g-navbar :on-back="goBack">
      <template #title>
        <div class="flex-cc">
          <div>
            <g-mathjax :text="currentBook?.bookName"></g-mathjax>
          </div>
          <div
            class="cursor-pointer ml-[6px] flex items-center"
            @click="showDrawer = true"
          >
            <img
              src="@/assets/img/taskCenter/exchange.png"
              class="w-17px mr-[2px] h-17px"
              alt=""
            />
            <div class="text-[#6474FD] text-[15px]">
              换一本
            </div>
          </div>
        </div>
      </template>
    </g-navbar>
    <div class="flex items-center justify-end mt-[2px] mb-[16px]">
      <div class="relative questionType">
        <el-select
          v-model="currentQuestionType"
          placeholder="Select"
          :clearable="false"
          size="large"
          multiple
          :style="{ opacity: 0, position: 'absolute' }"
        >
          <el-option
            v-for="item in questionTypeList"
            :key="item.value"
            :label="item.sysQuestionTypeName"
            :value="item.sysQuestionTypeId"
          />
          <template #tag>
            <div></div>
          </template>
        </el-select>
        <div
          class="text-[13px] cursor-pointer text-[#6C6C74] flex mr-[13px] items-center"
        >
          <span class="flex-shrink-0">题型</span><svg-common-expand class="w-[17px] ml-[2px] h-[17px]" />
        </div>
      </div>
      <div class="relative difficulty">
        <el-select
          v-model="currentDifficulty"
          placeholder="Select"
          size="large"
          :clearable="false"
          multiple
          :style="{ opacity: 0, position: 'absolute' }"
        >
          <el-option
            v-for="item in difficultyList"
            :key="item.value"
            :label="item.sysQuestionDifficultyName"
            :value="item.sysQuestionDifficultyId"
          />
        </el-select>
        <div
          class="text-[13px] cursor-pointer text-[#6C6C74] flex items-center"
        >
          难度<svg-common-expand class="w-[17px] ml-[2px] h-[17px]" />
        </div>
      </div>
    </div>
    <div
      class="flex"
      :style="{
        height: $g.isPC ? 'calc(100vh - 213px)' : 'calc(100vh - 9vh - 115px)',
      }"
    >
      <div
        class="w-[258px] border border-[#DADDE8] mr-[15px] p-[16px] overflow-x-hidden overflow-y-auto br-[6px] bg-[#FFFFFF]"
      >
        <g-loading v-if="treeLoading" class="h-[200px]"></g-loading>
        <div v-else>
          <el-tree
            v-if="treeData?.length"
            ref="treeRef"
            class="my-tree"
            :data="treeData"
            node-key="bookCatalogId"
            highlight-current
            :current-node-key="current"
            :default-expanded-keys="expandKey"
            :expand-on-click-node="false"
            @node-click="nodeClick"
          >
            <template #default="{ data }">
              <g-mathjax class="max-w-[300px] text" :text="data.bookCatalogName"></g-mathjax>
            </template>
          </el-tree>
          <g-empty v-else></g-empty>
        </div>
      </div>
      <div class="flex-1">
        <div class="flex items-center justify-between mb-[9px]">
          <div class="flex items-center ">
            <div class="text-[#6C6C74] text-[13px]">
              {{ pageOption.total || 0 }}个结果
            </div>
            <div
              v-if="currentTypeName?.length || currentDiffName?.length"
              class="w-[1px] mx-[6px] h-[7px] bg-[#6C6C74]"
            ></div>
            <div
              v-if="currentTypeName?.length"
              class="px-[7px] flex items-center py-[4px] bg-[#E8ECF6] br-[5px]"
            >
              <div class="text-[13px] text-[#6C6C74] mr-4px translate-y-1px">
                题型:{{
                  currentTypeName?.length > 5
                    ? `${currentTypeName.slice(0, 5)?.join('、')}...`
                    : currentTypeName?.join('、')
                }}
              </div>
              <svg-ri-close-line
                class="text-14px van-haptics-feedback"
                @click="onDeleteType"
              />
            </div>
            <div
              v-if="currentDiffName?.length"
              class="px-[7px] ml-[11px] flex items-center py-[4px] bg-[#E8ECF6] br-[5px]"
            >
              <div class="text-[13px] text-[#6C6C74] mr-4px translate-y-1px">
                难度:{{ currentDiffName?.join('、') }}
              </div>
              <svg-ri-close-line
                class="text-14px van-haptics-feedback"
                @click="onDeleteDifficulty"
              />
            </div>
            <div
              v-if="currentTypeName?.length && currentDiffName?.length"
              class="w-1px h-7px bg-[#6C6C744D] mx-6px"
            />
            <img
              v-if="currentTypeName?.length && currentDiffName?.length"
              src="@/assets/img/taskCenter/bin.png"
              alt="bin icon"
              class="w-17px h-17px van-haptics-feedback"
              @click="onDeleteAll"
            />
          </div>
          <div class="flex items-center min-h-31px">
            <div
              v-if="showAddAll == 1"
              class="text-[15px] text-[#fff] bg-[#6474FD] px-[10px] py-[4px] br-[5px] flex items-center cursor-pointer van-haptics-feedback"
              @click="onAddAll"
            >
              <svg-ri-add-line class="w-[17px] h-[17px] mr-[2px]" />
              <span>全部加入</span>
            </div>
            <div
              v-else-if="showAddAll == 2"
              class="text-[15px] text-[#FF4646] px-[10px] py-[3px] br-[5px] flex items-center cursor-pointer van-haptics-feedback border border-[#FF4646]"
              @click="onRemoveAll"
            >
              <svg-ri-subtract-line class="w-[17px] h-[17px] mr-[2px]" />
              <span>全部移除</span>
            </div>
          </div>
        </div>
        <div
          :style="{
            height: $g.isPC
              ? 'calc(100vh - 241px)'
              : 'calc(100vh - 9vh - 145px)',
            overflowY: 'auto',
          }"
        >
          <g-list
            ref="scrollRef"
            v-model:data="questionList"
            :show-loading="showLoading"
            :page-option="pageOption"
            url="/tutoring/admin/task/book/list"
            @pulldown="pulldown"
            @pullup="pullup"
          >
            <div
              v-for="(item, index) in questionList"
              :key="index"
              class="br-[6px] mb-[9px] border border-[#DADDE8] bg-[#FFFFFF]"
            >
              <QuestionItem :question-item="item"
                            :question-index="index"
                            :current-question-list-all="currentQuestionListAll"
              />
            </div>
          </g-list>
        </div>
      </div>
    </div>
    <el-drawer v-model="showDrawer"
               size="427"
               :show-close="false"
    >
      <template #header="{ close }">
        <div class="flex justify-between">
          <div class="text-[17px] font-600 text-[#333333]">
            切换校本练习
          </div>
          <img
            src="@/assets/img/taskCenter/close.png"
            alt="close icon"
            class="w-15px h-15px select-none van-haptics-feedback"
            @click="close"
          />
        </div>
      </template>
      <div class="flex items-center justify-between">
        <div
          v-if="gradeList?.length"
          id="grade"
          ref="boxRef"
          class="overflow-hidden cursor-pointer max-w-[221px] br-[6px] bg-[#FBFBFB] border-[1px] border-[#DADDE8] relative"
        >
          <div
            :style="{ width: `${58 * gradeList.length + 10}px` }"
            class="flex px-[5px] py-[5px]"
          >
            <div
              v-for="(item, index) in gradeList"
              :key="index"
              :class="{
                'bg-[#E9ECF5] br-[4px] text-[#6C6C74] item-active':
                  item?.sysGradeId == currentGrade,
              }"
              class="w-[58px] selcet-none flex-shrink-0 px-[14px] py-[3px] flex-cc cursor-pointer text-[15px]"
              @click="onGradeSelect(item)"
            >
              {{ item?.sysGradeName }}
            </div>
          </div>
        </div>
        <div
          v-else
          class="w-[221px] h-[38px] border-[1px] border-[#ffffff]"
        ></div>
        <div class="relative min-h-[34px]">
          <el-select-v2
            v-model="currentSchool"
            placeholder="请选择学校"
            size="large"
            :style="{ width: '128px', opacity: 0, position: 'absolute' }"
            :options="schoolList"
            :props="{
              value: 'schoolId',
              label: 'schoolName',
            }"
          >
          </el-select-v2>
          <div
            v-if="currentName"
            class="w-[128px] justify-between cursor-pointer flex items-center border text-[15px] text-[#6C6C74] border-[#DADDE8] br-[5px] bg-[#FBFBFB] px-[8px] py-[4px]"
          >
            <span class="truncate">{{ currentName }}</span>
            <svg-common-expand class="w-[17px] ml-[2px] h-[17px]" />
          </div>
        </div>
      </div>
      <div class="mt-[17px]">
        <g-list
          ref="scrollRef"
          v-model:data="bookList"
          :show-loading="loading"
          :page-option="pageBookOption"
          url="/tutoring/admin/task/book/list"
          @pulldown="bookPulldown"
          @pullup="bookPullup"
        >
          <g-loading v-if="loading" class="h-[200px]"></g-loading>
          <div v-else>
            <div
              v-for="(item, index) in bookList"
              :key="index"
              :class="{
                'border-[3px] border-[#A6D9FF]':
                  currentBook?.bookId == item?.bookId,
              }"
              class="bookBgDrawer cursor-pointer px-[16px] py-[12px] mb-[16px] br-[6px] w-full h-[92px]"
              @click="onBookClick(item)"
            >
              <div
                class="h-[43px] mb-[4px]"
              >
                <g-mathjax :text="item?.bookName" class="text-[#333333] line-2 h-[43px] text-[15px] font-600"></g-mathjax>
              </div>
              <div class="text-[#6C6C74] text-[14px] flex items-center">
                <div>{{ item?.sysGradeName }}</div>
                <div
                  v-if="item?.sysGradeName"
                  class="bg-[#6C6C74] w-[1px] h-[10px] mx-[12px]"
                ></div>
                <div>{{ item?.sysSubjectName }}</div>
                <div
                  v-if="item?.schoolList?.length"
                  class="bg-[#6C6C74] w-[1px] h-[10px] mx-[12px]"
                ></div>
                <div v-if="item?.schoolList?.length > 1">
                  {{ item?.schoolList?.[0]?.schoolName }}...
                </div>
                <div v-else>
                  {{ item?.schoolList?.[0]?.schoolName }}
                </div>
              </div>
            </div>
          </div>
        </g-list>
      </div>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-drawer__header {
    padding: 17px 21px 0px 17px;
    margin-bottom: 0px;
  }
  .el-drawer__body {
    padding: 21px;
  }
  .el-select--large .el-select__wrapper {
    min-height: 34px !important;
  }
  .questionType,
  .difficulty {
    .el-select--large .el-select__wrapper {
      max-height: 20px !important;
      min-height: 20px !important;
    }
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: rgba(100, 116, 253, 0.1) !important;
    border-radius: 6px !important;
    color: #6474fd !important;
    font-weight: 500 !important;
  }
}
.my-tree {
  --el-tree-node-content-height: 34px;
  .text{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  white-space: pre-wrap;
  -webkit-line-clamp: 2;  /* 显示2行 ‌:ml-citation{ref="2,5" data="citationList"} */
  overflow: hidden;}
  :deep() {
    .el-tree-node__content {
      font-size: 13px;
      height:auto !important;
      min-height: 34px;
      padding:4px 0px;
    }
    .el-tree-node__expand-icon {
      font-size: 13px !important;
      color: #676a88;
    }
    .el-tree-node__content:hover {
      background-color: rgba(100, 116, 253, 0.1) !important;
      border-radius: 6px;
    }
  }
}
.bookBgDrawer {
  background: url(@/assets/img/taskCenter/bookBg.png) center / cover no-repeat;
}
</style>
