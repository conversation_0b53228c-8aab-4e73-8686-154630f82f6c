<script setup lang="ts" name="ResourceReport">
import {
  getClass<PERSON>ist<PERSON><PERSON>,
  getGradeSelectApi,
  getStudentListApi,
  getTaskProfileApi,
  getTaskProgressApi,
} from '@/api/taskCenter'
import { useUserStore } from '@/stores/modules/user'
import PreviewFile from '../createTask/components/PreviewFile.vue'

let classList = $ref<any>([])
const userStore = useUserStore()
let currentClass = $ref<any>(null)
let currentGrade = $ref<any>(null)
let showFile = $ref<any>(false)
let drawer = $ref<any>(false)
let currentFile = $ref<any>(null)
let resData = $ref<any>(null)
let activeName = $ref<any>('学生报告')
const route = useRoute()
let gradeList = $ref<any>(null)
let taskName: any = $computed(() => {
  return route.query?.taskName
})
let currentClassName = $computed(() => {
  return classList?.find(item => item?.schoolClassId == currentClass)?.className
})
let currentGradeName = $computed(() => {
  return gradeList?.find(item => item?.sysGradeId == currentGrade)?.sysGradeName
})
let tabList = $ref<any>([
  {
    label: '学生报告',
    name: '学生报告',
  },
])
function onDrawClick() {
  drawer = true
}
let info = $ref<any>(null)
const tableOptions = reactive({
  loading: false,
  column: [
    {
      prop: 'fileName',
      label: '资源名称',
      slot: true,
    },
    {
      prop: 'finishStudentCount',
      label: '完成人数',
      formatter(row) {
        const finishStudentCount = row.finishStudentCount || 0
        return `${finishStudentCount}/${row.classStudentCount}`
      },
    },
    {
      prop: 'finishProgress',
      label: '进度',
      formatter(row) {
        return `${row.finishProgress}%`
      },
    },
    {
      prop: 'cz',
      label: '操作',
      slot: true,
    },
  ],
  data: [] as any,
})
const tableOptions1 = reactive({
  loading: false,
  column: [
    {
      prop: 'userName',
      label: '姓名',
    },
    {
      prop: 'progress',
      label: '进度',
      sort: true,
      formatter(row) {
        if (!row.progress)
          return '--'

        return `${row.progress}%`
      },
    },
    {
      prop: 'spentTime',
      label: '学习用时',
      sort: true,
      formatter(row) {
        if (!row?.spentTime)
          return '--'

        const time = $g.dayjs.duration(row.spentTime, 'seconds')
        let hour = time.hours()
        let minutes = time.minutes()
        let seconds = time.seconds()
        let str = ''
        if (hour)
          str = `${hour}时`

        if (minutes)
          str = `${str + minutes}分`

        if (seconds)
          str = `${str + seconds}秒`

        return str
      },
    },
    {
      prop: 'finishTime',
      label: '完成时间',
      sort: true,
      formatter(row) {
        if (!row?.finishTime)
          return '--'

        return $g.dayjs(row?.finishTime).format('YYYY-MM-DD HH:mm')
      },
    },
  ],
  data: [] as any,
})
function onPreviewClick(item) {
  showFile = true
  currentFile = item
}
function getTime(info, item) {
  if (!item) {
    info.seconds = 0
    return
  }
  const time = $g.dayjs.duration(item, 'seconds')
  let hour = time.hours()
  let minutes = time.minutes()
  let seconds = time.seconds()
  info.hour = hour || 0
  info.minutes = minutes || 0
  info.seconds = seconds || 0
}
async function getTaskProfile() {
  try {
    const res = await getTaskProfileApi({
      taskId: route?.query?.taskId,
      schoolClassId: currentClass,
      sysGradeId: currentGrade,
    })
    info = res
    getTime(info, info.averageTimeSpent)
  }
  catch (err) {
  }
}
async function getTaskProgress() {
  try {
    tableOptions.loading = true
    const res = await getTaskProgressApi({
      taskId: route?.query?.taskId,
      schoolClassId: currentClass,
      sysGradeId: currentGrade,
    })
    tableOptions.data = res || []
    tableOptions.loading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (err) {
    tableOptions.loading = false
  }
}
async function getStudentList() {
  try {
    tableOptions1.loading = true
    const res = await getStudentListApi({
      taskId: route?.query?.taskId,
      schoolClassId: currentClass,
      sysGradeId: currentGrade,
    })
    tableOptions1.data = res
    tableOptions1.loading = false
    resData = res?.length ? [...res] : []
  }
  catch (err) {
    tableOptions1.loading = false
  }
}
function sortChange(obj) {
  if (obj.prop == 'finishTime') {
    if (obj.order) {
      tableOptions1.data.sort((a, b) => {
        if (obj.order == 'asc') {
          return (
            $g.dayjs(a[obj.prop] || 0).unix() -
            $g.dayjs(b[obj.prop] || 0).unix()
          )
        }
        else {
          return (
            $g.dayjs(b[obj.prop] || 0).unix() -
            $g.dayjs(a[obj.prop] || 0).unix()
          )
        }
      })
    }
    else {
      tableOptions1.data = [...resData]
    }
  }
  else {
    if (obj.order) {
      tableOptions1.data.sort((a, b) => {
        if (obj.order == 'asc')
          return a[obj.prop] - b[obj.prop]

        else
          return b[obj.prop] - a[obj.prop]
      })
    }
    else {
      tableOptions1.data = [...resData]
    }
  }
}
async function getGradeList() {
  try {
    const res = await getGradeSelectApi({
      exerciseSourceType: 2,
      exerciseSourceId: route?.query?.taskId,
      schoolId: userStore.userInfo.schoolId,
    })
    gradeList = res || []
    currentGrade = gradeList[0]?.sysGradeId
  }
  catch (err) {}
}
async function getClassList() {
  try {
    const res = await getClassListApi({
      exerciseSourceType: 2,
      exerciseSourceId: route?.query?.taskId,
      schoolId: userStore.userInfo.schoolId,
      sysGradeId: currentGrade,
    })
    classList = res || []
    currentClass = classList[0]?.schoolClassId
  }
  catch (err) {

  }
}
watch(() => route?.query?.taskId, () => {
  if (route?.query?.taskId)
    getGradeList()
}, { immediate: true })
watch(() => currentGrade, () => {
  classList = []
  currentClass = null
  if (currentGrade != null)
    getClassList()
})
watch(() => currentClass, () => {
  info = null
  tableOptions.data = []
  tableOptions1.data = []
  resData = []
  if (currentClass != null) {
    getTaskProfile()
    getTaskProgress()
    getStudentList()
  }
})
let instructionInfo = $ref<any>([
  {
    title: '报告总览',
    expand: true,
    subTitle:
      '以班级整体展示数据，展示在当前数据范围下的班级任务概况，学生多次作答，只取第一次的作答数据',
    contentList: [
      {
        title: '完成人数',
        expand: false,
        content: '在当前数据范围下，当前班级下，完成整个资源任务人数/当前班级任务总人数',
      },
      {
        title: '平均进度',
        expand: false,
        content:
          '在当前数据范围下，班级的平均进度。平均进度=（视频进度+其他资源进度）/2',
      },
      {
        title: '平均学习用时',
        expand: false,
        content:
          '班级学生学习用时的平均数。仅统计视频类，已观看的视频时长总数/已观看视频的总人数*100%',
      },
    ],
  },
  {
    title: '学生列表',
    expand: false,
    subTitle: '以学生个体展示数据，展示在当前数据范围下的学生任务详情',
    contentList: [
      {
        title: '进度',
        expand: false,
        content:
          '在当前数据范围下，本人进度=本人已完成的子任务个数/本人需完成的任务总数*100%',
      },
      {
        title: '学习用时',
        expand: false,
        content:
          '该学生查看适配类资源的用时，多次观看不累加',
      },
      {
        title: '完成时间',
        expand: false,
        content:
          '该学生完成整个资源任务的时间，需看完全部视频和看完所有文档',
      },
    ],
  },
])

watch(
  () => showFile,
  (val) => {
    if (!val) {
      setTimeout(() => {
        currentFile = {}
      }, 350)
    }
  },
)
</script>

<template>
  <div class="p-[26px]">
    <g-navbar>
      <template #title>
        <div class="line-1" :class="$g.isPC ? 'max-w-[700px]' : 'max-w-[300px]'">
          {{ taskName }}
        </div>
      </template>
      <template #right>
        <div class="flex items-center mr-[17px]">
          <div class="mr-[13px] text-[15px] text-[#333333]">
            年级
          </div>
          <div class="relative min-h-[34px]">
            <el-select
              v-model="currentGrade"
              placeholder="Select"
              fit-input-width
              size="large"
              :style="{ width: '128px', opacity: 0, position: 'absolute' }"
            >
              <el-option
                v-for="item in gradeList"
                :key="item.sysGradeId"
                :label="item.sysGradeName"
                :value="item.sysGradeId"
              />
            </el-select>
            <div
              class="w-[128px] justify-between cursor-pointer flex items-center border text-[15px] text-[#6C6C74] border-[#DADDE8] br-[5px] bg-[#FBFBFB] px-[8px] py-[4px]"
            >
              <span class="truncate">{{ currentGradeName || '暂无年级' }}</span>
              <svg-common-expand class="w-[17px] ml-[2px] h-[17px]" />
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-[13px] text-[15px] text-[#333333]">
            班级
          </div>
          <div class="relative min-h-[34px]">
            <el-select
              v-model="currentClass"
              placeholder="Select"
              fit-input-width
              size="large"
              :style="{ width: '149px', opacity: 0, position: 'absolute' }"
            >
              <el-option
                v-for="item in classList"
                :key="item.schoolClassId"
                :label="item.className"
                :value="item.schoolClassId"
              />
            </el-select>
            <div
              class="w-[149px] justify-between cursor-pointer flex items-center border text-[15px] text-[#6C6C74] border-[#DADDE8] br-[5px] bg-[#FBFBFB] px-[8px] py-[4px]"
            >
              <span class="truncate">{{ currentClassName || '暂无班级' }}</span>
              <svg-common-expand class="w-[17px] ml-[2px] h-[17px]" />
            </div>
          </div>
        </div>
        <div
          class="flex items-center ml-[17px] cursor-pointer"
          @click="onDrawClick"
        >
          <img
            src="@/assets/img/taskCenter/instruction.png"
            class="w-19px h-19px"
            alt=""
          />
          <span class="ml-[1px] text-[#666666] text-[15px]">指标说明</span>
        </div>
      </template>
    </g-navbar>
    <el-tabs v-model="activeName" class="mt-[16px]">
      <el-tab-pane
        v-for="(item, index) in tabList"
        :key="index"
        :label="item?.label"
        :name="item?.name"
      >
      </el-tab-pane>
    </el-tabs>
    <div class="p-[16px] bg-[#FFFFFF] br-[6px] mt-[13px] mb-[17px]">
      <div class="flex items-center">
        <div class="flex items-center">
          <div>
            <div class="font-600">
              <span class="text-[26px] text-[#6474FD]">
                {{
                  info?.finishStudentNum || 0
                }}
              </span>/<span class="text-[13px] text-[#333333]">
                {{
                  info?.studentNum || 0
                }}
              </span>
            </div>
            <div class="text-[13px] text-[#929296] mt-[5px]">
              完成人数
            </div>
          </div>
        </div>
        <div class="flex-1 flex justify-center">
          <div class="w-[1px] h-[26px] bg-[#CCCCCC]"></div>
        </div>
        <div class="flex items-center">
          <div>
            <div>
              <span class="text-[26px] font-600">
                {{
                  info?.averageProgress || 0
                }}
              </span><span class="text-[12px]">%</span>
            </div>
            <div class="text-[13px] text-[#929296] mt-[5px]">
              平均进度
            </div>
          </div>
        </div>
        <div class="flex-1 flex justify-center">
          <div class="w-[1px] h-[26px] bg-[#CCCCCC]"></div>
        </div>
        <div class="flex justify-between">
          <div>
            <div>
              <span v-if="info?.hour" class="text-[26px] font-600">
                {{
                  info?.hour
                }}<span class="text-[13px] font-400">时</span>
              </span>
              <span v-if="info?.minutes" class="text-[26px] font-600">
                {{
                  info?.minutes
                }}<span class="text-[13px] font-400">分</span>
              </span>
              <span v-if="info?.seconds" class="text-[26px] font-600">
                {{
                  info?.seconds
                }}<span class="text-[13px] font-400">秒</span>
              </span>
              <span v-if="!info?.hour && !info?.minutes && !info?.seconds" class="text-[26px] font-600">0<span class="text-[13px] font-400">秒</span></span>
            </div>
            <div class="text-[13px] text-[#929296] mt-[5px]">
              平均学习用时
            </div>
          </div>
        </div>
        <div class="flex-1 flex justify-center">
          <div class="w-[1px] h-[26px] bg-[#CCCCCC]"></div>
        </div>
        <div class="flex  items-center">
          <div>
            <div v-if="info?.averageAnswerQuestionNum">
              <span class="text-[26px] font-600">
                {{
                  info?.averageAnswerQuestionNum
                }}
              </span><span class="text-[13px]">题</span>
            </div>
            <div v-else>
              <span class="text-[26px] font-600">--</span>
            </div>
            <div class="text-[13px] text-[#929296] mt-[5px]">
              平均答题数
            </div>
          </div>
        </div>
        <div class="flex-1 flex justify-center">
          <div class="w-[1px] h-[26px] bg-[#CCCCCC]"></div>
        </div>
        <div class="flex items-center">
          <div>
            <div v-if="info?.averageAccuracy">
              <span class="text-[26px] font-600">
                {{
                  info?.averageAccuracy
                }}
              </span><span class="text-[13px]">%</span>
            </div>
            <div v-else>
              <span class="text-[26px] font-600">--</span>
            </div>
            <div class="text-[13px] text-[#929296] mt-[5px]">
              平均正确率
            </div>
          </div>
        </div>
        <div class="flex-1 flex justify-center">
          <div class="w-[1px] h-[26px] bg-[#CCCCCC]"></div>
        </div>
        <div class="flex items-center">
          <div>
            <div class="flex items-center mt-[2px]">
              <span class="text-[26px] text-theme-primary font-600">
                {{
                  info?.unlearnedStudentNum || 0
                }}
              </span><span class="text-[13px] mt-[2px]">人</span>
              <el-popover
                :popper-style="{ padding: '14px 9px' }"
                :disabled="!info?.unlearnedStudentList?.length"
                placement="top"
                :width="343"
                trigger="hover"
              >
                <template #reference>
                  <div v-if="info?.unlearnedStudentList?.length" class="mt-[2px] cursor-pointer">
                    <img
                      src="@/assets/img/taskCenter/studentTip.png"
                      class="w-16px h-16px"
                      alt=""
                    />
                  </div>
                </template>
                <div
                  :style="{ display: 'flex', flexWrap: 'wrap', maxHeight: '171px', overflowY: 'auto', gap: info?.unlearnedStudentList?.length > 16 ? '6px' : '9px' }"
                >
                  <div
                    v-for="(item, index) in info?.unlearnedStudentList"
                    :key="index"
                    :style="{ background: '#F3F4F9', width: '73px', height: '26px', display: 'flex', justifyContent: 'center', alignItems: 'center', borderRadius: '4px' }"
                  >
                    <span class="line-1 text-[#333333] text-[15px]">{{ item?.studentName }}</span>
                  </div>
                </div>
              </el-popover>
            </div>
            <div class="text-[13px] text-[#929296]">
              未学习人数
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[#FFFFFF] br-[6px] p-[16px]">
      <div class="text-[#333333] text-[15px] font-600">
        任务进度总览
      </div>
      <g-table
        stripe
        :border="false"
        :header-cell-style="{
          background: 'rgba(100,116,253,0.1)!important',
          color: '#6C6C74',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :table-options="tableOptions"
      >
        <template #fileName="{ row }">
          <g-mathjax :text="row?.fileName"></g-mathjax>
        </template>
        <template #cz="{ row }">
          <div class="text-[13px] text-theme-primary cursor-pointer" @click="onPreviewClick(row)">
            预览
          </div>
        </template>
      </g-table>
    </div>
    <div class="bg-[#FFFFFF] br-[6px] p-[16px] my-[17px]">
      <div class="text-[#333333] text-[15px] font-600">
        学生列表
      </div>
      <g-table
        stripe
        :border="false"
        :header-cell-style="{
          background: 'rgba(100,116,253,0.1)!important',
          color: '#6C6C74',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :table-options="tableOptions1"
        @sort-change="sortChange"
      >
      </g-table>
    </div>
    <PreviewFile v-model:show="showFile" :current-file="currentFile" />
    <el-drawer v-model="drawer"
               size="476px"
               title="指标说明"
    >
      <div>
        <div
          v-for="(item, index) in instructionInfo"
          :key="index"
          class="w-full bg-[#FFFFFF] mb-[17px] br-[6px] py-[17px] px-[17px]"
        >
          <div class="flex cursor-pointer justify-between" @click="item.expand = !item.expand">
            <div class="text-[#333333] text-[17px] font-600">
              {{ item?.title }}
            </div>
            <img
              :src="item?.expand ? $g.tool.getFileUrl('taskCenter/wNoExpand.png') : $g.tool.getFileUrl('taskCenter/wExpand.png')"
              class="w-13px h-13px"
              alt=""
            />
          </div>
          <div v-if="item.expand" class="text-[#666666] text-[14px] mt-[13px]">
            {{ item?.subTitle }}
          </div>
          <div v-if="item.expand">
            <div
              v-for="(subItem, subIndex) in item.contentList"
              :key="subIndex"
              class="bg-[#F3F4F9] br-[6px] px-[13px] py-[13px] mt-[13px]"
            >
              <div
                class="flex justify-between cursor-pointer"
                @click="subItem.expand = !subItem.expand"
              >
                <div class="text-[#333333] text-[15px] font-600">
                  {{ subItem?.title }}
                </div>
                <img
                  :src="subItem?.expand ? $g.tool.getFileUrl('taskCenter/wNoExpand.png') : $g.tool.getFileUrl('taskCenter/wExpand.png')"
                  class="w-13px h-13px"
                  alt=""
                />
              </div>
              <div
                v-if="subItem.expand"
                class="text-[#666666] text-[14px] mt-[13px]"
              >
                {{ subItem?.content }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-table--striped
    .el-table__body
    tr.el-table__row--striped
    td.el-table__cell {
    background: #EFF1FE !important;
  }
  .el-table .sort-caret.ascending {
    border-bottom-color: rgb(153 153 153 / 80%);
  }
  .el-table .sort-caret.descending {
    border-top-color: rgb(153 153 153 / 80%);
  }
  .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #6C6C74;
  }
  .el-table .descending .sort-caret.descending {
    border-top-color: #6C6C74;
  }
  .el-tabs__nav-wrap::after {
    background-color: #f3f3f3;
  }
  .el-tabs__item {
    color: #333333;
    font-size: 15px;
  }
  .el-tabs__item.is-active {
    color: #6474FD;
    font-size: 15px;
    font-weight: 500;
  }
  .el-tabs__header{
    margin:0px !important;
  }
  .right-main .n-scrollbar-container {
    background: #f3f4f9 !important;
  }
  .el-drawer__header {
    margin-bottom: 0px !important;
    padding: 21px !important;
    color: #333333;
    font-weight: 600;
    font-size: 17px;
  }
  .el-drawer__body {
    padding-top: 0px;
    padding-left: 21px;
    padding-right: 21px;
  }
  .el-drawer__title{
    font-size: 17px !important;
  }
  .el-drawer {
    background-color: #f3f4f9;
  }
  .el-tabs__item {
    padding: 0 12px;
  }
}
</style>
