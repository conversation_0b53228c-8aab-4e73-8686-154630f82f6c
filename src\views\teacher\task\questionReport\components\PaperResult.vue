<script setup lang="ts">
import { getQuestionListDetail<PERSON>pi, getTeacherRemindApi } from '@/api/taskCenter'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import QuestionPanel from './QuestionPanel.vue'
import QuestionStemItem from './QuestionStemItem.vue'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const props = defineProps({
  reportAll: {
    type: Object,
    required: true,
  },
  params: {
    type: Object,
    required: true,
  },
})
const isActivity = $computed(() => {
  return (route.query.exerciseSourceType as any) == 1
})
const router = useRouter()
const route = useRoute()
let showLoading = $ref(true)
let scrollerRef = $ref<any>(null)
let sortType = $ref(1)
let sortList = $ref([
  {
    label: '按题目布置顺序排序',
    value: 1,
  },
  {
    label: '按正确率升序',
    value: 2,
  },
  {
    label: '按正确率降序',
    value: 3,
  },
])

const questionList = $computed(() => {
  let arr = [...props.reportAll.paperReport.questionList].filter(Boolean)
  if (sortType === 1) return arr
  if (sortType === 2) {
    return arr.sort((a, b) => {
      return a.correctRate - b.correctRate
    })
  }
  if (sortType === 3) {
    return arr.sort((a, b) => {
      return b.correctRate - a.correctRate
    })
  }
  return []
})

watch(() => props.reportAll.paperReport.questionList, (val) => {
  if (val)
    getQuestion()
}, {
  immediate: true,
})
async function getQuestion() {
  if (!props.reportAll.paperReport.questionList.length) {
    showLoading = false
    return
  }
  showLoading = true
  let res = await getQuestionListDetailApi({
    questionIdList: props.reportAll.paperReport.questionList.map(v => v.questionId),
  })
  props.reportAll.paperReport.questionList.forEach((item, index) => {
    let find = res?.find(v => v.questionId == item.questionId) || {}
    Object.assign(item, {
      ...find,
      questionIndex: index + 1,
      isObjective: find.subQuestions.every(h => [1,
2,
3].includes(h.subQuestionType)),
    })
  })
  setTimeout(async () => {
    showLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }, 500)
}

async function remind() {
  await getTeacherRemindApi({
    taskId: route.query.exerciseSourceId,
    type: 2,
    schoolStudentIds: props.reportAll.paperReport.noCorrectStudentList.map(v => v.schoolStudentId),
  })
  $g.showToast('提醒成功')
}
function goDetail(item, index) {
  router.push({
    name: 'QuestionDetail',
    query: {
      ...route.query,
      ...props.params,
      quesIndex: index,
      questionId: item?.questionId ?? '',
    },
  })
}
function onQuesSelect([item, index]) {
  scrollerRef.scrollToItem(index)
  setTimeout(async () => {
    await $g.tool.renderMathjax()
    nextTick(() => scrollerRef.scrollToItem(index))
  }, 100)
}
</script>

<template>
  <div>
    <div class="flex justify-between mb-11px">
      <div class="text-[#6C6C74] text-15px">
        共<span class="text-[#6474FD] ml-4px">{{ reportAll.paperReport.questionList?.length }}</span>
        道题（已按{{ [null, '题目布置顺序排序', '正确率升序', '正确率降序'][sortType] }}）
      </div>
      <div class="flex items-center">
        <el-select v-model="sortType" class="w-181px h-34px br-[5px]">
          <el-option
            v-for="item in sortList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-popover
          v-if="reportAll.paperReport.noCorrectStudentList?.length && !isActivity"
          :popper-style="{ padding: '15px 11px' }"
          placement="bottom"
          :width="350"
          trigger="hover"
          :teleported="false"
          :popper-options="{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [-64, 12],
                },
              },
            ],
          }"
        >
          <template #reference>
            <div class="flex items-center  cursor-pointer ml-13px font-500 h-23px lh-[23px]">
              <img :src="$g.tool.getFileUrl('taskCenter/purpleInfo.png')"
                   alt=""
                   class="w-16px h-16px mr-6px"
              >
              未自批学生：<span class="text-[#FF4646]">{{ reportAll.paperReport.noCorrectStudentList.length }}</span>人
            </div>
          </template>
          <div>
            <el-scrollbar maxheight="180px" always>
              <div class="flex flex-wrap gap-x-[11px] gap-y-[11px] ">
                <div v-for="item in reportAll.paperReport.noCorrectStudentList"
                     :key="item.schoolStudentId"
                     class="bg-[#F3F4F9] br-[4px]  h-26px lh-[26px] text-15px w-71px flex-cc px-2px"
                >
                  <div class="truncate">
                    {{ item.studentName }}
                  </div>
                </div>
              </div>
            </el-scrollbar>
            <div class="flex justify-end mt-8px">
              <el-button type="primary"
                         class="h-26px"
                         @click="remind"
              >
                一键提醒尽快完成自查
              </el-button>
            </div>
          </div>
        </el-popover>
        <el-button
          v-if="route.query.configCorrect === '1'"
          type="primary"
          class="ml-17px w-70px h-30px"
          @click="$router.push({ name: 'CorrectionPage',
                                 query: {
                                   taskId: route.query.exerciseSourceId,
                                 } })"
        >
          去批改
        </el-button>
      </div>
    </div>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>

    <template v-else>
      <DynamicScroller
        ref="scrollerRef"
        :items="questionList"
        :min-item-size="300"
        :buffer="500"
        key-field="questionId"
        class="h-[78vh]"
      >
        <template #default="{ item, index, active }">
          <DynamicScrollerItem
            :item="item"
            :active="active"
            :size-dependencies="['questionTitle']"
            :data-index="index"
            class="pb-1px"
          >
            <QuestionStemItem
              :key="item.questionId"
              :question-item="item"
              :index="item.questionIndex"
            >
              <template #footer>
                <div class="mt-12px flex justify-between ml-16px">
                  <div class="flex items-center text-15px text-[#666666] font-400">
                    <div>
                      正确率：<span
                        v-if="typeof item.correctRate === 'number'"
                        :class="{
                          'text-[#F5222D]': 60 > item.correctRate,
                          'text-[#FAAD14]':
                            60 <= item.correctRate
                            && item.correctRate < 70,
                          'text-[#1EA0F0]':
                            70 <= item.correctRate
                            && item.correctRate < 85,
                          'text-[#52C41A]': 85 <= item.correctRate,
                        }"
                      >
                        {{ item.correctRate }}%
                      </span>
                      <span v-else>--</span>
                    </div>
                    <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                    <el-popover
                      trigger="hover"
                      append-to="#app"
                      :width="200"
                      :disabled="!item.errorNum"
                    >
                      <template #reference>
                        <div :class="item.errorNum && 'cursor-pointer'">
                          {{ item.errorNum }}人错误
                        </div>
                      </template>
                      <div class="pb-20px max-h-200px overflow-y-auto">
                        <div class="w-full flex items-center mb-8px">
                          <div class="flex items-center w-246px">
                            <img
                              src="@/assets/img/taskCenter/cross.png"
                              alt="cross icon"
                              class="w-16px h-16px mr-6px"
                            />
                            <div class="text-14px text-[#333]">
                              错误
                            </div>
                          </div>
                        </div>
                        <div
                          class="flex flex-wrap gap-10px"
                        >
                          <template
                            v-if="item.errorStudentList?.length"
                          >
                            <div
                              v-for="(errorItem, errorIdx) in item
                                .errorStudentList"
                              :key="errorIdx"
                              class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                            >
                              {{ errorItem.studentName }}
                            </div>
                          </template>
                          <span v-else class="text-[#999]">无学生</span>
                        </div>
                      </div>
                    </el-popover>
                    <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>

                    <el-popover
                      trigger="hover"
                      :width="200"
                      append-to="#app"
                      :disabled="!item.answerNum"
                    >
                      <template #reference>
                        <div :class="item.answerNum && 'cursor-pointer'">
                          {{ item.answerNum }}人作答
                        </div>
                      </template>
                      <div class="pb-15px max-h-200px overflow-y-auto">
                        <div class="flex items-center mb-8px">
                          <img
                            src="@/assets/img/taskCenter/pen.png"
                            alt="pen icon"
                            class="w-16px h-16px mr-6px"
                          />
                          <div class="text-14px text-[#333]">
                            作答
                          </div>
                        </div>
                        <div
                          class="flex flex-wrap gap-10px"
                        >
                          <template
                            v-if="item.answerStudentList?.length"
                          >
                            <div
                              v-for="(finishItem, finishIdx) in item.answerStudentList"
                              :key="finishIdx"
                              class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                            >
                              {{ finishItem.studentName }}
                            </div>
                          </template>
                          <span v-else class="text-[#999]">无学生</span>
                        </div>
                      </div>
                    </el-popover>
                    <div v-if="!isActivity" class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                    <el-popover
                      v-if="!isActivity"
                      trigger="hover"
                      append-to="#app"
                      :width="200"
                      :disabled="!item.noAnswerNum"
                    >
                      <template #reference>
                        <div :class="item.noAnswerNum && 'cursor-pointer'">
                          {{
                            item.noAnswerNum ?? 0
                          }}人未作答
                        </div>
                      </template>
                      <div class="pb-15px max-h-200px overflow-y-auto">
                        <div class="flex items-center mb-8px">
                          <img
                            src="@/assets/img/taskCenter/warn.png"
                            alt="warn icon"
                            class="w-16px h-16px mr-6px"
                          />
                          <div class="text-14px text-[#333]">
                            未作答
                          </div>
                        </div>
                        <div
                          class="flex flex-wrap gap-10px"
                        >
                          <template
                            v-if="item.noAnswerStudentList?.length"
                          >
                            <div
                              v-for="(unfinishItem, unfinishIdx) in item
                                .noAnswerStudentList"
                              :key="unfinishIdx"
                              class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                            >
                              {{ unfinishItem.studentName }}
                            </div>
                          </template>
                          <span v-else class="text-[#999]">无学生</span>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                  <el-button class="w-64px h-30px bg-white text-[#6474FD] border border-[#6474FD]" @click="goDetail(item, index)">
                    查看
                  </el-button>
                </div>
              </template>
            </QuestionStemItem>
          </DynamicScrollerItem>
        </template>
      </DynamicScroller>

      <g-empty v-if="!questionList?.length"></g-empty>
    </template>
    <QuestionPanel :ques-list="questionList" @select="onQuesSelect"></QuestionPanel>
  </div>
</template>

<style scoped lang="scss"></style>
