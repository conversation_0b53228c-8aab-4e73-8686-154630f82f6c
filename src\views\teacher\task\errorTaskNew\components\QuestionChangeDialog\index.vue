<script setup lang="ts">
import { getVectorDetail, recommendConfirm } from '@/api/taskCenter'
import VariantQuestion from '../VariantQuestion.vue'
import KnowledgeTree from './components/KnowledgeTree.vue'

const props = defineProps<{
  questionId: any
  bookId: any
  difficulty: string
  knowledgePoint: Array<any>
  structureNumber: any
  parallelPaperType: number
  sysCourseId: any
  questionItem: any
  variantQuestionId: any
  from?: string
}>()

const emit = defineEmits(['getVariantQuestion'])
const show = defineModel<boolean>('show', { required: true })
const knowledgeTreeRef: any = $ref(null)
let checkedData = $ref<any[]>([]) // 已选知识点
let defaultOpenKey = $ref<any[]>([]) // 默认展开项
const examName = inject<Ref<any>>('examName', ref(''))
let questionList: any = $ref([]) // 题目列表
let showLoading = $ref(true)
const pageOption = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})
let onceInit = $ref(false) // 首次请求
let buttonLoading = $ref(false) // 题目替换按钮loading
let newKnowledgePoint: any = $ref([]) // 最多保留3个知识点

// 已选知识点id
const checkedId = $computed(() => {
  return checkedData?.map(item => item.sysKnowledgePointId)
})

// 默认展开项id
const defaultOpenKeyId = $computed(() => {
  return defaultOpenKey?.map(item => item.sysKnowledgePointId)
})

// 删除知识点
function deleteKnowledge(data) {
  knowledgeTreeRef.deleteCheckedData(data.sysKnowledgePointId)
}

async function pullup() {
  pageOption.page += 1
  await getQuestionData(true)
}

function initPage() {
  pageOption.page = 1
  if (questionList.length > 0) questionList = []
  getQuestionData()
}

// 获取题目列表
async function getQuestionData(up = false) {
  try {
    if (!up) showLoading = true
    const res = await getVectorDetail({
      page: pageOption.page,
      pageSize: pageOption.pageSize,
      sysCourseId: props.sysCourseId,
      sysKnowledgePointIdList: checkedId || [],
      sysQuestionTypeId: props.questionItem.sysQuestionTypeId,
      minScore: 0.0,
      sysQuestionDifficultyIdList: props.difficulty == '难' ? [21] : props.difficulty == '中' ? [20, 19] : [18, 17],
    })
    res.list = res.list.map((item) => {
      return {
        ...item,
        id: item.questionId + $g.tool.uuid(2),
      }
    })
    questionList = up ? [...questionList, ...res.list] : res?.list || []
    pageOption.total = res.total
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (error) {
    questionList = []
    console.log('⚡[ error ] >', error)
  }
  finally {
    onceInit = false
    showLoading = false
  }
}

// 题目换用
async function setQuestion(data) {
  try {
    if (buttonLoading) return
    if (props.from == 'aiTask') {
      emit('getVariantQuestion', data)
      show.value = false
      return
    }
    buttonLoading = true
    await recommendConfirm({
      bookId: props.bookId,
      questionId: props.questionId,
      parallelPaperType: props.parallelPaperType,
      recommendQuestionId: data.questionId,
    })
    emit('getVariantQuestion')
    show.value = false
    $g.msg('题目更换成功！', 'success')
  }
  catch (error) {
    $g.msg('题目更换失败！', 'error')
    console.log('⚡[ error ] >', error)
  }
  finally {
    buttonLoading = false
  }
}

watch(() => checkedId.join(''), () => {
  if (onceInit) return
  showLoading = true
  initPage()
})

watch(() => show.value, (newVal) => {
  if (newVal) {
    showLoading = true
    onceInit = true
    newKnowledgePoint = props.knowledgePoint.length <= 3 ? props.knowledgePoint : props.knowledgePoint.slice(0, 3)
    defaultOpenKey = $g._.cloneDeep(newKnowledgePoint || [])
    checkedData = $g._.cloneDeep(newKnowledgePoint || [])
    initPage()
  }
}, { immediate: true })
</script>

<template>
  <transition name="van-fade">
    <div v-show="show"
         class="fixed top-0 left-0 w-full h-full bg-[rgba(0,0,0,0.5)] z-[2000]"
         @click.stop="show = false"
    >
    </div>
  </transition>
  <transition name="van-slide-right">
    <div v-show="show"
         class="fixed top-0 right-0 flex flex-col w-[90%] h-full bg-[white] px-21px pt-17px z-[2001]"
         @click.stop
    >
      <div class="flex-shrink-0">
        <div class="text-center font-600 text-17px flex justify-between items-center">
          <div class="flex-1 van-ellipsis text-left mr-20px">
            {{ from == 'aiTask' ? '' : `${examName}考试第${structureNumber}题变式题更换` }}
          </div>
          <img
            :src="$g.tool.getFileUrl('taskCenter/close.png')"
            alt=""
            class="h-15px w-15px van-haptics-feedback"
            @click="show = false"
          >
        </div>

        <p class=" text-13px text-[#666] mt-9px van-ellipsis">
          <span>以下题目难度等级均为</span>
          <span class="text-[#333]">【{{ props.difficulty }}】</span>
          <span v-if="knowledgePoint?.length > 0">
            ，原题知识点：
            <span v-for="(item, index) in newKnowledgePoint" :key="item.sysKnowledgePointId">{{ item.sysKnowledgePointName }}{{ index == knowledgePoint.length - 1 ? '' : '、' }}</span>
          </span>
        </p>
      </div>
      <div class="flex items-center mt-13px flex-shrink-0 h-30px">
        <div class="mr-21px text-14px font-600 flex-shrink-0">
          已选知识点
        </div>
        <el-tag
          v-for="tag in checkedData"
          :key="tag.sysKnowledgePointName"
          closable
          class="mr-21px h-30px border-0 br-[5px]"
          @close="deleteKnowledge(tag)"
        >
          <div class="van-ellipsis text-14px" :class="$g.isPC ? 'max-w-[350px]' : 'max-w-[200px]'">
            {{ tag.sysKnowledgePointName }}
          </div>
        </el-tag>
      </div>
      <div class="flex-1 flex min-h-[0px] overflow-hidden mt-13px">
        <KnowledgeTree
          ref="knowledgeTreeRef"
          class="flex-shrink-0"
          :checked-data="checkedData"
          :default-open-key="defaultOpenKeyId"
          :sys-course-id="questionItem.sysCourseId"
          :show="show"
          @get-question-data="getQuestionData"
        />
        <div class="flex-1 ml-17px h-full overflow-auto pb-20px">
          <g-virtual-list
            :list-data="questionList"
            item-key="id"
            :size-dependencies="['subQuestions', 'questionTitle', 'showAnswer', 'subQuestionTitle', 'subQuestionParse']"
            :min-item-size="150"
            :buffer="500"
            :page-option="pageOption"
            :show-loading="showLoading"
            @pullup="pullup"
          >
            <template #default="{ item, index }">
              <div v-if="questionList.length > 0" class="border border-[#DADDE8] br-[13px] mb-11px p-17px">
                <VariantQuestion :question-item="item" :order="`${index + 1}. `">
                  <template #footerRight>
                    <div
                      v-if="item.questionId != variantQuestionId"
                      class="border border-[#6474FD] px-13px py-6px br-[6px] flex items-center van-haptics-feedback"
                      @click="setQuestion(item)"
                    >
                      <img src="@/assets/img/taskCenter/change.png"
                           alt=""
                           class="w-17px h-17px mr-6px"
                      >
                      <span class="font-500 text-[#6474FD]">换用此题</span>
                    </div>
                    <div v-else class="px-13px py-6px br-[6px] flex items-center">
                      <img src="@/assets/img/taskCenter/changed.png"
                           alt=""
                           class="w-17px h-17px mr-6px"
                      >
                      <span class="font-500 text-[#52C41A]">已换用此题</span>
                    </div>
                  </template>
                </VariantQuestion>
              </div>
            </template>
          </g-virtual-list>
        </div>
      </div>
    </div>
  </transition>
</template>

<style lang="scss" scoped>
:deep(){
  .el-dialog__body{
    height: 100%;
  }
}
</style>
