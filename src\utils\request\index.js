import { useUserStore } from '@/stores/modules/user'
import { getToken } from '@/utils/token'
import axios from 'axios'
import handleData from './handleData'
import loading from './loading'

const CancelToken = axios.CancelToken
const currentRequestList = []
let cancelArr = []

/**
 * @description axios初始化
 */
const server = axios.create({
  timeout: 30 * 1000,
  headers: {
    // 配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
    'Content-Type': 'application/json;charset=UTF-8',
  },
  showTip: true, // 显示提示信息
  showLoading: false, // 默认不显示loading弹窗
  returnAll: false, // false-只返回data数据 true-返回全部数据(包括code,msg...)
  replace: false, // 是否替换旧的请求，相同url新请求替换旧请求,用于tab快速切换重复请求
  delay: true, // 默认给每个接口添加200ms延迟，在某些情况可能有会影响，可以关闭
  isLogin: true, // 是否需要验证登录code码
})

/**
 * @description axios请求拦截器
 */
server.interceptors.request.use(
  async (config) => {
    let {
      token,
      version,
      platform,
      jztToken,
    } = $(storeToRefs(useUserStore()))

    // 展示加载
    if (config.showLoading)
      config.loadTimer = setTimeout(() => loading.open(), 100)

    // 合并请求头, API配置中的headers优先级最高
    config.headers = {
      token,
      version,
      platform,
      ...config.headers,
    }

    // 金字塔接口使用金字塔token,PC端优先使用cookie中的token
    if (config.url.includes(import.meta.env.VITE_JZT_API)) {
      let jztTokenCookie = getToken('jzt_token', 'cookie')
      config.headers.token = $g.isPC && jztTokenCookie ? jztTokenCookie : jztToken
    }

    // 如果 header 的值为空，则 删除该 key
    config.headers = $g._.omitBy(config.headers, value => !value)
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

/**
 * @description axios响应拦截器
 */
server.interceptors.response.use(
  (response) => {
    // 删除取消请求
    const index = cancelArr.findIndex((item) => {
      return item.key == response?.config?.url
    })
    cancelArr.splice(index, 1)
    deleteCurrentRequest(response?.config)
    if (response.config.showLoading) {
      clearTimeout(response.config.loadTimer)

      setTimeout(() => {
        loading.close()
      }, 200)
    }
    return handleData(response)
  },
  (error) => {
    const {
      response,
      config,
    } = error

    // 关闭loading
    if (config?.showLoading) {
      clearTimeout(config.loadTimer)
      loading.close()
    }

    // 取消请求不处理
    if (axios.isCancel(error)) return Promise.reject('取消请求')

    // 错误提示
    if (config?.showTip !== false) {
      let errorMsg = '服务器异常，请稍后重试'

      // CORS跨域错误
      if (error.code === 'CORS_ERROR' || error.message === 'Network Error') {
        errorMsg = '网络请求失败'
      }
      // 网络连接错误
      else if (error.message?.includes('Network Error')) {
        errorMsg = '网络连接失败，请检查您的网络'
      }
      // 请求超时
      else if (error.message?.includes('timeout')) {
        errorMsg = '请求超时，请稍后重试'
      }
      // 服务器返回的错误
      else if (response) {
        const status = response.status

        if (status === 401)
          errorMsg = '登录状态已过期，请重新登录'
          // 可以在此处触发退出登录逻辑

        else if (status === 403)
          errorMsg = '没有权限访问该资源'

        else if (status === 404)
          errorMsg = '请求的资源不存在'

        else if (status === 501)
          errorMsg = '服务器不支持请求的功能'

        else if (status >= 500)
          errorMsg = `服务器错误(${status})`

        else if (response.data?.message)
          errorMsg = response.data.message
      }

      console.error(errorMsg)
      $g.showToast(errorMsg)
    }

    return Promise.reject(error)
  },
)

/**
 * @description axios请求兼容 争对业务处理 取消重复请求
 */
function request(config) {
  config.method = config.method || 'get'
  if (config.method.toLowerCase() === 'get') {
    /* 删除分页接口多余参数 */
    if ($g.tool.typeOf(config.data) == 'object')
      delete config.data.total

    config.params = config.data
    delete config.data
  }
  const params = getAxiosParams(config)

  const item = {
    url: config.url,
    params,
  }

  currentRequestList.push(item)
  return server({
    ...config,
    cancelToken: new CancelToken((c) => {
      // 如果replace为true，取消cancelArr重复请求，只保留最后一次请求
      if (config.replace) {
        // 取消请求 并删除item
        cancelArr = cancelArr.filter((item) => {
          if (item.key == config.url) {
            item.c() // 取消请求
            return false // 从数组中删除该项
          }
          return true // 保留其他项
        })
        cancelArr.push({
          key: config.url,
          c,
        })
      }
    }),
  })
}

const methods = ['get',
'post',
'delete',
'put',
'patch']
methods.forEach((type) => {
  request[type] = (url, data, options) => {
    return request({
      url,
      method: type,
      data,
      ...options,
    })
  }
})

function getAxiosParams(config) {
  const dataType = ['post']
  return dataType.includes(config.method) ? config.data : config.params
}

function deleteCurrentRequest(config = {}) {
  $g._.remove(currentRequestList, (n) => {
    return n.url == config.url
  })
}

export default request
