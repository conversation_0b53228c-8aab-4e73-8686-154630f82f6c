<script setup>
import { Overlay } from 'vant'

const props = defineProps({
  id: String,
  url: {
    type: String,
    default: '',
  },
  config: {
    type: Object,
    default: () => ({}),
  },
})
const GVideo = defineAsyncComponent(
  () => import('@/components/global/g-video/index.vue'), // 异步加载组件
)
let show = $ref(false)

onMounted(() => {
  show = true
})

watch(
  () => show,
  (newVal) => {
    if (!newVal) {
      let element = document.getElementById(props.id)
      element.remove()
    }
  },
)
</script>

<template>
  <Overlay v-model:show="show" @click="show = false">
    <div class="video-wrapper flex-cc" @click.stop>
      <div class="inner">
        <g-icon
          name="svg-yx-cancel"
          size="40"
          class="close"
          @click="show = false"
        />
        <GVideo :url="url"
                class="video"
                :config="config"
        />
      </div>
    </div>
  </Overlay>
</template>

<style lang="scss">
.video-wrapper {
  width: 100vw;
  height: 100vh;
  position: relative;
  .inner {
    width: 80%;
    height: 80%;
    .close {
      position: absolute;
      right: 40px;
      top: 40px;
      cursor: pointer !important;
    }
    .video {
      padding-top: 0 !important;
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
