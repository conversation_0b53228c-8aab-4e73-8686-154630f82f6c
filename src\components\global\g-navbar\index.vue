<script setup lang="ts">
const props = defineProps({
  showBack: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '',
  },
  onBack: {
    type: Function,
  },
})

const emit = defineEmits(['back'])

const goBack = $g._.throttle(
  () => {
    if (props.onBack) {
      props.onBack()
      return
    }
    $g.flutter('back')
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
)

const route = useRoute()
// 路由参数中的返回按钮显示状态
const defaultShowBack = $computed(() => {
  return route.query.showBack === 'true'
})

// 根据优先级判断是否显示返回按钮
const showBackButton = $computed(() => {
  if (route.query.showBack !== undefined)
    return defaultShowBack

  // 否则使用 props 中的 showBack 值
  return props.showBack
})
</script>

<template>
  <div class="flex justify-between items-center relative">
    <div class="flex-cc">
      <slot name="left">
        <div
          v-if="showBackButton"
          class="w-34px h-34px br-[50%] p-7px cursor-pointer bg-[#fff] mr-11px"
          @click="goBack"
        >
          <img src="@/assets/img/demo/left.png"
               class="w-19px h-19px"
               alt=""
          >
        </div>
        <div class="title font-600 text-17px lh-[34px] flex-1">
          <slot name="title">
            {{ title }}
          </slot>
        </div>
      </slot>
    </div>

    <div class="absolute left-1/2 transform -translate-x-1/2 text-center">
      <slot name="center" />
    </div>

    <div class="flex items-center">
      <slot name="right" />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
