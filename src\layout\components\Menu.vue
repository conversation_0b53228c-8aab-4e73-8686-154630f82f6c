<script lang="ts" setup>
import { oldTeacherTip } from '@/api/taskCenter'
import { useRouterStore } from '@/stores/modules/router'
import { driver } from 'driver.js'
import 'driver.js/dist/driver.css'

const collapsed = defineModel('collapsed', {
  type: Boolean,
  default: false,
})

const route = useRoute()
const router = useRouter()
const routesStore = useRouterStore()
const { getRoutes } = toRefs(routesStore)

// 获取激活菜单
const activeMenu = $computed<string>(() => {
  const parentRoute = route.matched[1]
  const currentRoute = route.matched[route.matched.length - 1]
  return String(
    currentRoute?.meta?.activeName ||
    parentRoute?.name ||
    currentRoute?.name ||
    '',
  )
})

const handleRoutesArr = $computed(() => {
  const showMenuArr = getRoutes.value[0]?.children?.filter(v => v.meta?.showMenu)
  return showMenuArr
})

// 判断是否显示子菜单
function hasVisibleChildren(menu) {
  if (!menu.children || menu.children.length === 0) { return false }
  return menu.children.some(child => !child.meta?.hidden)
}

// 获取可见的子菜单
function getVisibleChildren(menu) {
  if (!menu.children) { return [] }
  return menu.children.filter(child => !child.meta?.hidden)
}

/** 菜单选择处理 */
function handleSelect(name: string) {
  router.push({ name })
}

/** 旧版任务提示和跳转 */
let driverObj: any = $ref(null)
onMounted(async () => {
  if (!$g.isPC || route.name != 'TeacherHome') return
  const res = await oldTeacherTip({ type: 'ONCE_WARN_NEW_TASK' })
  // 开始引导
  if (res) {
    driverObj = driver({
      steps: [
        {
          element: '#oldTeacher',
          popover: {
            title: '', // 引导标题
            description:
              '当前是新版发任务界面，暂不支持教师批改，若要发布教师批改任务请点击此处进入老版发任务界面', // 引导描述
            doneBtnText: '我知道了',
            showButtons: ['next'],
            popoverClass: 'driver-button-class',
          },
        },
      ],
      allowClose: false,
    })
    driverObj.drive()
  }
})

// 跳转旧发布任务页
function toNewTeacher() {
  const host = import.meta.env.VITE_ZXS_TEACHER
  if (driverObj) driverObj.destroy()
  window.location.href = `${host}/#/wisdomWork`
}
</script>

<template>
  <div class="h-screen flex">
    <!-- 展开状态 -->
    <div
      id="left-bar"
      class="!bg-white shadow-md h-full relative transition-all duration-300 overflow-hidden"
      :class="[collapsed ? 'w-0' : ($g.isPC ? 'w-180px' : 'w-140px')]"
    >
      <div :class="$g.isPC ? 'w-180px' : 'w-140px'">
        <div class="pt-21px pb-18px flex items-center justify-center">
          <img
            :src="$g.tool.getFileUrl('menu/menu-icon.png')"
            class="mr-5px"
            :class="$g.isPC ? 'w-[16px]' : 'w-[14px]'"
          />
          <span class="rzyt" :class="$g.isPC ? 'text-16px' : 'text-13px'">智习室作业</span>
        </div>

        <!-- 菜单项 -->
        <div class="pl-19px pr-20px">
          <el-menu
            :default-active="activeMenu"
            class="border-none"
            text-color="#6C6C74"
            @select="handleSelect"
          >
            <template v-for="menu in handleRoutesArr" :key="menu.name">
              <!-- 有可见子菜单的情况 -->
              <el-sub-menu v-if="hasVisibleChildren(menu)" :index="menu.name">
                <template #title>
                  <svg-menu-task-center class="mr-6px w-[1.2em]" />
                  <span>{{ menu?.meta?.title }}</span>
                </template>
                <el-menu-item
                  v-for="subMenu in getVisibleChildren(menu)"
                  :key="subMenu.name"
                  :index="subMenu.name"
                  :class="{ 'text-[#6474FD] is-active': activeMenu == subMenu.name }"
                >
                  <span>{{ subMenu?.meta?.title }}</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 无子菜单或所有子菜单都隐藏的情况 -->
              <el-menu-item
                v-else
                :index="menu.redirect?.name || menu.name"
                :class="{ 'text-[#6474FD] is-active': activeMenu == menu.name }"
              >
                <svg-menu-task-center class="mr-6px w-[1.2em]" />
                <span>{{ menu?.meta?.title }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </div>
      </div>
      <!-- <img
        v-if="$g.isPC && route.name == 'TeacherHome'"
        id="oldTeacher"
        src="@/assets/img/taskCenter/oldTeacher.png"
        alt="oldTeacher"
        class="w-[115px] h-33px cursor-pointer select-none absolute left-[50%] translate-x-[-50%] bottom-[10px]"
        :class="$g.isPC ? 'hover:opacity-70' : ''"
        draggable="false"
        @click="toNewTeacher"
      > -->
    </div>

    <!-- 收起状态 - 只显示按钮 -->
    <div
      v-if="$g.isPC"
      class="w-11px h-52px flex items-center justify-center bg-[#E0E2EF] transition-transform duration-300 cursor-pointer"
      :class="[
        collapsed
          ? 'fixed left-0 top-[50%] -translate-y-1/2 rounded-[0_25px_25px_0]'
          : 'absolute right-0 top-[50%] -translate-y-1/2 rounded-[25px_0_0_25px]',
      ]"
      @click="collapsed = !collapsed"
    >
      <!-- todo: 图标也定位，是因为不定位会导致图标无法显示正常大小 -->
      <svg-ri-arrow-right-wide-line
        class="transition-transform duration-300 text-[#707382] text-[14px]"
        :class="[
          collapsed
            ? 'fixed left-[-4px] top-[50%] -translate-y-1/2'
            : 'rotate-180 absolute right-[-4px] top-[50%] -translate-y-1/2',
        ]"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-menu-item {
    display: flex;
    align-items: center;
    height: 37px;
    padding: 0 !important;
    padding-left: 12px !important;
    margin-bottom: 11px;
    &.is-active {
      background-color: #ebf1ff;
      border-radius: 4px;
    }
  }

  .el-sub-menu {
    .el-sub-menu__title {
      display: flex;
      align-items: center;
      height: 37px;
      padding: 0 !important;
      border-radius: 4px;
      padding-left: 12px !important;
      margin-bottom: 11px;
      &:hover {
        background-color: #f5f5f5;
      }
    }

    .el-menu-item {
      padding-left: 35px !important;
    }
  }
}
</style>
