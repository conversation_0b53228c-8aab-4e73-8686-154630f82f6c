import $g from '@/utils/index'
import { loadOnScroll } from './directives'
import { registerSentry } from './sentry'
import { initTheme } from './theme'
import vant from './vant'
import { initVConsole } from './vconsole'
import { registeredVMdEditor } from './vMdEditor'
import 'vant/lib/index.css'

const plugins = { $g }

export function setupPlugins(app) {
  initTheme()
  registerSentry(app)
  registeredVMdEditor(app)
  // 注册全局变量
  Object.keys(plugins).forEach((key) => {
    app.provide(key, plugins[key as keyof typeof plugins])
    app.config.globalProperties[key] = plugins[key as keyof typeof plugins]
  })

  // 注册vant
  const { vantComponents } = vant
  Object.keys(vantComponents).forEach((key) => {
    if (key == 'Lazyload')
      app.use(vantComponents[key], { lazyComponent: true })
  })

  // 注册指令
  app.directive('loadOnScroll', loadOnScroll)

  initVConsole()

  if ($g.isPC)
    import('@vant/touch-emulator')
}
