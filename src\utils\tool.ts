import config from '@/config/index'
import ClipboardJS from 'clipboard'
import CryptoJS from 'crypto-js'
import J<PERSON><PERSON><PERSON> from 'jszip'
import { format } from 'timeago.js'
import * as XLSX from 'xlsx'
import MathBigNumber from './math'

function mathFn(num) {
  return new MathBigNumber(num)
}

/* mathjax */
const TypeSet = async function (elClass = 'g-mathjax') {
  if (!window.MathJax)
    return

  const node = document.getElementsByClassName(elClass)
  window.MathJax.startup.promise = window.MathJax.startup.promise
    .then(() => {
      return window.MathJax.typesetPromise(node)
    })
    .catch(err => console.log(`Typeset failed: ${err.message}`))
  return window.MathJax.startup.promise
}

const tool = {
  /**
   * 描述  获取文件后缀名
   * @param {string} filename
   */
  getExt(filename: string) {
    if (typeof filename === 'string') {
      // 先移除URL参数部分（问号及其后面的所有内容）
      const cleanFilename = filename.split('?')[0]
      return cleanFilename.split('.').pop()?.toLowerCase()
    }
    else {
      throw new TypeError('filename must be a string type')
    }
  },

  /**
   * @description 将url请求参数转为json格式
   * @param url
   */
  paramObj(qs, sep, eq, options) {
    sep = sep || '&'
    eq = eq || '='
    const obj = {}

    if (typeof qs !== 'string' || qs.length === 0)
      return obj

    if (qs.includes('?'))
      qs = qs.substr(qs.indexOf('?') + 1)

    const regexp = /\+/g
    qs = qs.split(sep)

    let maxKeys = 1000
    if (options && typeof options.maxKeys === 'number')
      maxKeys = options.maxKeys

    let len = qs.length // maxKeys <= 0 means that we should not limit keys count
    if (maxKeys > 0 && len > maxKeys)
      len = maxKeys

    for (let i = 0; i < len; ++i) {
      const x = qs[i].replace(regexp, '%20')
      const idx = x.indexOf(eq)
      let kstr, vstr

      if (idx >= 0) {
        kstr = x.substr(0, idx)
        vstr = x.substr(idx + 1)
      }
      else {
        kstr = x
        vstr = ''
      }

      const k = decodeURIComponent(kstr)
      const v = decodeURIComponent(vstr)

      if (!Object.prototype.hasOwnProperty.call(obj, k))
        obj[k] = v

      else if (Array.isArray(obj[k]))
        obj[k].push(v)

      else
        obj[k] = [obj[k], v]
    }

    return obj
  },

  /**
   * @description 获取随机id
   * @param {number} length - 生成随机字符串的长度
   * @returns {string} 返回指定长度的随机字符串，由大小写字母和数字组成
   */
  uuid(length = 32): string {
    const num = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
    let str = ''
    for (let i = 0; i < length; i++)
      str += num.charAt(Math.floor(Math.random() * num.length))

    return str
  },

  /**
   * 判断是否为存在
   */
  isTrue(a: any) {
    if (a === 0)
      return false
    // 检验空字符串
    if (a === '')
      return false
    // 检验空字符串
    if (a === 'null')
      return false
    // 检验字符串类型的null
    if (a === 'undefined')
      return false
    // 检验字符串类型的 undefined
    if (!a && a !== 0 && a !== '')
      return false
    // 检验 undefined 和 null
    if (Array?.prototype?.isPrototypeOf(a) && a.length === 0)
      return false
    // 检验空数组
    if (Object?.prototype?.isPrototypeOf(a) && Object.keys(a).length === 0)
      return false
    // 检验空对象
    return true
  },

  /**
   * 获取静态资源地址
   */
  getFileUrl(url: string) {
    return new URL(`../assets/img/${url}`, import.meta.url).href
  },

  /**
   * 获取静态资源地址
   */
  getOSSUrl(url: string) {
    return config.baseOssImg + url
  },

  /**
   * 判断精准类型
   */
  typeOf(obj) {
    const toString = Object.prototype.toString
    const map = {
      '[object Boolean]': 'boolean',
      '[object Number]': Number.isNaN(obj) ? 'NaN' : 'number',
      '[object String]': 'string',
      '[object Function]': 'function',
      '[object Array]': 'array',
      '[object Date]': 'date',
      '[object RegExp]': 'regExp',
      '[object Undefined]': 'undefined',
      '[object Null]': 'null',
      '[object Object]': 'object',
    }
    return map[toString.call(obj)]
  },

  isAndroid() {
    return window.navigator.userAgent.toLowerCase().includes('android')
  },

  isIOS() {
    return !!window.navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
  },

  isPC() {
    return /Win32|Win64|Mac/i.test(navigator.platform)
  },

  isPCTest() {
    return tool.isPC() && !(import.meta.env.VITE_APP_ENV == 'production')
  },

  isPCDevelopment() {
    return tool.isPC() && import.meta.env.VITE_APP_ENV == 'development'
  },

  /* 多久之前 */
  timeAgo(val) {
    const time = new Date(val?.replace(/-/g, '/')) // 先将接收到的json格式的日期数据转换成可用的js对象日期
    return format(time, 'zh_CN') // 转换成类似于几天前的格式
  },

  /* 更新mathjax数据 */
  renderMathjax(elClass?) {
    return new Promise((resolve) => {
      nextTick(() => {
        TypeSet(elClass).then(() => {
          resolve(true)
        })
      })
    })
  },

  /* 将参数放到url上 */
  paramsToUrl(query) {
    const params: any[] = []
    Object.entries(query).forEach(([key, val]) => {
      params.push(`${key}=${val}`)
    })
    const path = `${location.hash.split('?')[0]}?${params.join('&')}`
    history.replaceState(history.state, '', path)
  },

  /* 动态加载js */
  loadJS(url: string) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = url
      document.body.appendChild(script)
      script.onload = function () {
        resolve(`success: ${url}`)
      }
      script.onerror = function () {
        reject(new Error(`${url} load error!`))
      }
    })
  },

  /* 资源类型 */
  resourceType(type) {
    const newType: any = tool.getExt(type)
    const imgType = ['jpg',
'png',
'jpeg',
'heic',
'heif',
'gif',
'webp']
    const videoType = ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8']
    const audioType = ['mp3',
'ogg',
'wav',
'aac']
    const fileType = ['doc',
'docx',
'xls',
'xlsx',
'pdf',
'ppt',
'pptx']

    if (imgType.includes(newType))
      return 'img'

    else if (videoType.includes(newType))
      return 'video'

    else if (audioType.includes(newType))
      return 'audio'

    else if (fileType.includes(newType))
      return 'file'
  },

  /* 下载 */
  downloadFile(url, filename) {
    if (!url)
      return

    const link = document.createElement('a') // 创建a标签
    link.style.display = 'none' // 使其隐藏
    link.href = url // 赋予文件下载地址
    link.setAttribute('download', filename) // 设置下载属性 以及文件名
    document.body.appendChild(link) // a标签插至页面中
    link.click() // 强制触发a标签事件
    document.body.removeChild(link)
  },

  /* mqtt */
  decryptMqttPassword(clientId, ciphertext) {
    function getMD5Hash(input) {
      const md5Hash = CryptoJS.MD5(input)
      return md5Hash.toString()
    }
    try {
      const split = clientId.split('_')
      const temp = split[0] + split[2]
      let key = getMD5Hash(temp).substring(8, 24)
      let iv = getMD5Hash(key).substring(8, 24)
      key = CryptoJS.enc.Utf8.parse(key)
      iv = CryptoJS.enc.Utf8.parse(iv)
      const encryptedHexStr = CryptoJS.enc.Hex.parse(ciphertext)
      const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
      const decrypt = CryptoJS.AES.decrypt(srcs, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      })
      return decrypt.toString(CryptoJS.enc.Utf8)
    }
    catch (error) {
      return null
    }
  },

  /* 通过文件后缀获取对应文件类型缩略图 */
  getFileTypeIcon(suffix) {
    suffix = suffix.toLowerCase()
    const fileTypeIcon = {
      doc: ['doc', 'docx'],
      pdf: ['pdf'],
      xlsx: ['xls', 'xlsx'],
      txt: ['txt'],
      mp3: ['mp3',
'wav',
'wma',
'ape',
'aac'],
      mp4: ['mp4',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'mov'],
      img: ['jpg',
'jpeg',
'png',
'gif',
'bmp',
'svg'],
      ppt: ['ppt', 'pptx'],
      zip: ['zip'],
      rar: ['rar'],
    }
    let name = ''
    Object.keys(fileTypeIcon).some((key) => {
      if (fileTypeIcon[key].includes(suffix)) {
        name = key
        return true
      }
    })
    const url = this.getFileUrl(`fileImg/${name.toUpperCase()}.png`)
    return url
  },

  /* 文字编码 */
  unicodeToZH(str) {
    str = str?.replace(/(\\u)(\w{1,4})/gi, ($0) => {
      return String.fromCharCode(
        Number.parseInt(escape($0).replace(/(%5Cu)(\w{1,4})/g, '$2'), 16),
      )
    })
    str = str?.replace(/(&#x)(\w{1,4});/gi, ($0) => {
      return String.fromCharCode(
        Number.parseInt(escape($0).replace(/(%26%23x)(\w{1,4})(%3B)/g, '$2'), 16),
      )
    })
    str = str?.replace(/(&#)(\d{1,6});/g, ($0) => {
      return String.fromCharCode(
        Number.parseInt(escape($0).replace(/(%26%23)(\d{1,6})(%3B)/g, '$2')),
      )
    })
    return str
  },

  /* 存储单位转换 */
  formatFileSize(fileSize) {
    if (fileSize < 1024) {
      return `${fileSize}B`
    }
    else if (fileSize < 1024 * 1024) {
      const temp = mathFn(fileSize).divide(1024)
      return `${temp.toFixed().value()}KB`
    }
    else if (fileSize < 1024 * 1024 * 1024) {
      const temp = mathFn(fileSize).divide(1024 * 1024)
      return `${temp.toFixed().value()}MB`
    }
    else {
      const temp = mathFn(fileSize).divide(1024 * 1024 * 1024)
      return `${temp.toFixed(2).value()}GB`
    }
  },

  /**
   * 点击复制
   */
  copyData(target, text, msg, cb) {
    const clipboard = new ClipboardJS(target, {
      text() {
        return text
      },
    })
    clipboard.on(
      'success',
      cb ||
      (() => {
        $g.showToast(`链接已复制，${msg}`)
      }),
    )
  },

  /**
   * 长按函数
   */
  longPress(dom, durationInSeconds, callback) {
    let pressTimer

    // 监听 touchstart 事件
    dom?.addEventListener('touchstart', (event) => {
      // 清除计时器
      clearTimeout(pressTimer)

      // 设置计时器，在指定秒数后执行回调函数
      pressTimer = setTimeout(() => {
        if (typeof callback === 'function')
          callback()
      }, durationInSeconds * 1000)
    })

    // 监听 touchend 或 touchcancel 事件
    dom?.addEventListener('touchend', (event) => {
      // 清除计时器
      clearTimeout(pressTimer)
    })
  },

  /**
   * 获取iframe web365完整地址,适配高清模式
   * @param {string} url - 文件URL
   * @returns {string} 完整的web365预览地址
   */
  getWeb365Url(url: string): string {
    let id = 28777
    const idMap = {
      'qm-cloud.oss-cn-chengdu.aliyuncs.com': 28777,
      'edu-jzt.oss-cn-chengdu.aliyuncs.com': 35356,
    }
    for (const [key, value] of Object.entries(idMap)) {
      if (url.includes(key)) {
        id = value
        break
      }
    }
    let web365Url = `//vip.ow365.cn/?i=${id}&ssl=1&time=${Date.now()}`
    // let web365Url = `//vip.ow365.cn/?i=28777&ssl=1&time=${Date.now()}`
    const suffix: any = this.getExt(url)
    const fileType = {
      doc: {
        suffix: ['doc', 'docx'],
        params: '&n=4',
      },
      pdf: {
        suffix: ['pdf'],
        params: '&n=7',
      },
      ppt: {
        suffix: ['ppt', 'pptx'],
        params: '&n=5',
      },
    }
    for (const [key, value] of Object.entries(fileType)) {
      if (value.suffix.includes(suffix)) {
        web365Url += value.params
        break
      }
    }
    // 添加时间戳防止缓存
    web365Url = `${web365Url}&furl=${url}`
    return web365Url
  },

  /**
   * px换算适配后的px
   * @param {number} num - 设计稿中的px值
   * @param {number} [designWidth] - 设计稿的宽度，默认375px
   * @param {number} [fixedNum] - 保留的小数位数
   * @param {boolean} [needUnit] - 是否需要返回带单位的值，默认带px
   * @returns {string | number} - 适配后的px值，如果needUnit为true，返回带单位的字符串，否则返回数字
   */
  pxConversionAdaptedPx(num, needUnit = true, designWidth = 375, fixedNum = 2) {
    // 将窗口宽度限制为最大500px
    const maxWidth = 500
    const currentWidth = Math.min(window.innerWidth, maxWidth)

    const scaleFactor = currentWidth / designWidth
    const adaptedPx = num * scaleFactor

    return needUnit
      ? `${adaptedPx.toFixed(fixedNum)}px`
      : Number.parseFloat(adaptedPx.toFixed(fixedNum))
  },

  // 阿拉伯数字转汉字
  numberToChinese(num) {
    const chineseNumbers = [
      '零',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
    ]
    const unitNames = ['',
'十',
'百',
'千']
    if (num === 0)
      return chineseNumbers[0]

    let result = ''
    let unitIndex = 0
    while (num > 0) {
      const digit = num % 10
      if (digit > 0 || result !== '')
        result = chineseNumbers[digit] + unitNames[unitIndex] + result

      unitIndex++
      num = Math.floor(num / 10)
    }
    return result
  },
  /**
   * 下载并压缩文件
   * @param {Array} fileList - 文件列表
   * @param {string} zipName - 压缩包名称
   * @param {(loading: boolean) => void} loadingCallback - 加载状态回调函数，参数为boolean类型
   */
  async downloadAndZipFiles(fileList, zipName, loadingCallback: (loading: boolean) => void = () => {}) {
    try {
      if (fileList.length === 0) {
        $g.showToast('文件列表不能为空')
        return
      }

      // 设置loading状态为true
      loadingCallback && loadingCallback(true)

      const zip = new JSZip()

      // 使用Promise.all并行下载所有文件
      const downloadPromises = fileList.map(async ({
        url,
        name,
      }, index) => {
        const response = await fetch(url)
        if (!response.ok)
          throw new Error(`下载文件 ${index + 1} 失败: ${response.statusText}`)

        const blob = await response.blob()
        zip.file(name, blob)
      })

      await Promise.all(downloadPromises)

      const content = await zip.generateAsync({ type: 'blob' })

      const url = window.URL.createObjectURL(content)
      $g.tool.downloadFile(url, zipName)
      window.URL.revokeObjectURL(url)
    }
    catch (error: any) {
      console.log('下载或打包过程中出错:', error)
    }
    finally {
      // 无论成功失败，都设置loading状态为false
      loadingCallback && loadingCallback(false)
    }
  },

  /**
   * 创建多sheet的Excel文件并下载
   * @param {Array} sheetsData - 工作表数组，每个元素包含 {name: '工作表名', data: 数据数组, headers: 表头数组}
   * @param {string} filename - 文件名，默认为 'multi_sheet_export.xlsx'
   * @param {string} errMsg - 错误信息，默认为 '工作表数据不能为空'
   */
  downloadExcel(sheetsData, filename = 'multi_sheet_export.xlsx', errMsg = '工作表数据不能为空') {
    try {
      if (!Array.isArray(sheetsData) || !sheetsData.length) {
        $g.showToast(errMsg)
        return
      }

      // 创建工作簿
      const workbook = XLSX.utils.book_new()

      // 处理每个工作表
      sheetsData.forEach((sheet) => {
        const {
          name,
          data,
          headers,
        } = sheet

        if (!Array.isArray(data) || !data.length) {
          console.warn(`工作表 "${name}" 的数据为空，已跳过`)
          return
        }

        if (!Array.isArray(headers) || !headers.length) {
          console.warn(`工作表 "${name}" 的表头配置为空，已跳过`)
          return
        }

        // 准备数据
        const excelData = data.map((item) => {
          const row = {}
          headers.forEach((header) => {
            row[header.label] = item[header.prop] ?? 0
          })
          return row
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(excelData)

        // 设置单元格样式 - 居中对齐
        const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
        for (let R = range.s.r; R <= range.e.r; ++R) {
          for (let C = range.s.c; C <= range.e.c; ++C) {
            const cell_address = XLSX.utils.encode_cell({
              r: R,
              c: C,
            })
            const cell = worksheet[cell_address]

            if (!cell) continue

            // 初始化样式对象
            if (!cell.s) cell.s = {}

            // 设置垂直居中和水平分散对齐
            cell.s.alignment = {
              vertical: 'center',
              horizontal: 'distributed',
            }

            // 设置列宽
            if (!worksheet['!cols'])
              worksheet['!cols'] = []

            // 根据单元格内容长度设置列宽，最小10，最大50
            const cellValue = cell.v?.toString() || ''
            const width = Math.min(50, Math.max(10, cellValue.length * 2))
            worksheet['!cols'][C] = { wch: width }
          }
        }

        // 将工作表添加到工作簿
        XLSX.utils.book_append_sheet(workbook, worksheet, name)
      })

      // 生成Excel文件并下载
      XLSX.writeFile(workbook, filename)
    }
    catch (error) {
      console.log('导出多工作表Excel失败:', error)
    }
  },
  /**
   * 格式化秒数为时间字符串
   * @param {number} seconds - 秒数
   * @param {Array} [unit] - 单位数组，默认['时', '分钟', '秒']
   * @returns {string} 格式化后的时间字符串
   */
  formatSeconds(seconds: number, unit: string[] = ['时',
'分钟',
'秒']): string {
    if (!seconds && seconds !== 0) return ''

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    let result = ''
    if (hours) result += `${hours}${unit[0]}`
    if (minutes) result += `${minutes}${unit[1]}`
    if (remainingSeconds || !result) result += `${remainingSeconds}${unit[2]}`

    return result
  },
}

export default tool
