<script setup lang="ts">
import { getCourseKnowledge } from '@/api/taskCenter'

const props = defineProps<{
  checkedData: any[]
  defaultOpenKey: any[]
  sysCourseId: any
  show: boolean
}>()
let treeData = $ref<any[]>([])
let keyword = $ref<string>('')
const TreeRef = $ref<any>()
const checkedId = $computed(() => {
  return props.checkedData.map(item => item.sysKnowledgePointId)
})
let showLoading = $ref(true)
let openKeys: any = $ref([])

function checkChange({
  data,
  obj,
}) {
  if (checkedId.join('') == obj.checkedKeys.join('')) return
  const index = props.checkedData.findIndex(item => item.sysKnowledgePointId == data.sysKnowledgePointId)
  if (index != -1)
    props.checkedData.splice(index, 1)

  else if (props.checkedData.length < 3)
    props.checkedData.push(data)
}

function initTreeData(data) {
  if (!data) return
  return data.map((item) => {
    if (item.children) {
      // item.disabled = true
      item.children = initTreeData(item.children)
    }
    // 默认展开项
    if (props.defaultOpenKey.includes(item.sysKnowledgePointId) && !openKeys.includes(item.parentSysKnowledgePointId))
      openKeys.push(item.parentSysKnowledgePointId)

    return item
  })
}

async function getTreeData() {
  try {
    if (!props.sysCourseId) return
    const res = await getCourseKnowledge({ sysCourseId: props.sysCourseId })
    treeData = initTreeData(res)
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
  finally {
    showLoading = false
  }
}

function treeFilter() {
  TreeRef.getFilterNode(keyword)
}

function deleteCheckedData(key) {
  if (!key) return
  TreeRef.setCheckedNode(key, false)
  const index = props.checkedData.findIndex(item => item.sysKnowledgePointId == key)
  props.checkedData.splice(index, 1)
}

watch(() => props.show, (val) => {
  if (val) {
    openKeys = []
    keyword = ''
    initTreeData(treeData)
  }
})

function isOpen(data, id) {
  if ('openTip' in data) return
  const dom = document.querySelector(`#${id}`)
  const open = dom ? dom.scrollWidth <= dom.clientWidth : true
  data.openTip = open
}

onMounted(() => {
  getTreeData()
})

defineExpose({
  deleteCheckedData,
  getTreeData,
})
</script>

<template>
  <div class="w-[258px] h-full overflow-hidden flex flex-col bg-[#F3F4F9] br-[4px] px-9px py-13px">
    <div class="flex-shrink-0 flex items-center justify-between">
      <div class="text-15px font-600">
        知识点目录
      </div>
      <div class="text-[#999] font-400 text-[13px]">
        知识点交集最多支持选3项
      </div>
    </div>
    <div class="flex items-center gap-x-[10px] flex-shrink-0 mt-11px">
      <el-input
        v-model="keyword"
        placeholder="知识点立即查询"
        class="w-[160px]"
        clearable
      />
      <el-button type="primary"
                 class="w-64px"
                 @click="treeFilter"
      >
        搜索
      </el-button>
    </div>
    <div class="flex-1 min-h-[0px] bg-[white] overflow-y-auto overflow-x-hidden mt-10px br-[6px] no-bar">
      <g-loading v-if="showLoading" class="h-200px" />
      <template v-else>
        <g-tree
          v-if="treeData.length > 0 && show"
          ref="TreeRef"
          tree-name="leftTree"
          class="p-10px h-full"
          :tree-data="treeData"
          node-key="sysKnowledgePointId"
          :default-expanded-keys="openKeys"
          :default-checked-keys="checkedId"
          :border="false"
          :tree-line="false"
          auto-expand-parent
          render-after-expand
          multiple
          max="3"
          check-strictly
          :highlight-check="false"
          :check-on-click-node="false"
          style="overflow-y: auto;overflow-x: hidden;"
          :default-props="{
            label: 'sysKnowledgePointName',
          }"
          :check-on-click-leaf="false"
          @radio-change="checkChange"
        >
          <template #body="{ data }">
            <div class="flex items-center justify-between w-full" @mouseover="isOpen(data, `id${data.sysKnowledgePointId}`)">
              <el-tooltip
                effect="dark"
                :content="data.sysKnowledgePointName"
                placement="top"
                :disabled="data.openTip"
                :hide-after="10"
              >
                <div :id="`id${data.sysKnowledgePointId}`" class="van-ellipsis">
                  {{ data.sysKnowledgePointName }}
                </div>
              </el-tooltip>
            </div>
          </template>
        </g-tree>
        <g-empty v-else></g-empty>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
  .custom-tree-node{
    width: 100%;
    overflow: hidden;
  }
  .element-tree-node-label-wrapper{
    width: 100%;
  }
  .el-tree-node__content{
    min-height: 34px;
    border-radius: 6px;
  }
  .el-tree-node__content .el-tree-node__expand-icon{
    color: #676A88;
  }
  .el-tree-node:focus>.el-tree-node__content{
    background-color: #6474FD1A;
    color: #6474FD;
  }
}
</style>
