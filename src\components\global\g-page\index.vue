<script setup lang="ts">
const props = defineProps({
  pageOptions: {
    type: Object,
    default() {
      return {
        page: 1,
        page_size: 10,
        total: 200,
        all: false, // 当前是否选中全选
      }
    },
  },
  showAllButton: {
    type: Boolean,
    default: false,
  },
  align: {
    type: String,
    default: 'center',
  },
  layout: {
    type: String,
    default: 'total,prev,pager,next',
  },
})
const emit = defineEmits(['change', 'showAll'])
let pageRef = $ref<any>(null)

const alignStyle = $computed(() => {
  const alignObj = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
  }
  return alignObj[props.align]
})

// 是否显示"全"按钮，满足两个条件：1.开启showAll 2.总页数>1
const showAllButton = $computed(() => {
  return props.showAllButton
})

const pageSizes = [10,
20,
30]

// 根据属性名获取页码大小
const getPageSize = $computed(() => {
  let flag = 'pageSize' in props.pageOptions
  return {
    field: flag ? 'pageSize' : 'page_size',
  }
})

function changePage(page: number, type?: string) {
  // 切换页码时取消全按钮的选中状态
  props.pageOptions.all = false

  if (type === 'size') {
    props.pageOptions[getPageSize.field] = page

    if (props.pageOptions.page !== 1)
      props.pageOptions.page = 1
  }
  emit('change')
}

/** 点击全字按钮事件 */
function handleShowAll() {
  // 切换全按钮的选中状态
  props.pageOptions.all = !props.pageOptions.all

  // 如果全按钮被选中，需要移除数字按钮的选中状态
  if (props.pageOptions.all) {
    props.pageOptions.page = -1
    emit('showAll')
  }
  else {
    // 取消全选时，将页码重置为第一页
    props.pageOptions.page = 1
    emit('change')
  }
}
</script>

<template>
  <div class="mt-20px text-14px w-full">
    <div class="flex items-center" :class="[alignStyle]">
      <div class="relative" :class="{ 'showAll': showAllButton, 'is-active-all': pageOptions.all }">
        <el-pagination
          id="pageRef"
          ref="pageRef"
          v-model:current-page="pageOptions.page"
          class="flex"
          :layout="layout"
          :total="pageOptions.total"
          v-bind="$attrs"
          :page-size="pageOptions[getPageSize.field]"
          :page-sizes="pageOptions.pageSizes || pageSizes"
          @current-change="changePage"
          @size-change="changePage($event, 'size')"
        />
        <!-- 全按钮 -->
        <div
          v-if="showAllButton"
          class="w-32px h-32px flex items-center justify-center cursor-pointer absolute right-[40px] top-0 border border-[#DCDFE6] rounded-[4px]"
          :class="{ active: pageOptions.all }"
          @click="handleShowAll"
        >
          全
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-pagination .el-select .el-input {
    width: 100px;
    height: 28px;
  }
  .el-select .el-input__inner {
    height: 28px;
  }
  .el-pagination button {
    background-color: transparent;
  }
  .el-pager li {
    background: transparent;
  }
  .showAll {
    .btn-next {
      margin-left: 44px;
    }
  }
  .is-active-all{
    .is-active {
    border: 1px solid #DADADA !important;
    color: #666 !important;
    font-weight: 400 !important;
  }
  }

}
// 全按钮选中样式
.active {
    border: 1px solid #6474FD !important;
    color: #6474FD;
    font-weight: bold;
  }
</style>
