<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['close'])
let show = defineModel('modelValue')
function close() {
  emit('close')
  show.value = false
}
</script>

<template>
  <div class="sidebarContainer"
       :class="{ show }"
       @click.stop
  >
    <div class="relative">
      <div v-if="title" class="sidebarHeader">
        {{ title }}
      </div>
      <div class="absolute top-5px right-0" @click="close">
        <g-icon name="ri-close-line"
                size=""
                color=""
        />
      </div>
    </div>
    <div ref="sidebarContent" class="sidebarContent">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sidebarContainer {
  position: absolute;
  right: -300px;
  top: 110px;
  bottom: 80px;
  width: 300px;
  z-index: 1;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;

  &.show {
    right: 0;
  }

  .closeBtn {
    position: absolute;
    right: 20px;
    top: 12px;
    font-size: 20px;
    cursor: pointer;
  }

  .sidebarHeader {
    width: 100%;
    height: 44px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .sidebarContent {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
</style>
