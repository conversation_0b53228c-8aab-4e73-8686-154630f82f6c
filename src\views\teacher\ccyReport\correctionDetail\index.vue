<script setup lang="ts" name="AiCorrectionDetail">
import { getAutoCorrectResult } from '@/api/ccyReport'

const route = useRoute()

const title = (route.query.title as string) || ''

let showLoading = $ref(true)
let dataList = $ref<any[]>([])
let curIndex = $ref(0)

const currentData = $computed(() => dataList[curIndex] || null)

const currentImg = $computed(() => currentData?.correctionImg || '')
const currentQues = $computed(() => currentData?.question || null)
const currentSummary = $computed(() => currentData?.answerConversion?.msg || [])

const prevDisabled = $computed(() => curIndex === 0)
const nextDisabled = $computed(() => curIndex === dataList.length - 1)

async function prev() {
  if (prevDisabled) return
  if (curIndex > 0) {
    curIndex--
    await nextTick()
    $g.tool.renderMathjax()
  }
}

async function next() {
  if (nextDisabled) return
  if (curIndex < dataList.length - 1) {
    curIndex++
    await nextTick()
    $g.tool.renderMathjax()
  }
}

async function getAutoCorrectResultApi() {
  try {
    showLoading = true
    const data = await getAutoCorrectResult({
      exerciseTaskId: route.query.exerciseTaskId,
    })
    dataList = data
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    console.error(e)
  }
  finally {
    showLoading = false
  }
}

function getTitle(item, number) {
  const isSingle = currentQues?.subQuestions.length === 1
  const title = item.subQuestionTitle || ''
  const subTitle = Object.keys(item)
    .filter(
      key => key.includes('option') && key !== 'optionNumbers' && item[key],
    )
    .map((key) => {
      const option = key.replace('option', '')
      const value = item[key]
      return `<div class="ques-text-row"><span class="text-option">${option}.</span>${value}</div>`
    })
    .join('\n')
  const numberPrefix = isSingle
    ? ''
    : `<span class="ques-num">(${number})</span>`
  return (
    `<div class='ques-row'>${
      numberPrefix
    }<div><div>${
      title
    }</div><div>${
      subTitle
    }</div></div>`
  )
}

function previewImg() {
  $g.flutter('previewImage', {
    urls: [currentImg],
  })
}

onBeforeMount(() => {
  getAutoCorrectResultApi()
})

onMounted(async () => {
  await nextTick()
  $g.tool.renderMathjax()
})
</script>

<template>
  <div class="correction-page h-[100vh] p-26px">
    <div class="flex items-start mb-13px h-58px">
      <g-navbar title="" class="flex-shrink-0"></g-navbar>
      <div class="flex-grow truncate text-left">
        <div class="leading-[27px] font-600 text-[#333] mb-10px">
          <g-mathjax :text="title" class="!text-19px title-row-math truncate" />
        </div>
        <div class="text-15px text-[#999] leading-[21px]">
          Ai老师目前只支持白板及拍照作答结果批改，未作答的题目Ai老师不会进行批改，此处只展示批改成功的题目
        </div>
      </div>
      <div
        class="w-45px flex-shrink-0 whitespace-nowrap flex items-center justify-end"
      >
        <span class="text-19px text-[#6474FD] leading-[23px]">
          {{
            curIndex + 1
          }}
        </span>
        <span class="text-19px text-[#3C1500] leading-[23px]">/{{ dataList.length }}</span>
      </div>
    </div>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <template v-else>
      <g-empty v-if="!dataList.length"></g-empty>
      <template v-else>
        <div class="box-h flex">
          <div
            class="text-17px text-[#333333] w-1/2 mr-17px h-full bg-[#fff] rounded-[13px] border border-solid border-[#EEEEEE] p-17px pr-0"
          >
            <el-scrollbar class="pr-17px">
              <div class="leading-[24px] font-600 mb-13px title-row">
                <span>题目信息</span>
              </div>
              <div class="mb-13px">
                <g-mathjax
                  :text="currentQues?.questionTitle || ''"
                  class="mathjax-p"
                ></g-mathjax>
                <g-mathjax
                  v-for="(item, index) in currentQues?.subQuestions"
                  :key="item.subQuestionId"
                  :text="getTitle(item, index + 1)"
                  class="mathjax-p"
                ></g-mathjax>
              </div>
              <div class="leading-[24px] font-600 mb-13px title-row">
                <span>正确答案</span>
              </div>
              <div class="mb-13px">
                <div
                  v-for="(item, index) in currentQues?.subQuestions"
                  :key="item.subQuestionId"
                  class="flex items-start"
                >
                  <div
                    v-if="currentQues?.subQuestions.length > 1"
                    class="flex-shrink-0 mr-5px"
                  >
                    ({{ index + 1 }})
                  </div>
                  <g-mathjax
                    :text="item.subQuestionAnswer || '-'"
                    class="mathjax-p"
                  ></g-mathjax>
                </div>
              </div>
              <div class="leading-[24px] font-600 mb-13px title-row">
                <span>答案解析</span>
              </div>
              <div class="mb-13px">
                <div
                  v-for="(item, index) in currentQues?.subQuestions"
                  :key="item.subQuestionId"
                  class="flex items-start"
                >
                  <div
                    v-if="currentQues?.subQuestions.length > 1"
                    class="flex-shrink-0 mr-5px"
                  >
                    ({{ index + 1 }})
                  </div>
                  <g-mathjax
                    v-for="sub in item.subQuestionParseList"
                    :key="sub.subQuestionParseId"
                    :text="sub.content || '-'"
                    class="mathjax-p"
                  ></g-mathjax>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <div
            class="right-w text-17px text-[#333333] flex-shrink-0 h-full bg-[#fff] rounded-[13px] border border-solid border-[#EEEEEE] p-17px pr-7px"
          >
            <el-scrollbar class="pr-10px">
              <div class="leading-[24px] font-600 mb-13px">
                AI 批改结果
              </div>
              <div class="right-container-h rounded-[6px] pb-40px">
                <el-scrollbar>
                  <div class="flex items-center mb-17px min-h-22px">
                    <div class="leading-[21px] font-600 title-row">
                      <span>批改结果</span>
                    </div>
                    <div v-if="!currentData">
                      --
                    </div>
                    <div
                      v-else-if="currentData.aiCorrectionState === 3"
                      class="text-15px text-[#00DA8F] ml-18px"
                    >
                      成功
                    </div>
                    <div
                      v-else-if="currentData.aiCorrectionState === 4"
                      class="text-15px text-[#FF4446] ml-18px"
                    >
                      失败<template v-if="currentData.errorMessage">
                        ，原因：{{ currentData.errorMessage }}
                      </template>
                    </div>
                  </div>
                  <div class="leading-[21px] font-600 mb-13px title-row">
                    <span>AI 总结</span>
                  </div>
                  <div
                    class="bg-[#F5F5F5] rounded-[6px] mb-17px p-13px h-[111px] overflow-y-auto"
                  >
                    <div
                      v-if="!currentSummary.length"
                      class="w-full h-full flex items-center justify-center text-14px text-[#929292]"
                    >
                      此题没有AI总结内容
                    </div>
                    <div
                      v-for="item in currentSummary"
                      :key="item.index"
                      class="flex items-start"
                    >
                      <div class="flex-shrink-0 mr-5px">
                        ({{ item.index }})
                      </div>
                      <g-mathjax
                        :text="item.comment || '-'"
                        class="mathjax-p"
                      ></g-mathjax>
                    </div>
                  </div>
                  <div class="leading-[21px] font-600 mb-13px title-row">
                    <span>批改详情</span>
                  </div>
                  <img
                    v-if="currentImg"
                    :src="currentImg"
                    alt="cover"
                    class="w-full object-contain select-none"
                    @click="previewImg"
                  />
                </el-scrollbar>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div class="fixed right-31px bottom-38px z-[10] flex items-center">
          <div
            class="ques-btn mr-26px"
            :class="{
              'grayscale-[100%] cursor-not-allowed': prevDisabled,
              'van-haptics-feedback': !prevDisabled,
            }"
            @click="prev"
          >
            上一题
          </div>
          <div
            class="ques-btn"
            :class="{
              'grayscale-[100%] cursor-not-allowed': nextDisabled,
              'van-haptics-feedback': !nextDisabled,
            }"
            @click="next"
          >
            下一题
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.box-h {
  height: calc(100vh - 26px - 26px - 58px - 10px);
}

.right-w {
  width: calc(50% - 17px);
}

.title-row {
  position: relative;
  width: fit-content;
  & > span {
    position: relative;
    z-index: 1;
  }
  &::after {
    display: inline-block;
    content: '';
    width: 100%;
    height: 5px;
    right: 0;
    bottom: 2px;
    position: absolute;
    background: linear-gradient(
      90deg,
      rgba(121, 138, 254, 0.2) 0%,
      #6474FD 100%
    );
    border-radius: 3px;
    z-index: 0;
  }
}

.right-container-h {
  height: calc(100% - 24px - 13px);
}

.ques-btn {
  @apply rounded-[9px] bg-[#6474FD] w-102px h-43px text-center leading-[43px] text-15px text-[#fff] font-600 select-none;
  box-shadow: 0 0 16px 16px rgba(121, 138, 254, 0.2);
}
</style>

<style lang="scss">
.correction-page {
  .ques-num,
  .text-option {
    flex-shrink: 0;
    margin-right: 4px;
  }
  .ques-row,
  .ques-text-row {
    display: flex;
    align-items: flex-start;
  }
  .mathjax-p {
    font-size: 17px !important;
    p {
      white-space: break-spaces;
    }
  }
}
</style>
