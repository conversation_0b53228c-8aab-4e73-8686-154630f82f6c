<script setup lang="ts">
import { getTaskPattern } from '@/api/comprehensiveTask'
import {
  fetchZuJuanInfoApi,
  getQuestionListApi,
  getXkwPaperDataId,
  importBookList,
} from '@/api/taskCenter'
import { useUserStore } from '@/stores/modules/user'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import QuestionItem from './QuestionItem.vue'
import SortDialog from './SortDialog.vue'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const route = useRoute()
const router = useRouter()
// 选择的题目列表数据
const questionList = inject<Ref<any[]>>('questionList', ref([]))
const getStepVal = inject('getStepVal') as any
const setStepVal = inject('setStepVal') as any
const userStore = useUserStore()
const taskModel = inject<Ref<any>>('taskModel', ref(null))

const scrollerRef = $ref<any>()
let taskType = $ref<any>('')
let taskTypeList = $ref<Array<{
  label: string
  value: string | number
}>>([])// 测试类型

let fetchQuestionListLoading = $ref(false)
// 控制排序弹窗显隐
let showSortDialog = $ref(false)
const sortDialogRef = $ref<any>()
// 控制菜单显示
let showPopover = $ref(false)

// 如果当前步骤不是创建任务，则关闭排序弹窗
watch(
  getStepVal,
  (stepVal, oldValue) => {
    // 从其他页面回到任务内容，模拟加载动画
    if (stepVal === 1 && oldValue !== 1) {
      fetchQuestionListLoading = true
      setTimeout(() => {
        fetchQuestionListLoading = false
        nextTick($g.tool.renderMathjax)
      }, 1000)
    }
    // 去其他页面关闭排序弹窗
    if (stepVal !== 1)
      showSortDialog = false
  },
  { immediate: true },
)

const ZXXKObj: any = {
  handler: null,
  isListen: false,
  qmdrKey: null,
}

/** 去选题 */
async function handleChooseQuestion(type) {
  if (type === 'XKW') {
    const res = await fetchZuJuanInfoApi()
    ZXXKObj.qmdrKey = res?.qmdrKey
    ZXXKObj.handler = window.open(
      `https://zujuan.qimingdaren.com/#/third/teacherURedirect?jztToken=${userStore.jztToken}`,
    )
  }

  if (type === 'XBLX')
    setStepVal(2)

  showPopover = false
}

function scrollIntoView(questionId) {
  const index = questionList.value.findIndex(item => item.questionId === questionId)
  scrollerRef.scrollToItem(index)
  setTimeout(async () => {
    await $g.tool.renderMathjax()
    nextTick(() => scrollerRef.scrollToItem(index))
  }, 100)
}

/** 移除题目 */
function handleRemoveQuestion(index) {
  questionList.value.splice(index, 1)
  // 移除题目后更新排序弹窗中的数据
  showSortDialog && sortDialogRef?.handleSwitchChange?.()
}

/**
  获取学科网的题目列表
 */
async function loadXKWQuestion(paperid, openid) {
  console.log('🚀 ~ loadXKWQuestion ~ paperid, openid:', paperid, openid)

  try {
    fetchQuestionListLoading = true

    // 1、通过paperId获取xkwPaperDataId
    const PaperData = await getXkwPaperDataId(
      {
        paperId: paperid,
        openId: openid,
      },
      {
        headers: {
          Authorization: ZXXKObj.qmdrKey,
        },
      },
    )

    // 2、通过xkwPaperDataId获取bookId
    const data = await importBookList(
      { xkwPaperDataId: PaperData?.xkwPaperDataId },
      {
        headers: {
          Authorization: ZXXKObj.qmdrKey,
        },
      },
    )
    if (!data)
      throw new Error('获取bookId失败')

    // 3、通过bookId获取题目列表
    const res: any = await getQuestionListApi({
      bookId: data?.bookId,
      page: 1,
      pageSize: 999,
    })

    console.log('🚀 ~ 导入题目 ~ res.list:', res.list)

    let list = res.list.map((question) => {
      return {
        ...question,
        chooseSource: 1,
      }
    })

    // 遍历list，去除重复题目
    list.forEach((question) => {
      if (
        !questionList.value.some(q => q.questionId === question.questionId)
      )
        questionList.value.push(question)
    })
  }
  catch (err) {
    console.log(err)
  }
  finally {
    fetchQuestionListLoading = false
    nextTick($g.tool.renderMathjax)
  }
}

/** 监听学科网发送的事件 */
function onPostMessage(event) {
  console.log('onPostMessage', event)
  const {
    data: {
      paperid,
      openid,
    },
  } = event
  if (paperid && openid) {
    ZXXKObj.handler &&
    ZXXKObj.handler.postMessage('close', 'https://zujuan.qimingdaren.com')
    $g.isFlutter && $g.flutter('closeXueKeWang')
    loadXKWQuestion(paperid, openid)
  }
}

/** 题号获取index的方法， 虚拟列表的index有几率出现混乱 */
function getIndex(questionId) {
  return questionList.value.findIndex(item => item.questionId === questionId)
}
async function getTaskPatternApi() {
  const pageType = route.query?.pageType
  if (!pageType) return

  const res = await getTaskPattern({ taskType: pageType })
  taskTypeList = res.map(it => ({
    label: it?.title,
    value: it?.id?.toString(),
  }))
  // if (route?.query?.pattenType) {
  //   taskTypeList = taskTypeList.filter(it => it.value != 1)
  // }
  taskType = route.query?.pattenType || taskTypeList?.[0]?.value || ''
}
watch(() => taskType, (newVal) => {
  if (newVal)
    taskModel.value = newVal
})
watch(() => taskModel.value, (newVal) => {
  taskType = newVal?.toString() ?? ''
}, { immediate: true })

onBeforeUnmount(() => {
  if (ZXXKObj.isListen) {
    window.removeEventListener('message', onPostMessage)
    ZXXKObj.isListen = false
  }
})
onMounted(() => {
  if (!ZXXKObj.isListen) {
    window.addEventListener('message', onPostMessage)
    ZXXKObj.isListen = true
  }
  getTaskPatternApi()
})
</script>

<template>
  <div>
    <div class="mb-17px" :class="{ '!mb-10px': !$g.isPC }">
      <span class="text-17px leading-[24px] font-600">2.测试设置</span>
    </div>
    <div class="p-17px bg-[#fff] rounded-[6px] mb-[21px]" :class="{ '!py-[8px] !mb-10px': !$g.isPC }">
      <div class="flex items-center ">
        <div class="mr-[16px] text-[15px] text-[#333]">
          测试模式
        </div>
        <el-radio-group v-model="taskType" :disabled="Boolean(route.query?.pattenType)">
          <el-radio
            v-for="item in taskTypeList"
            :key="item.value"
            class="!text-[#333] !text-[15px]"
            :value="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>

  <div class="flex-1 overflow-hidden pb-10px flex flex-col">
    <div class="flex items-center justify-between mb-12px" :class="{ '!mb-10px': !$g.isPC }">
      <span class="text-17px leading-[24px] font-600"> 3.任务内容</span>
      <div>
        <el-button
          v-if="questionList.length"
          text
          class="text-13px h-30px px-5px text-[#6c6c74]"
          @click="showSortDialog = true"
        >
          <img src="@/assets/img/taskCenter/sort.png" class="w-17px mr-4px" />
          <span>排序</span>
        </el-button>
        <el-button
          v-if="questionList.length"
          text
          class="text-13px h-30px px-5px"
          @click="questionList = []"
        >
          <img src="@/assets/img/taskCenter/bin3.png" class="w-17px mr-4px" />
          <span>清空</span>
        </el-button>

        <el-popover
          v-model:visible="showPopover"
          placement="bottom-end"
          trigger="click"
          width="auto"
          :show-arrow="false"
          :teleported="false"
          popper-style="min-width: unset;text-align: center;"
        >
          <template #reference>
            <el-button type="primary" class="text-15px h-30px br-[4px] px-20px">
              <img src="@/assets/img/common/plus.png" class="w-11px mr-4px" />
              <span>添加题目</span>
            </el-button>
          </template>

          <template #default>
            <div
              class="text-15px text-[#333] leading-[21px] mb-17px cursor-pointer hover:font-600"
              @click="handleChooseQuestion('XKW')"
            >
              去学科网选题
            </div>
            <div
              class="text-15px text-[#333] leading-[21px] cursor-pointer hover:font-600"
              @click="() => handleChooseQuestion('XBLX')"
            >
              校本练习选题
            </div>
          </template>
        </el-popover>
      </div>
    </div>

    <!-- 加载中 -->
    <g-loading v-if="fetchQuestionListLoading" class="h-200px m-auto" />
    <!-- 题目为空时显示 -->
    <template v-if="!questionList.length && !fetchQuestionListLoading">
      <div class="bg-white min-h-400px flex flex-col items-center">
        <div class="font-600 text-17px leading-[21px] mt-60px">
          添加题目
        </div>
        <div class="text-[#929296FF] leading-[18px] mb-13px mt-13px text-15px">
          暂无题目,可通过以下方式生成作业题目
        </div>
        <el-button
          type="primary"
          plain
          class="h-30px bg-[#fbfbfb] text-[#5864F8] border-[#646AB4] text-15px font-600"
          @click="
            handleChooseQuestion(route.query.pageType == '1' ? 'XKW' : 'XBLX')
          "
        >
          {{ route.query.pageType == '1' ? '去学科网选题' : '校本练习选题' }}
        </el-button>
      </div>
    </template>

    <template
      v-if="
        questionList.length && !fetchQuestionListLoading && getStepVal() === 1
      "
    >
      <DynamicScroller
        ref="scrollerRef"
        :items="questionList"
        :min-item-size="300"
        :buffer="500"
        key-field="questionId"
        class="h-full"
      >
        <template #default="{ item, index, active }">
          <DynamicScrollerItem
            :item="item"
            :active="active"
            :size-dependencies="['questionTitle', 'subQuestions', 'showParse']"
            class="pb-10px"
            :data-index="index"
          >
            <div class="border border-solid border-[#DADDE8] rounded-[6px] bg-white  relative">
              <QuestionItem
                :question-index="getIndex(item.questionId)"
                :question-item="item"
                render-if-change
              >
                <template #extraData>
                  <div
                    class="text-13px leading-[13px] py-7px text-center w-[66px] border border-[#6474fd24] bg-[#f3f4f9] text-[#6474FD] rounded-[4px] ml-12px"
                  >
                    {{
                      item.chooseSource === 1
                        ? '学科网'
                        : '校本练习'
                    }}
                  </div>
                </template>

                <template #footerAction>
                  <el-button
                    class="border-[#FF4646] text-[#FF4646]"
                    @click="handleRemoveQuestion(index)"
                  >
                    移除
                  </el-button>
                </template>
              </QuestionItem>
            </div>
          </DynamicScrollerItem>
        </template>
      </DynamicScroller>

      <!-- 排序弹窗 -->
      <SortDialog
        ref="sortDialogRef"
        v-model:show="showSortDialog"
        @scroll-into-view="scrollIntoView"
      />
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
