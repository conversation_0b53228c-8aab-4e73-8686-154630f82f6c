<script setup lang="ts">
const props = defineProps({
  btnLoading: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['submit'])
let dialogVisible = defineModel<boolean>('visible')
const typeList = [
  {
    label: '只发布变式题',
    value: 1,
  },
  {
    label: '发布原题及其变式题',
    value: 2,
  },
]
let currentType = $ref<any>(1)
function close() {
  dialogVisible.value = false
  currentType = 1
}
async function submit() {
  if (props.btnLoading) return
  await emit('submit', currentType)
  close()
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="395"
    class="!rounded-[6px]"
    :lock-scroll="false"
  >
    <template #header>
      <div class="flex items-center justify-center">
        <div class="text-[17px] text-[#333]">
          请选择试题范围
        </div>
      </div>
    </template>
    <div class="py-76px flex-cc">
      <div
        v-for="item in typeList"
        :key="item.value"
        class="flex items-center select-none"
        :class="{ 'mr-26px': item.value == 1 }"
      >
        <svg-common-select v-if="currentType != item.value"
                           class="w-15px h-15px cursor-pointer"
                           @click="currentType = item.value"
        />
        <svg-common-selected v-else class="w-15px h-15px cursor-pointer" />
        <div class="ml-5px">
          {{ item.label }}
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex-cc">
        <div class="w-[75px] leading-[30px] mr-[64px] text-center bg-[rgba(153,153,153,0.1)] rounded-[4px] cursor-pointer select-none text-[#666666] text-[15px]" @click="close">
          取消
        </div>
        <div class="w-[75px] leading-[30px] text-center bg-[#6474FD] rounded-[4px] cursor-pointer select-none text-[#fff] text-[15px]" @click="submit">
          确定
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
