<script setup lang="ts">
const props = defineProps({
  sourceList: {
    type: Array as PropType<any>,
    default: () => [],
  },
  typeList: {
    type: Array as PropType<any>,
    default: () => [],
  },
  gradeList: {
    type: Array as PropType<any>,
    default: () => [],
  },
})
let showDialog = defineModel<boolean>('show')
let checkedCondition = $ref<any>([])
let confirmCondition = defineModel<any>('condition')
/* 选择条件 */
function itemClick(type: string, item: any) {
  let data = checkedCondition?.find(v => v.type === type)
  if (data) {
    let index = data.list.findIndex((v: any) => v.id === item.id)
    if (index != -1)
      data.list.splice(index, 1)

    else
      data.list.push(item)

    checkedCondition.find(v => v.type === type).list = data.list
  }
  else {
    checkedCondition.push({
      type,
      name: type === 'source' ? '来源' : type === 'type' ? '类型' : '年级',
      list: [item],
    })
  }
}
function open() {
  checkedCondition = $g._.cloneDeep(confirmCondition.value)
}
function confirm() {
  confirmCondition.value = checkedCondition.filter(v => v.list.length)
  showDialog.value = false
}
function reset() {
  checkedCondition = []
  confirmCondition.value = []
}
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    position="right"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="w-549px h-screen  flex flex-col pt-16px pb-10px px-21px overflow-hidden bg-[#FFFFFF] "
    teleport="#app"
    v-bind="$attrs"
    @open="open"
  >
    <div class="relative h-full">
      <div class="flex items-center justify-between font-600 text-17px">
        <span>筛选</span>
        <img class="cursor-pointer w-15px"
             src="@/assets/img/taskCenter/close.png"
             @click="showDialog = false"
        />
      </div>
      <div class="mt-20px ">
        <div class="text-14px flex items-center">
          <div class="font-600 ">
            来源
          </div>
          <div class="flex ml-11px">
            <div
              v-for="item in sourceList"
              :key="item.id"
              class=" px-11px py-6px mr-10px cursor-pointer"
              :class="checkedCondition?.find(v => v.type === 'source')?.list?.find(v => v.id === item.id) ? 'bg-[#ECEFFF] rounded-[5px] text-[#6474FD]' : 'text-[#6C6C74]'"
              @click="itemClick('source', item)"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
        <div class="text-14px flex items-center my-15px">
          <div class="font-600 flex-shrink-0">
            年级
          </div>
          <div class="flex ml-11px flex-wrap">
            <div
              v-for="item in gradeList"
              :key="item.sysGradeId"
              class=" px-11px py-6px mr-10px cursor-pointer"
              :class="checkedCondition?.find(v => v.type === 'grade')?.list?.find(v => v.id === item.id) ? 'bg-[#ECEFFF] rounded-[5px] text-[#6474FD]' : 'text-[#6C6C74]'"
              @click="itemClick('grade', item)"
            >
              {{ item.sysGradeName }}
            </div>
          </div>
        </div>
        <div class="text-14px flex items-center">
          <div class="font-600 ">
            类型
          </div>
          <div class="flex ml-11px">
            <div
              v-for="item in typeList"
              :key="item.id"
              class="px-11px py-6px mr-10px cursor-pointer"
              :class="checkedCondition?.find(v => v.type === 'type')?.list?.find(v => v.id === item.id) ? 'bg-[#ECEFFF] rounded-[5px] text-[#6474FD]' : 'text-[#6C6C74]'"
              @click="itemClick('type', item)"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
      <div class="absolute bottom-[10px]   flex items-center">
        <div class="w-160px leading-[30px] text-[#fff] text-center bg-[#6474FD] rounded-[4px] van-haptics-feedback" @click="confirm">
          确定
        </div>
        <div class="w-160px leading-[30px] text-[#5864F8] text-center mx-12px border-[1px] border-solid border-[#6474FD] rounded-[4px] van-haptics-feedback" @click="reset">
          重置
        </div>
        <div class="w-160px leading-[30px] text-[#666666] text-center border-[1px] border-solid border-[#6474FD] rounded-[4px] van-haptics-feedback" @click="showDialog = false">
          取消
        </div>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>

</style>
