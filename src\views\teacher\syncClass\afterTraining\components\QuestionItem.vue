<script setup lang="ts">
const props = defineProps({
  questionItem: {
    required: true,
    type: Object,
  },
  index: {
    type: Number,
  },
})
const cardItem: any = $computed(() => {
  return {
    ...(props.questionItem ?? {}),
    subQuestionList: props.questionItem.subQuestions?.map((h) => {
      return {
        ...h,
        optionArr: Object.keys(h)
          .filter(
            key => key.includes('option') && h[key] && key !== 'optionNumbers',
          )
          .map((realKey) => {
            return {
              name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
              title: h[realKey].toString(),
            }
          }),
      }
    }),
  }
})
let expand = $ref(false)
let hasExpand = $ref(false)
let maxHeight = $ref(321)
let observer: any = null
const contentRef = $ref<any>()

async function init() {
  observer?.disconnect?.()
  observer = null
  await nextTick()
  $g.tool.renderMathjax()
  observer = new ResizeObserver(() => {
    hasExpand = contentRef?.scrollHeight > maxHeight
    if (hasExpand) {
      observer?.disconnect?.()
      observer = null
    }
  })
  observer.observe(contentRef)
}
</script>

<template>
  <div class="bg-white br-[9px] mb-17px px-17px overflow-hidden">
    <div ref="contentRef" class="overflow-hidden pt-17px">
      <!-- 大题题干 -->
      <g-mathjax
        v-if="cardItem.questionTitle"
        :order="`${questionItem.questionIndex ?? index}.`"
        :text="cardItem.questionTitle"
        class="flex-shrink-0 mb-12px text-16px"
        :font-width="16"
      />
      <div
        v-for="(sub, subI) in cardItem.subQuestionList"
        :key="sub.subQuestionId"
      >
        <!-- 子题题干，如果大题没有题干时，需要充当大题题干,多个子题时，如果子题没有题干又要显示序号，则传入一个空的html元素 -->
        <div class="flex items-center mb-12px" :class="cardItem.subQuestionList.length > 1 && 'my-12px'">
          <g-mathjax
            class="text-15px"
            :order="!cardItem.questionTitle ? `${questionItem.questionIndex ?? index}.`
              : cardItem.subQuestionList.length > 1 ? `(${subI + 1})`
                : undefined"
            :font-width="15"
            :text="sub.subQuestionTitle || (cardItem.subQuestionList.length > 1 && sub.optionArr.length > 1 ? `<span></span>` : '')"
            :class="cardItem.questionTitle && 'ml-16px'"
          />
        </div>

        <div
          v-for="(item, subIndex) in sub.optionArr"
          :key="item.name"
          class="flex items-center  text-15px"
          :class="[subIndex !== sub.optionArr.length - 1 && 'mb-12px',
                   questionItem.questionTitle && sub.subQuestionTitle ? 'pl-32px' : 'pl-16px']"
        >
          <div class="flex-shrink-0">
            {{ item.name }}.
          </div>
          <g-mathjax :text="item.title" class="text-15px"></g-mathjax>
        </div>
      </div>
    </div>
    <div v-if="hasExpand"
         class="text-[#6474FD] text-15px flex items-center mt-11px cursor-pointer pl-16px"
         @click="expand = !expand"
    >
      <span class="mr-6px">{{ expand ? '收起' : '展开查看更多' }}</span>
      <img :src="expand ? $g.tool.getFileUrl('taskCenter/blueTop.png') : $g.tool.getFileUrl('taskCenter/blueBottom.png')"
           alt=""
           class="w-15px h-9px"
      >
    </div>

    <div class="myDashed mt-17px"></div>
    <slot name="footer" :question-item="questionItem"></slot>
  </div>
</template>

<style scoped lang="scss">
.myDashed{
  height: 1px;
  background: linear-gradient(
    to left,
    transparent 0%,
    transparent 50%,
    #ccc 50%,
    #ccc 100%
);
background-size: 10px 1px;
background-repeat: repeat-x;

}

:deep(){
  .g-mathjax {
    & > :first-child::before {
      color: #999999;
      }
  }
}
</style>
