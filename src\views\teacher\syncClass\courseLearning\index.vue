<script setup lang="ts" name="CourseLearning">
import type { FormInstance, FormRules } from 'element-plus'
import { getTaskResourceTeacherUploadList } from '@/api/aiTask'
import { uploadResource } from '@/api/comprehensiveTask'
import {
  getCheckedList,
  getCourseLearningList,
  getKnowledgeList,
  getKnowledgeResource,
  getVideoResource,
  saveCheckedItems,
  uploadVideo,
} from '@/api/syncClass'
import knowledgeCard from '@/assets/img/syncClass/knowledge-card.png'
import videoCard from '@/assets/img/syncClass/video-card.png'
import { Upload, UploadFilled } from '@element-plus/icons-vue'
import Preview from './components/Preview.vue'

const route: any = useRoute()
const router = useRouter()

let dataList = $ref<any>([])
let currentFile = $ref<any>(null)
let showFile = $ref(false)
let showDialog = $ref(false)
let loading = $ref(false)
let disableBtn = $ref(true)
let showTitle = $ref(false)
let uploadRef = $ref<any>([])
let articleInfo = $ref<any>({})
let previewType = $ref<any>(3)
let saveLoading = $ref(false)
let initCheckedIds = $ref<any>([]) // 选中的ids

const formRef = $ref<FormInstance>()

interface FormData {
  name: string
  fileList: any[]
}
// 表单数据
const formData = reactive<FormData>({
  name: '',
  fileList: [],
})

// 验证规则
const rules = reactive<FormRules<FormData>>({
  fileList: [
    {
      type: 'array',
      required: true,
      message: '请选择文件',
      trigger: 'change',
    },
  ],
  name: [{
    required: true,
    message: '请输入名称',
    trigger: 'blur',
  }],
})

function handleUpload(file, fileList) {
  formRef?.validateField('fileList') // 手动触发校验
}

fetchList()

/* 获取列表 */
async function fetchList(uploadId?) {
  try {
    const URL =
    route.query.type == 3 ? getCourseLearningList : getKnowledgeList
    let res = await URL({
      bookCatalogId: route.query.bookCatalogId,
    })
    let arr = res
    dataList = arr.map(v => ({
      ...v,
      isChecked: false,
    }))
    dataList.forEach((v) => {
      if ($g.tool.isTrue(v.images)) {
        v.images = JSON.parse(v.images)
        v.fileCoverUrl = JSON.parse(v.images[0]).url
      }
    })
    if (dataList.length && route.query?.from != 'aiTask')
      getCheckedListApi(uploadId)

    if (route.query?.from == 'aiTask') {
      await fetchTeacherUploadData()
      handleAiTaskCheck()
    }
  }
  catch (err) {
    dataList = []
  }
}

/**
 * AiTask选中id手动处理
 */
function handleAiTaskCheck() {
  let rawIds = route.query?.initCheckedIds
  initCheckedIds = Array.isArray(rawIds)
    ? rawIds.map(Number)
    : rawIds
      ? [Number(rawIds)]
      : []
  const result = dataList.map(item => ({
    ...item,
    isChecked: route.query.type == 3 ? initCheckedIds.includes(item.videoResourceId) : initCheckedIds.includes(item.bookCatalogArticleId),
  }))
  dataList = result
}

/* 获取选中项 */
function getCheckedListApi(uploadId?) {
  getCheckedList({
    bookCatalogId: route.query.bookCatalogId,
    bookCatalogResourceType: route.query.type == 3 ? 3 : 2,
    schoolClassId: route.query.schoolClassId,
  }).then((res) => {
    let result = dataList.map(item => ({
      ...item,
      isChecked: route.query.type == 3 ? res.includes(item.videoResourceId) : res.includes(item.bookCatalogArticleId),
    }))
    if ($g.tool.isTrue(uploadId)) {
      result.forEach((item) => {
        if (item.videoResourceId == uploadId)
          item.isChecked = true
      })
    }
    dataList = result
  })
}

/**
 * from=aiTask
 * 查询指定文件夹下面的资源列表
 */
async function fetchTeacherUploadData() {
  if (route.query?.from != 'aiTask') return
  const { list } = await getTaskResourceTeacherUploadList({ taskResourceTeacherDirId: 0 })
  dataList = dataList.concat(list)
}

/* 预览 */
async function handlePreview(row) {
  try {
    if (route.query.type == 3) {
      let res = await getVideoResource({
        videoResourceId: row.videoResourceId,
      })
      let obj = {
        fileAbsoluteUrl: res,
        fileAbsoluteUrlM3u8: res,
        fileExtension: res.split('.').pop(),
        fileName: row.fileName,
        videoCoverUrl: `${res}?x-oss-process=video/snapshot,t_0,f_jpg,m_fast`,
      }
      previewType = 3
      currentFile = obj
      showTitle = true
      showFile = true
    }
    else {
      let res = await getKnowledgeResource({
        bookCatalogArticleId: row.bookCatalogArticleId,
      })
      articleInfo = res
      if (res?.bookCatalogArticleFormatType == 3)
        articleInfo.content = JSON.parse(articleInfo.content)

      previewType = 2
      showTitle = false
      showFile = true
    }
  }
  catch (err) {
  }
}

/**
 * 选择图片或视频
 */
function onChoose(item) {
  item.isChecked = !item.isChecked
}

/**
 * 窗口关闭以后触发
 */
function handleClosed() {
  formRef?.resetFields()
  disableBtn = true
  formData.fileList = []
}

/**
 * 上传视频
 */
async function submit() {
  await formRef?.validate()
  loading = true
  let params = {
    bookId: route.query.bookId,
    bookCatalogId: route.query.bookCatalogId,
    video: {
      fileName: formData.name,
      fileAbsoluteUrl: formData.fileList[0].resource_url,
      fileSize: formData.fileList[0].size,
      fileExtension: formData.fileList[0].resource_url.split('.').pop(),
      fileDuration: formData.fileList[0].duration,
      fileCoverUrl: `${formData.fileList[0].resource_url}?x-oss-process=video/snapshot,t_0,f_jpg,m_fast`,
      showType: 2,
    },
  }
  const URL = route.query?.from == 'aiTask'
    ? uploadResource({
        taskResourceTeacherDirId: 0,
        taskResourceFileList: [{
          ...params.video,
          showType: undefined,
          resourceType: 1,
          sysGradeId: route.query?.sysCourseId,
        }],
      })
    : uploadVideo(params)
  await URL.then((res) => {
    $g.showToast('上传成功')
    showDialog = false
    fetchList(res)
    fetchTeacherUploadData()
  }).catch((err) => {
    console.log('表单验证或提交出错', err)
  }).finally(() => {
      loading = false
    })
}

/* 删除视频 */
function deleteFile(item, index) {
  if (item.percentage != 100)
    uploadRef.handleRemove(item)

  formData.fileList.splice(index, 1)
  formData.name = ''
  disableBtn = true
}

/* 保存勾选项 */
function onSave() {
  let checkedIds = dataList.filter(item => item.isChecked).map(v => route.query.type == 3 ? v.videoResourceId : v.bookCatalogArticleId)
  if (!checkedIds.length) {
    $g.showToast('请勾选保存项')
    return
  }
  if (route.query?.from == 'aiTask') {
    // 取出被取消勾选的id
    const cancelIds = initCheckedIds.filter(id => !checkedIds.includes(id))
    $g.bus.emit('saveCheckedRes', {
      checkedIds,
      cancelIds,
    })
    router.back()
    return
  }
  saveLoading = true
  saveCheckedItems({
    bookCatalogId: route.query.bookCatalogId,
    bookCatalogResourceType: route.query.type == 3 ? 3 : 2,
    schoolClassId: route.query.schoolClassId,
    bindIdList: checkedIds,
  }).then((res) => {
    $g.showToast('保存成功')
    router.back()
    setTimeout(() => {
      saveLoading = false
    }, 200)
  }).catch((err) => {
      router.back()
      setTimeout(() => {
        saveLoading = false
      }, 200)
      console.log('保存失败', err)
    })
}

function getFilenameWithoutExtension(filename) {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex === -1 ? filename : filename.substring(0, lastDotIndex)
}

watch(
  () => showFile,
  (val) => {
    if (!val) {
      setTimeout(() => {
        currentFile = {}
        articleInfo = {}
      }, 350)
    }
  },
)

watch(
  () => formData.fileList,
  (val) => {
    if (val.length) {
      console.log('⚡[ 111 ] >', 111)
      formData.name = getFilenameWithoutExtension(val[0].name)
      if (val[0].percentage == 100)
        disableBtn = false

      else
        disableBtn = true
    }
  },
  { deep: true },
)
</script>

<template>
  <div class="p-26px flex flex-col h-full">
    <div>
      <g-navbar title="退出">
        <template #right>
          <el-button
            type="primary"
            class="w-[102px] h-43px bg-[#6474FD] text-[#fff] font-600 text-15px rounded-[9px]"
            :loading="saveLoading"
            @click="onSave"
          >
            保存
          </el-button>
        </template>
      </g-navbar>
      <div class="flex justify-between items-center mt-13px mb-25px">
        <span class="text-17px font-600">{{ route.query.title || '您正在给1.1.1集合的概念及其表示方法' }}</span>
        <el-button
          v-if="route.query.type == 3"
          class="w-[96px] h-30px bg-transparent text-[#6474FD] border border-[#6474FD]  text-15px rounded-[4px]"
          @click="showDialog = true"
        >
          <el-icon class="el-icon--left">
            <Upload />
          </el-icon> 本地上传
        </el-button>
      </div>
    </div>
    <!-- 列表 -->
    <div class="flex-1 overflow-y-auto  no-bar">
      <div v-if="dataList.length" class="w-full  grid grid-cols-[repeat(auto-fill,minmax(181px,1fr))] gap-[16px] content-start">
        <div
          v-for="(item, index) in dataList"
          :key="index"
        >
          <div
            :style="{
              background: `url('${
                item?.fileCoverUrl || (route.query.type == 3 ? videoCard : knowledgeCard)
              }')  `,
              backgroundSize: '100% auto',
            }"
            class="w-full h-135px  flex flex-col rounded-[6px] justify-between cursor-pointer"
            @click="handlePreview(item)"
          >
            <div class="flex justify-end mt-9px mr-9px flex-shrink-0">
              <svg-common-checked v-if="item.isChecked"
                                  class="w-17px h-17px"
                                  @click.stop="onChoose(item)"
              />
              <svg-common-circle v-else
                                 class="w-17px h-17px"
                                 @click.stop="onChoose(item)"
              />
            </div>
            <div class="flex flex-col justify-between h-full">
              <div v-if="route.query.type == 3" class="flex justify-center">
                <img
                  :src="$g.tool.getFileUrl('common/video.png')"
                  alt=""
                  srcset=""
                  class="w-27px h-27px mt-29px  flex-shrink-0"
                >
              </div>
              <div v-else class="w-27px h-[27px] mt-29px flex-shrink-0 "></div>
              <div
                v-if="$g.tool.isTrue(item?.fileName || item?.title)"
                class="text-15px px-10px h-[44px] leading-[44px] rounded-b-[6px]"
                style="background:  linear-gradient( 180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.74) 100%);"
              >
                <el-text line-clamp="1" class="text-[#fff]">
                  <g-mathjax :text="item?.fileName || item?.title" class="text-15px"></g-mathjax>
                </el-text>
              </div>
            </div>
          </div>
        </div>
      </div>
      <g-empty v-else></g-empty>
    </div>
    <!-- 上传课程 -->
    <van-popup
      v-model:show="showDialog"
      round
      close-on-popstate
      class="custom-van-popup w-[704px] "
      teleport="#app"
      overlay-class="bg-[rgba(0,0,0,0.3)] "
      @closed="handleClosed"
    >
      <div class="text-center text-18px font-600">
        上传视频
      </div>
      <div class="flex-1 overflow-y-auto overflow-x-hidden px-27px pt-23px">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="auto"
        >
          <el-form-item label="文件" prop="fileList">
            <div
              v-for="(item, index) in formData.fileList"
              :key="item.uid"
              class="bg-[#F3F4F9] rounded-[6px] mr-15px mb-20px"
            >
              <div class="flex items-center w-[437px] h-[59px] box-border px-17px">
                <img
                  :src="$g.tool.getFileUrl('taskCenter/video.png')"
                  alt=""
                  class="w-21px h-21px flex-shrink-0"
                />
                <div class="ml-10px text-[15px] font-600 flex-1 truncate">
                  {{ item.name }}
                </div>
                <div class="flex flex-shrink-0">
                  <div
                    v-if="item.percentage == 100"
                    class="text-[#FF4646] cursor-pointer w-66px h-30px leading-[30px] border-[1px] border-solid border-[#FF4646] text-center rounded-[4px] ml-13px"
                    @click="deleteFile(item, index)"
                  >
                    删除
                  </div>
                </div>
              </div>
              <!-- 进度条 -->
              <el-progress
                v-if="item.percentage != 100"
                :percentage="item.percentage"
                class="mx-17px"
              />
            </div>
            <g-upload
              ref="uploadRef"
              v-model:file-list="formData.fileList"
              mode="text"
              class="text-center"
              :max-size="1024 * 1024 * 1024 * 4.88"
              accept=".mp4"
              :show-file-list="!formData.fileList.length"
              @change="handleUpload"
            >
              <div
                v-if="!formData.fileList.length"
                class="flex flex-col items-center justify-center rounded-[6px] text-[#6474FD] bg-[#fbfbfb] w-[300px] h-[100px] "
                style="border: 1px solid #646ab4"
              >
                <el-icon size="40">
                  <UploadFilled />
                </el-icon>
                <div>点击上传文件</div>
              </div>
              <div v-else>
              <!-- <svg-common-add-circle class="w-26px h-26px" /> -->
              </div>
              <template #tip>
                <div
                  v-if="!formData.fileList.length"
                  class="text-[#929296] text-[14px] mt-10px"
                >
                  只能上传.mp4文件，且不超过4.88GB
                </div>
                <div v-else></div>
              </template>
            </g-upload>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="formData.name"
              type="text"
              maxlength="100"
              class="w-300px"
              show-word-limit
              :disabled="disableBtn"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="w-full flex justify-end px-20px">
        <el-button
          class="w-[80px] h-38px br-[4px] text-15px  mr-10px"
          size="large"
          @click="showDialog = false"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          class="w-[80px] h-38px br-[4px] text-15px"
          size="large"
          :loading="loading"
          :disabled="disableBtn"
          @click="submit"
        >
          确定
        </el-button>
      </div>
    </van-popup>
    <!-- 预览文件 -->
    <Preview
      v-model:show="showFile"
      :current-file="currentFile"
      :show-close="false"
      :show-title="showTitle"
      :type="previewType"
      :article-info="articleInfo"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-van-popup {
  @apply flex flex-col overflow-hidden pt-18px pb-26px max-h-[90vh] br-[13px] bg-gradient-to-b from-[#ffe6d6] via-[70px] via-[#ffffff];
  :deep() {
    .van-icon-cross {
      color: #333333;
      font-size: 18px;
      font-weight: 600;
      top: 18px;
    }
  }
}
:deep() {
  .el-upload.el-upload--picture-card {
    --el-upload-picture-card-size: 120px;
  }
  .el-upload-list.el-upload-list--picture-card {
    --el-upload-list-picture-card-size: 120px;
  }
}
</style>
