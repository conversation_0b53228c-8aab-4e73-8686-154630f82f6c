import JSON_DATA from '@/json/index'
import { elementFunction } from '@/plugins/element'
import vant from '@/plugins/vant'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import * as _ from 'lodash-es'
import mitt from 'mitt'
import flutter from './flutter'
import MathBigNumber from './math'
import tool from './tool'

dayjs.locale('zh-cn')
dayjs.extend(duration)
const $g = {
  ...vant.vantFunction,
  ...elementFunction,
  flutter,
  tool,
  bus: mitt(),
  dayjs,
  // 判断是否flutter中
  isFlutter: !!window.flutter_inappwebview,
  // 判断是否在PC,否则就是移动端
  isPC: !('ontouchstart' in window) && !window.flutter_inappwebview,
  _,
  math: (num) => {
    return new MathBigNumber(num)
  },
  navigationHeight: 0, // 导航条高度
  JSON: JSON_DATA,
}

export default $g
