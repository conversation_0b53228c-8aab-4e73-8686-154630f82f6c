/* 使用方法  v-loadOnScroll:3000.once="initData"
 * 当元素出现在视口时，执行 initData 函数，可选参数 timeout，表示延迟执行时间,ms值，修饰符once，表示只执行一次绑定的方法以后不再执行
 */

export const loadOnScroll = {
  mounted(el, binding) {
    const options = {
      rootMargin: '0px',
      threshold: 0,
    }
    // 执行方法的延时
    const timeout = Number(binding.arg || 0)

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // 元素进入视口
          if (el._timer)
            clearTimeout(el._timer)

          el._timer = setTimeout(() => {
            // 触发绑定方法执行
            binding.value()
            // 如果只执行一次绑定的方法，则执行以后，停止监听
            if (binding.modifiers.once)
              observer.unobserve(el)

            delete el._timer
          }, timeout)
        }
        else {
          // 元素离开视口
          if (el._timer) {
            clearTimeout(el._timer)
            delete el._timer
          }
        }
      })
    }, options)

    observer.observe(el)
    // 保存 observer 对象到元素实例上
    el._observer = observer
  },
  beforeUnmount(el) {
    // 销毁定时器
    if (el._timer)
      clearTimeout(el._timer)

    // 停止观察目标元素
    if (el._observer) {
      el._observer.disconnect()
      delete el._observer
    }
  },
}
