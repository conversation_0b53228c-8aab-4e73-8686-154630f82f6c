<script lang="ts" setup>
import PaperIndex from './PaperIndex.vue'
import SchoolIndex from './SchoolIndex.vue'

const props = defineProps({
  stepType: {
    type: Number,
  },
})
let checkedValue = $ref<any>(null)
const list = ref([{
  id: 0,
  name: '校本教辅',
}, {
  id: 1,
  name: '试卷选题',
}])
const setStepVal = inject('setStepVal') as any
function goBack() {
  checkedValue = null
  setStepVal(1)
}
watch(() => props.stepType, (newValue, oldValue) => {
  if (newValue === 2 && oldValue == 1) {
    setTimeout(() => {
      checkedValue = 0
    }, 100)
  }
})
</script>

<template>
  <div>
    <g-navbar title="校本练习 选题" :on-back="goBack"></g-navbar>
    <div class="mb-[13px] mt-[35px]">
      <g-radio
        v-model="checkedValue"
        :option="list"
        item-class="px-17px br-[6px] mr-[17px] py-[9px] font-400 text-[15px] border border-[#DADDE8] bg-[white]"
        active-item-class="!border-[#646AB4] br-[6px] !text-[15px] !font-600 !bg-[#ECEFFF]"
      />
    </div>
    <div>
      <SchoolIndex v-if="checkedValue == 0" />
      <PaperIndex v-else-if="checkedValue == 1" />
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
