<script setup lang="ts" inheritAttrs="false">
import AliOss from '@/plugins/OSS'

defineProps({
  mode: {
    type: String,
    default: 'edit', // edit-读写 preview-只读
  },
  height: {
    type: String,
    default: '600px',
  },
  cursor: {
    type: Boolean,
    default: false,
  },
})

// 富文本内容
let text = defineModel({
  type: String,
  default: '',
})

let aliOss: any = new AliOss({})
// gpt文字块的信息
let gptText = $ref('')
// 上传图片加载状态
let loading = $ref(false)
// 插入GPT文字块的弹窗是否可见
let dialogVisible = $ref(false)
// 编辑器配置
const editorConfig = {
  leftToolbar:
    'emoji clear undo redo | h bold italic strikethrough quote | ul ol table tip hr | link image code',
  rightToolbar: 'preview sync-scroll fullscreen chatGpt',
  toolbar: {
    chatGpt: {
      icon: 'ri-robot-fill text-18px',
      title: '插入GPT文字块',
      action: (editor) => {
        dialogVisible = true
      },
    },
  },
}

// 插入GPT文字块到编辑器中
function handleInsert() {
  let res = replaceSpecialStrings(gptText)
  if (res)
    text.value += res

  gptText = ''
  dialogVisible = false
}

function onClose() {
  gptText = ''
}

// 替换GPT中复制出来的公式
function replaceSpecialStrings(mathJaxText) {
  // 匹配行内公式 \( ... \)
  let inlineMathRegex = /\\\(\s*(.+?)\s*\\\)/g
  // 匹配行间公式 \[ ... \]
  let blockMathRegex = /\\\[(.+?)\\\]/gs

  // 将行内公式包裹在 $...$ 中
  mathJaxText = mathJaxText.replace(inlineMathRegex, (match, p1) => {
    return `$${p1}$`
  })

  // 将行间公式包裹在 $$...$$ 中
  mathJaxText = mathJaxText.replace(blockMathRegex, (match, p1) => {
    return `$$${p1}$$`
  })

  return mathJaxText
}

// 上传图片处理
function handleUploadImage(event, insertImage, files) {
  // 检查图片格式
  for (const file of files) {
    // 校验文件类型
    let ext = file.name.split('.').pop()
    if ($g.tool.getFileType(ext) !== 'img') {
      $g.showToast(`${file.name}  文件类型错误，请选择图片文件！`)
      return
    }

    // 校验文件大小
    if (file.size > 20 * 1024 * 1024) {
      $g.showToast(
        `${file.name}  文件大小超过限制，请选择小于 20M 的文件！`,
      )
    }
  }

  loading = true
  let uploadList = files.map((file, index) =>
    aliOss.uploadFile(
      {
        name: file.name,
        size: file.size,
        file,
        id: index,
      },
      undefined,
    ))

  Promise.all(uploadList)
    .then((res) => {
      console.log(res)
      res.forEach((fileUrl, index) => {
        insertImage({
          url: fileUrl.fullUrl,
          desc: files[index].name,
          width: 'auto',
          height: 'auto',
        })
      })
    })
    .finally(() => {
      loading = false
    })
}
</script>

<template>
  <div class="mdEditor-container">
    <template v-if="mode === 'edit'">
      <div v-loading="loading" class="w-full h-600px relative loading-box">
        <v-md-editor
          v-model="text"
          :height="height"
          :left-toolbar="editorConfig.leftToolbar"
          :right-toolbar="editorConfig.rightToolbar"
          :toolbar="editorConfig.toolbar"
          :disabled-menus="[]"
          v-bind="$attrs"
          @upload-image="handleUploadImage"
        />
      </div>
      <!-- 插入GPT文字块弹窗 -->
      <g-dialog
        v-model:show="dialogVisible"
        title="插入GPT文字块"
        width="700"
        :auto-close="false"
        @confirm="handleInsert"
      >
        <n-input
          v-model:value="gptText"
          type="textarea"
          :rows="10"
          placeholder="请输入内容"
        />
      </g-dialog>
    </template>
    <!-- 普通预览 -->
    <v-md-preview
      v-else-if="mode === 'preview'"
      class="custom-md-preview"
      :text="text"
      :height="height"
      v-bind="$attrs"
    />
    <!-- 流式预览 -->
    <v-md-preview-stream
      v-else-if="mode === 'stream'"
      class="custom-md-preview !text-15px"
      :text="text"
      :show-cursor="cursor"
      :height="height"
      v-bind="$attrs"
    />
  </div>
</template>

<style lang="scss" scoped>
.loading-box {
  :deep() {
    .el-loading-mask {
      .el-loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

:deep() {
  .custom-md-preview .github-markdown-body {
    padding: 5px;
    font-size: 15px;
  }
  .github-markdown-body p {
    margin-bottom: 0;
  }
  .v-md-plugin-tip,
  tip {
    padding-bottom: 10px;
  }
  .katex-block {
    overflow-x: auto;
    overflow-y: hidden;
  }
}
</style>

<style>
.qm-chat-cursor {
  background-image: url(@/assets/img/aiTeacher/aiLoading.gif);
  background-size: cover;
  display: inline-block;
  height: 12px;
  margin-left: 4px;
  position: relative;
  top: 1px;
  width: 12px;
}
</style>
