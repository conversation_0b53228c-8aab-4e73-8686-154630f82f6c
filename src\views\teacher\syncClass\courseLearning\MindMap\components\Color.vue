<script>
import { colorList } from '../config/other'

/**
 * @Desc: 颜色选择器
 */
export default {
  name: 'Color',
  props: {
    color: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      colorList,
      selectColor: '',
    }
  },
  watch: {
    color() {
      this.selectColor = this.color
    },
  },
  created() {
    this.selectColor = this.color
  },
  methods: {
    /**
     * @Author: 黄原寅
     * @Desc: 点击预设颜色
     */
    clickColorItem(color) {
      this.$emit('change', color)
    },

    /**
     * @Author: 黄原寅
     * @Desc: 修改颜色
     */
    changeColor() {
      this.$emit('change', this.selectColor)
    },
  },
}
</script>

<template>
  <div class="colorContainer">
    <div class="colorList">
      <span
        v-for="item in colorList"
        :key="item"
        class="colorItem iconfont"
        :style="{ backgroundColor: item }"
        :class="{ icontouming: item === 'transparent' }"
        @click="clickColorItem(item)"
      ></span>
    </div>
    <div class="moreColor">
      <span>更多颜色</span>
      <el-color-picker
        v-model="selectColor"
        size="small"
        @change="changeColor"
      ></el-color-picker>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.colorList {
  width: 240px;
  display: flex;
  flex-wrap: wrap;
  .colorItem {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 15px;
    height: 15px;
    margin-right: 5px;
    margin-bottom: 5px;
    cursor: pointer;
  }
}

.moreColor {
  display: flex;
  align-items: center;

  span {
    margin-right: 5px;
  }
}
</style>
