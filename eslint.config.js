import process from 'node:process'

import antfu from '@antfu/eslint-config'

export default antfu({
  // 启用 TypeScript 支持
  typescript: true,

  // 添加 ignores 配置来排除 types 文件夹
  ignores: ['**/types/**'],

  // 全局变量配置应该在顶层
  globals: {
    $g: true, // 允许使用 $g 全局变量
  },

  // Vue 相关配置
  vue: {
    overrides: {
      // Vue 规则配置
      'vue/component-name-in-template-casing': ['error', 'PascalCase'],
      'vue/block-order': ['error',{
          order: ['script','template','style'],
        }],
      // 允许未使用的 ref
      'vue/no-unused-refs': 'off',
      // 允许在组件中修改 props
      'vue/no-mutating-props': 'off',
      'vue/eqeqeq': 'off',
      'vue/html-self-closing': 'off',
    },
  },

  // 通用规则配置
  rules: {
    // JavaScript/TypeScript 规则
    'quotes': ['error', 'single'],
    'semi': ['error', 'never'],
    'indent': ['off'],
    // 关闭普通未使用变量警告
    'no-unused-vars': ['off',{
        vars: 'all',
        args: 'after-used',
        ignoreRestSiblings: true,
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^(props)$',
      }],
    // 开启 TypeScript 未使用变量警告
    '@typescript-eslint/no-unused-vars': 'warn',
    'unused-imports/no-unused-imports': 'off',
    'eqeqeq': 'off',
    'arrow-parens': ['off', 'always'],
    'prefer-const': 'off',
    'no-console': 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'unused-imports/no-unused-vars': 'off',
    'no-empty': 'off',
    'no-undef': ['off'],

    // TypeScript 特定规则
    'ts/no-use-before-define': ['error',{
        functions: false,
        classes: false,
        variables: false,
        allowNamedExports: false,
      }],
    'ts/ban-ts-comment': 'off',

    'no-use-before-define': 'off',

    // eslint-plugin-vue 规则
    'regexp/no-super-linear-backtracking': 'off',

    // 关闭 Promise reject 必须使用 Error 对象的规则
    'prefer-promise-reject-errors': 'off',

    'array-callback-return': 'off',

    'no-prototype-builtins': 'off',

    // 添加以下规则以符合项目规范

    // Vue 相关规则
    'vue/multi-word-component-names': 'off', // 允许单个单词的组件名
    'vue/require-default-prop': 'off', // 不强制要求 props 默认值
    'vue/max-attributes-per-line': ['error',{
        singleline: 2, // 单行最多2个属性，更容易触发换行
        multiline: 1, // 多行每行1个属性
      }],

    // Vue模板格式化规则
    'vue/html-indent': ['error',2,{
        attribute: 1,
        baseIndent: 1,
        closeBracket: 0,
        alignAttributesVertically: true,
        ignores: [],
      }],
    'vue/html-closing-bracket-newline': ['error',{
        singleline: 'never',
        multiline: 'always',
      }],
    'vue/html-closing-bracket-spacing': ['error',{
        startTag: 'never',
        endTag: 'never',
        selfClosingTag: 'always',
      }],
    'vue/multiline-html-element-content-newline': ['error',{
        ignoreWhenEmpty: true,
        ignores: ['pre', 'textarea'],
      }],

    // TypeScript 相关规则
    'ts/consistent-type-definitions': ['error', 'interface'], // 优先使用 interface
    'ts/no-explicit-any': 'off', // 允许使用 any

    // 代码风格规则
    'curly': ['error','multi-or-nest','consistent'], // 允许单行语句不使用大括号
    'nonblock-statement-body-position': ['error', 'any'], // 智能选择单行或换行，优先考虑行长度
    'antfu/if-newline': 'off', // 关闭antfu的if换行规则，允许单行if
    'space-before-function-paren': ['error',{
        anonymous: 'always',
        named: 'never',
        asyncArrow: 'always',
      }],
    'style/max-statements-per-line': 'off',
    'jsdoc/check-param-names': 'off',

    // === ESLint负责所有格式化规则 ===
    // 行长度限制和自动换行
    // 导入语句格式化
    'style/object-curly-newline': ['error',{
        ImportDeclaration: {
          multiline: true,
          minProperties: 4,
          consistent: true,
        },
        ExportDeclaration: {
          multiline: true,
          minProperties: 4,
          consistent: true,
        },
        ObjectExpression: {
          multiline: true,
          minProperties: 2,
          consistent: true,
        },
        ObjectPattern: {
          multiline: true,
          minProperties: 2,
          consistent: true,
        },
      }],

    // 对象属性换行规则
    'style/object-property-newline': ['error',{
        allowAllPropertiesOnSameLine: false,
      }],

    // 数组元素换行
    'style/array-element-newline': ['error',{
        multiline: true,
        minItems: 3,
      }],

    // 函数参数换行
    'style/function-paren-newline': ['error', 'multiline-arguments'],
    'style/function-call-argument-newline': ['error', 'consistent'],

    // 链式调用换行
    'style/newline-per-chained-call': ['error',{
        ignoreChainWithDepth: 2,
      }],

    // 其他格式化规则
    'comma-dangle': ['error', 'always-multiline'],
    'style/comma-spacing': ['error',{
        before: false,
        after: true,
      }],
    'style/operator-linebreak': ['error','after',{
        overrides: {
          '?': 'before',
          ':': 'before',
          '=': 'after',
        },
      }],
  },
})
