import {
  closeNotify,
  closeToast,
  Dialog,
  Lazyload,
  Notify,
  showConfirmDialog,
  showDialog,
  showFailToast,
  showLoadingToast,
  showNotify,
  showSuccessToast,
  showToast,
  Toast,
} from 'vant'

import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'

/** Vant UI 组件类型定义 */
interface IVantComponents {
  Toast: typeof Toast
  Dialog: typeof Dialog
  Notify: typeof Notify
  Lazyload: typeof Lazyload
}

/** Vant UI 方法类型定义 */
interface IVantFunction {
  showToast: typeof showToast
  showLoadingToast: typeof showLoadingToast
  showSuccessToast: typeof showSuccessToast
  showFailToast: typeof showFailToast
  closeToast: typeof closeToast
  showDialog: typeof showDialog
  showConfirmDialog: typeof showConfirmDialog
  showNotify: typeof showNotify
  closeNotify: typeof closeNotify
}

/** Vant UI 插件配置 */
const vant = {
  vantComponents: {
    Toast,
    Dialog,
    Notify,
    Lazyload,
  } as IVantComponents,

  vantFunction: {
    showToast,
    showLoadingToast,
    showSuccessToast,
    showFailToast,
    closeToast,
    showDialog,
    showConfirmDialog,
    showNotify,
    closeNotify,
  } as IVantFunction,
}

export default vant
