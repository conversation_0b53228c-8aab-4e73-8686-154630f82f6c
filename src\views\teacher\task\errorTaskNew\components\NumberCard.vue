<script setup lang="ts">
const props = withDefaults(defineProps<{
  quesList: any[]
  range: any
  scrollTop: number
  itemClass: string
}>(), {
  quesList: () => [],
  range: () => ({
    rateBegin: 0,
    rateEnd: 100,
  }),
  itemClass: () => 'ques-item-class',
  scrollTop: () => 0,
})

const emit = defineEmits<{
  'sort': [args: any]
  'update:range': [args: any]
  'handleSetFilterRange'
  'scrollToTop'
}>()

let observerList: any = []

let isClick = false
let showPop = $ref(false)

const sideRef = $ref<any>()
let boxStyle = $ref<any>({})
let startY = 0
let startTop = 0
let isMove = false

let showSide = $ref(false)
let currentIdx = $ref(0)

const difficulty = [
  {
    value: 'difficult',
    label: '困难: 0%≤班级得分率<60%',
  },
  {
    value: 'medium',
    label: '较难: 60%≤班级得分率<70%',
  },
  {
    value: 'general',
    label: '一般: 70%≤班级得分率<85%',
  },
  {
    value: 'easy',
    label: '容易: 85%≤班级得分率≤100%',
  },
]
const colorMap = {
  difficult: '#FF4646',
  medium: '#6474FD',
  general: '#faad14',
  easy: '#52c41a',
}
const typeList = [
  {
    label: '按题号排序',
    value: 'index',
  },
  {
    label: '按正确率排序',
    value: 'classRate',
  },
]
let currentType = $ref('index')
const rangeText = $ref({
  left: 0,
  right: 0,
})

const quesListSorted = $computed(() => {
  return currentType === 'index' ? props.quesList : props.quesList.slice().sort((a, b) => a.classRate - b.classRate)
})

const shiftStatus = $g._.debounce(() => {
  isClick = false
}, 300)

function selectQues(index) {
  isClick = true
  currentIdx = index
  const domList = document.getElementsByClassName(props.itemClass)
  if (domList.length)
    domList[index]?.scrollIntoView()

  shiftStatus()
}

function onmousedown(e) {
  startY = e.clientY || e.touches[0].clientY
  startTop = sideRef?.offsetTop
  window.addEventListener('mousemove', onmousemove)
  window.addEventListener('touchmove', onmousemove)
  window.addEventListener('mouseup', mouseup)
  window.addEventListener('touchend', mouseup)
}

function onmousemove(e) {
  const clientY = e.clientY || e.touches[0].clientY
  const offsetY = startTop + (clientY - startY)
  if (Math.abs(clientY - startY) > 5)
    isMove = true

  const maxTop = window.innerHeight - sideRef?.clientHeight
  const top = offsetY < 0 ? 0 : offsetY > maxTop ? maxTop : offsetY
  boxStyle = {
    top: `${top}px`,
    bottom: 'auto',
  }
}

function mouseup() {
  window.removeEventListener('mousemove', onmousemove)
  window.removeEventListener('touchmove', onmousemove)
  window.removeEventListener('mouseup', mouseup)
  window.removeEventListener('touchend', mouseup)
  setTimeout(() => {
    isMove = false
  }, 0)
}

async function checkShow() {
  if (isMove) {
    isMove = false
    return
  }
  showSide = !showSide
  if (showSide) {
    await nextTick()
    const currentTop = sideRef?.offsetTop
    const maxTop = window.innerHeight - sideRef?.clientHeight
    const top = currentTop < 0 ? 0 : currentTop > maxTop ? maxTop : currentTop
    boxStyle = {
      top: `${top}px`,
      bottom: 'auto',
    }
  }
}

function handleTypeChange(value) {
  unObserve()
  currentType = value
  emit('sort', value)
  toFirstRow()
}

function closePanel() {
  if (showPop)
    return

  if (!isMove)
    showSide = false
}

function showSetting() {
  rangeText.left = props.range.rateBegin
  rangeText.right = props.range.rateEnd
  showPop = true
}

function unObserve() {
  observerList.forEach(v => v.disconnect())
  observerList = []
}

async function confirmSetting() {
  if (rangeText.left >= rangeText.right) {
    $g.showToast('请输入正确的过滤范围')
    return
  }
  unObserve()
  emit('update:range', {
    rateBegin: rangeText.left,
    rateEnd: rangeText.right,
  })
  showPop = false
  toFirstRow()
  await emit('handleSetFilterRange')
}

function toFirstRow() {
  nextTick(() => {
    if (quesListSorted.length) {
      currentIdx = 0
      selectQues(0)
    }
  })
}

function setDomObserver() {
  const domList = document.getElementsByClassName(props.itemClass)
  quesListSorted.forEach((item, index) => {
    const dom = domList[index]
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        if (!isClick)
          currentIdx = index
      }
    }, {
      threshold: 0.1, // 元素可见 10% 时就触发
    })
    observer.observe(dom)
    observerList.push(observer)
  })
}

onBeforeMount(() => {
  window.addEventListener('click', closePanel)
})

onBeforeUnmount(() => {
  unObserve()
  window.removeEventListener('click', closePanel)
})

watch(() => quesListSorted, () => {
  setDomObserver()
}, {
  immediate: true,
})
function scrollToTop() {
  emit('scrollToTop')
}
</script>

<template>
  <div
    ref="sideRef"
    class="side-box fixed right-0 bottom-100px flex items-end z-[2]"
    :style="boxStyle"
  >
    <div>
      <div v-if="scrollTop > 100"
           class="w-[51px] h-51px bg-[#6474FD] rounded-[50%] mb-13px text-[#fff] cursor-pointer"
           @click="scrollToTop"
      >
        <svg-common-top class="w-26px h-26px mx-auto"></svg-common-top>
        <div class="text-[13px] text-center">
          TOP
        </div>
      </div>
      <div
        class="w-51px h-156px z-10 flex flex-col items-center justify-center cursor-pointer active:brightness-110 select-none bg-white ques-panel rounded-[2px]"
        :class="{
          '!rounded-[2px_0_0_2px]': showSide,
        }"
        @click.stop="checkShow"
        @mousedown="onmousedown"
        @touchstart="onmousedown"
        @touchend="mouseup"
        @mouseup="mouseup"
      >
        <div class="w-17px leading-[20px] text-17px text-center font-600">
          题目面板
        </div>
        <div
          class="w-22px h-22px rounded-[22px] bg-[#CCCCCC80] flex items-center justify-center mt-12px arrow-icon pr-2px"
          :class="{
            'arrow-open': showSide,
          }"
        ></div>
      </div>
    </div>
    <div
      v-if="showSide"
      class="rounded-[9px_9px_9px_0] w-[291px] h-[529px] overflow-y-auto no-bar bg-white ques-panel px-17px pt-13px pb-21px"
      @click.stop
    >
      <div class="h-21px leading-[21px] flex items-center justify-between text-[#666666] text-14px mb-13px">
        <div class="text-[#333] text-15px font-600 flex-shrink-0">
          题目面板
        </div>
        <el-select
          :model-value="currentType"
          class="w-140px ques-panel-select"
          @update:model-value="handleTypeChange"
        >
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <!-- 题号 -->
      <div
        v-if="quesListSorted.length"
        class="w-full grid grid-cols-5 gap-x-12px gap-y-13px min-h-211px content-start"
      >
        <div v-for="(item, index) in quesListSorted" :key="index">
          <div
            class="w-43px h-43px rounded-[50%] text-center leading-[43px] border border-solid border-[transparent] text-15px  cursor-pointer active:opacity-80 select-none"
            :class="{
              'bg-[rgba(255,70,70,0.1)] bg-1':
                (60 > item.classRate),
              '!bg-[rgba(100,116,253,0.1)] bg-3':
                (60 <= item.classRate
                  && item.classRate < 70),
              '!bg-[rgba(250,173,20,0.1)] bg-2':
                (70 <= item.classRate
                  && item.classRate < 85),
              '!bg-[rgba(82,196,26,0.1)] bg-4':
                (85 <= item.classRate),
              'item-active font-600': currentIdx === index,
            }"
            @click.stop="selectQues(index)"
          >
            {{ item.questionNumber }}
          </div>
        </div>
      </div>
      <g-empty v-else :size="50"></g-empty>
      <!-- 提示信息 -->
      <div class="mt-28px p-9px bg-[#F3F4F9] rounded-[4px]">
        <div
          v-for="(item, index) in difficulty"
          :key="item.value"
          class="flex items-center"
          :class="{
            'mt-7px': index !== 0,
          }"
        >
          <div
            class="w-10px h-10px rounded-[50%] border-[2px] border-solid"
            :style="{
              borderColor: colorMap[item.value],
            }"
          ></div>
          <div class="ml-5px text-[13px] text-[#6C6C74] h-16px leading-[16px]">
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="mt-17px">
        <div class="text-[#6474FD] font-600 text-[14px] select-none hover:opacity-80 van-haptics-feedback leading-[13px]" @click.stop="showSetting">
          题目展示设置
        </div>
        <div class="flex items-center text-[#999999] mt-13px leading-[16px]">
          <svg-menu-info
            class="w-13px h-13px mr-2px -translate-y-1px"
          />
          <div class="text-[13px] text-[#999]">
            当前展示的是≤0%的全部错题
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showPop"
      class="w-395px h-269px rounded-[6px] pt-21px pb-26px flex flex-col items-center"
      :close-on-click-overlay="false"
      @click.stop
    >
      <img
        class="w-15px h-15px absolute right-21px top-21px"
        alt="close"
        src="@/assets/img/taskCenter/close.png"
        @click="showPop = false"
      />
      <div class="text-[#333] text-17px font-600 mb-38px h-24px leading-[24px]">
        设置班级得分率区间
      </div>
      <div class="flex items-center mb-35px text-15px text-[#333]">
        <div class="w-71px h-34px rounded-[2px] border border-solid border-[#DCDEE0] flex items-center">
          <el-input-number v-model="rangeText.left"
                           :controls="false"
                           class="ques-panel-input"
          ></el-input-number>
          <div class="w-35px h-full border-l border-solid border-[#DCDEE0] text-center text-[15px] leading-[34px] text-[#646566] bg-[#F7F8FA]">
            %
          </div>
        </div>
        <div class="px-10px">
          ≤
        </div>
        <div>
          班级得分率
        </div>
        <div class="px-10px">
          ≤
        </div>
        <div class="w-71px h-34px rounded-[2px] border border-solid border-[#DCDEE0] flex items-center">
          <el-input-number v-model="rangeText.right"
                           :controls="false"
                           class="ques-panel-input"
          ></el-input-number>
          <div class="w-35px h-full border-l border-solid border-[#DCDEE0] text-center text-[15px] leading-[34px] text-[#646566] bg-[#F7F8FA]">
            %
          </div>
        </div>
      </div>
      <div class="flex flex-col items-center text-14px text-[#999] leading-[20px] mb-21px">
        <div>设置后只展示符合条件的大题，且学生端也只</div>
        <div>会做展示题目的变式题</div>
      </div>
      <div class="w-75px h-30px rounded-[4px] bg-[#6474FD] text-center text-15px leading-[28px] text-white hover:opacity-80 select-none van-haptics-feedback" @click="confirmSetting">
        确定
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
.ques-panel-select{
  :deep(){
    .el-select__wrapper{
      box-shadow: none !important;
      text-align: right !important;
      padding-right:0!important;
      font-size: 14px !important;
      color:#333 !important;
    }
  }
}
.ques-panel-input{
  width: 34px!important;
  :deep() {
    .el-input, .el-input__wrapper{
      border: none!important;
      box-shadow: none!important;
      padding: 0!important;
      min-width: 34px;
      width: 34px;
    }
  }
}

.ques-panel {
  box-shadow: 0px 2px 16px 0px rgba(176, 177, 178, 0.35);
}

.arrow-icon {
  &::after {
    display: inline-block;
    content: "";
    width: 8px;
    height: 8px;
    border-left: 2px solid #666666;
    border-bottom: 2px solid #666666;
    transform: rotate(45deg) translateX(2px) translateY(-2px);
    opacity: 0.8;
  }
  &.arrow-open {
    &::after {
      transform: rotate(-135deg) translateX(0px) translateY(-1px);
    }
  }
}

.item-active {
  &.bg-1 {
    border: 1px solid #FF4646!important;
  }
  &.bg-2 {
    border: 1px solid #faad14!important;
  }
  &.bg-3 {
    border: 1px solid #6474FD!important;
  }
  &.bg-4 {
    border: 1px solid #52c41a!important;
  }
  &.bg-5 {
    border: 1px solid #80a0c5!important;
  }
}

.side-box {
  * {
    box-sizing: border-box;
  }
}
</style>
