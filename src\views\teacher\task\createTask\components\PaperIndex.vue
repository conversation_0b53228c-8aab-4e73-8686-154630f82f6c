<script setup lang="ts">
import {
  getPaperGradeList,
  getPaperList,
  getPaperSourceList,
  getPaperTypeList,
  getPaperYearList,
  getQuestionList,
} from '@/api/taskCenter'

const paperRef = $ref<any>()

const setStepVal = inject('setStepVal') as any
const questionList = inject<Ref<any[]>>('questionList', ref([]))
const currentPaperId = inject<Ref<any>>('currentPaperId', ref(null))

let expand = $ref(true)
let currentType = $ref(0)
let typeList = $ref<any>([])

let paperList = $ref<any>([])
const pageOption = reactive({
  page: 1,
  page_size: 10,
  total: 0,
})

let showMenu = $ref(false)
const route = useRoute()
const formOptions = reactive({
  items: {
    grade: {
      name: '年级',
      options: [],
    },
    semester: {
      name: '学期',
      options: [],
    },
    year: {
      name: '年份',
      options: [],
    },
    origin: {
      name: '来源',
      options: [],
    },
  },
  selected: {
    grade: [],
    semester: [],
    year: [],
    origin: [],
  },
  data: {
    grade: [],
    semester: [],
    year: [],
    origin: [],
  },
})

const filterTagList = $computed(() => {
  return Object.keys(formOptions.items).map((v) => {
    return {
      key: v,
      name: formOptions.items[v].name,
      options: formOptions.items[v].options.filter(vv => formOptions.data[v].includes(vv.value)).map(vv => vv.label),
    }
  }).filter(v => v.options.length)
})

const filterLen = $computed(() => {
  return Object.keys(formOptions.data).reduce((total, key) => {
    return total + formOptions.data[key].length
  }, 0)
})

const leftList = $computed(() => {
  return expand ? typeList : typeList.slice(0, 1)
})

function onselect(value) {
  if (currentType !== value) {
    currentType = value
    pulldown()
  }
}

function delFilter(key) {
  if (key) {
    formOptions.data[key] = []
    formOptions.selected = $g._.cloneDeep(formOptions.data)
    pulldown()
  }
  else {
    reset()
  }
}

async function onStateChange(item) {
  if (item.questionIdList.length === 0) {
    $g.showToast('没有可以加入的题')
    return
  }
  if (item.isAdd) {
    item.isAdd = false
    questionList.value = questionList.value.filter(v => item.questionIdList.every(vv => vv !== v.questionId))
  }
  else {
    item.isAdd = true
    if (!item.questionList) {
      const { list } = await getQuestionList({
        bookId: item.bookId,
        pageSize: 9999,
      })
      item.questionList = list
    }
    const existingIds = new Set(questionList.value.map(v => v.questionId))
    const newQuestions = item.questionList.filter(v => !existingIds.has(v.questionId))
    questionList.value.push(...newQuestions) // 批量推送
  }
}

function showPanel() {
  showMenu = true
}

function reset() {
  Object.keys(formOptions.selected).forEach((key) => {
    formOptions.selected[key] = []
  })
  confirm()
}

function confirm() {
  formOptions.data = $g._.cloneDeep(formOptions.selected)
  showMenu = false
  pulldown()
}

function onCancel() {
  formOptions.selected = $g._.cloneDeep(formOptions.data)
  showMenu = false
}

async function pulldown() {
  pageOption.page = 1
  await getPaperListApi()
}
async function pullup() {
  pageOption.page += 1
  await getPaperListApi(true)
}
/* 获取列表 */
async function getPaperListApi(up?) {
  const data = await getPaperList({
    page: pageOption.page,
    pageSize: pageOption.page_size,
    sysBookTypeId: typeList[currentType].sysBookTypeIds.join(','),
    sysGradeId: formOptions.data.grade.join(','),
    termId: formOptions.data.semester.join(','),
    year: formOptions.data.year.join(','),
    bookSource: formOptions.data.origin.join(','),
    sysSubjectId: route.query.subjectId,
  })
  if (data?.list?.length) {
    const list = data.list.map(v => ({
      ...v,
      isAdd: v.questionIdList.length === 0 ? false : getStatus(v.questionIdList),
    }))
    paperList = up ? paperList.concat(list) : list
    nextTick(() => {
      $g.tool.renderMathjax()
    })
    if (!up) {
      nextTick(() => {
        paperRef?.scrollIntoView()
      })
    }
  }
  else if (!up) {
    paperList = []
  }
  pageOption.total = data.total
}

function getStatus(list) {
  if (questionList.value.length < list.length)
    return false

  return list.every((v) => {
    return questionList.value.find(vv => vv.questionId === v)
  })
}

async function getPaperTypeListApi() {
  const data = await getPaperTypeList()
  typeList = [{
    label: '全部',
    value: 0,
    sysBookTypeIds: [],
  },
...data.map((v, index) => ({
    label: v.sysBookTypeName,
    value: index + 1,
    sysBookTypeIds: v.sysBookTypeIds,
  }))]
}

async function getPaperGradeListApi() {
  const data = await getPaperGradeList({
    sysSubjectId: route.query.subjectId,
  })
  formOptions.items.grade.options = data.map(v => ({
    label: v.sysGradeName,
    value: v.sysGradeId,
  }))
}

async function getPaperYearListApi() {
  const data = await getPaperYearList()
  formOptions.items.year.options = data.map(v => ({
    label: v.label,
    value: v.id,
  }))
}

async function getPaperSourceListApi() {
  const data = await getPaperSourceList()
  formOptions.items.origin.options = data.map(v => ({
    label: v.title,
    value: v.id,
  }))
}

function toDetail(item) {
  currentPaperId.value = item.bookId
  setStepVal(5)
}

onBeforeMount(async () => {
  await getPaperTypeListApi()
  getPaperListApi()
  getPaperGradeListApi()
  getPaperYearListApi()
  getPaperSourceListApi()
})

onMounted(() => {
  $g.bus.on('update-count', () => {
    paperList.forEach((v) => {
      v.isAdd = getStatus(v.questionIdList)
    })
  })
})
</script>

<template>
  <div class="relative">
    <div
      class="flex items-center absolute right-0 -top-42px text-[#6C6C74] select-none van-haptics-feedback"
      :class="{
        '!text-[#6474FD]': filterLen > 0,
      }"
      @click="showPanel"
    >
      <img
        v-if="filterLen"
        src="@/assets/img/taskCenter/filter-active.png"
        alt="filter-active icon"
        class="w-17px h-17px"
      />
      <img
        v-else
        src="@/assets/img/taskCenter/filter.png"
        alt="filter icon"
        class="w-17px h-17px"
      />
      <div class="ml-2px text-13px pt-2px">
        筛选<template v-if="filterLen">
          ({{ filterLen }})
        </template>
      </div>
    </div>
    <div class="box-h w-full flex justify-between">
      <div
        v-if="typeList.length"
        class="w-[237px] bg-white rounded-[6px] border border-solid border-[#DADDE8] p-17px mr-5px overflow-y-auto no-bar"
      >
        <div
          v-for="item in leftList"
          :key="item.value"
          class="relative w-full h-34px rounded-[6px] pl-27px flex items-center select-none van-haptics-feedback font-600 pt-1px text-[#6C6C74]"
          :class="{
            'bg-[#6474FD1A] !text-[#6474FD]': currentType === item.value,
          }"
          @click="onselect(item.value)"
        >
          <img
            v-if="item.value === 0"
            src="@/assets/img/taskCenter/select-down.png"
            alt="select icon"
            class="w-13px h-13px absolute left-4px top-1/2 -translate-y-1/2"
            :class="{
              'rotate-180': !expand,
            }"
            @click.stop="expand = !expand"
          />
          <div class="text-14px">
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="h-full flex-1 translate-x-10px">
        <div class="w-full h-24px flex items-center text-[#6C6C74] text-13px mb-9px">
          <div>{{ pageOption.total }}个结果</div>
          <template v-if="filterTagList.length">
            <div class="w-1px h-7px bg-[#6C6C744D] mx-6px" />
            <div class="max-w-[80%] flex flex-shrink-0 overflow-x-auto no-bar">
              <div
                v-for="(item, index) in filterTagList"
                :key="item.name"
                class="h-full px-7px bg-[#E8ECF6] border border-solid border-[#D7DDE9] rounded-[5px] flex items-center"
                :class="{
                  'ml-11px': index !== 0,
                }"
              >
                <div class="max-w-250px truncate mr-4px translate-y-1px">
                  {{ item.name }}:{{ item.options.join('、') }}
                </div>
                <svg-ri-close-line class="text-14px van-haptics-feedback" @click="delFilter(item.key)" />
              </div>
            </div>
            <div class="w-1px h-7px bg-[#6C6C744D] mx-11px" />
            <img
              src="@/assets/img/taskCenter/bin.png"
              alt="bin icon"
              class="w-17px h-17px van-haptics-feedback"
              @click="delFilter('')"
            />
          </template>
        </div>
        <div class="list-h">
          <el-scrollbar class="pr-10px">
            <g-list
              ref="scrollRef"
              v-model:data="paperList"
              :page-option="pageOption"
              url="/tutoring/admin/task/book/paper/list"
              @pulldown="pulldown"
              @pullup="pullup"
            >
              <div v-if="paperList.length"
                   ref="paperRef"
                   class="w-full"
              ></div>
              <div
                v-for="(item, index) in paperList"
                :key="item.bookId"
                class="w-full h-74px bg-white rounded-[6px] border border-solid border-[#DADDE8] py-15px pl-18px pr-43px flex items-center justify-between cursor-pointer"
                :class="{
                  'mt-9px': index !== 0,
                }"
                @click="toDetail(item)"
              >
                <div class="no-title-w">
                  <div class="w-full text-[#333] text-14px font-600 mb-9px truncate">
                    <g-mathjax :text="item.bookName" />
                  </div>
                  <div class="text-[#6C6C74] text-13px">
                    题量：{{ item.questionTotal }}
                  </div>
                </div>
                <div class="w-77px h-30px flex-cc">
                  <div
                    class="w-66px pt-1px h-full border border-solid border-[#6474FD] text-15px text-[#6474FD] rounded-[4px] flex-cc select-none van-haptics-feedback transition-all duration-150 ease-out"
                    :class="{
                      '!w-full !border-[#FF4646] !text-[#FF4646]': item.isAdd,
                    }"
                    @click.stop="onStateChange(item)"
                  >
                    {{ !item.isAdd ? '加入' : '取消加入' }}
                  </div>
                </div>
              </div>
            </g-list>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showMenu"
      position="right"
      :style="{ width: '550px', height: '100%' }"
      :transition-appear="true"
      class="px-21px pt-17px pb-10px"
      @close="onCancel"
    >
      <div class="flex flex-col w-full h-full">
        <div class="flex items-center justify-between w-full mb-21px">
          <div class="text-17px text-[#333] font-600">
            筛选
          </div>
          <img
            src="@/assets/img/taskCenter/close.png"
            alt="close icon"
            class="w-15px h-15px select-none van-haptics-feedback"
            @click="showMenu = false"
          />
        </div>
        <div class="flex-1">
          <div v-for="(item, key) in formOptions.items"
               :key="item.name"
               class="flex items-start mb-12px"
          >
            <div class="text-[#333] text-14px font-600 mr-10px flex-shrink-0 pt-5px">
              {{ item.name }}
            </div>
            <g-radio
              v-if="item.options.length"
              v-model="formOptions.selected[key]"
              :option="item.options"
              :replace-keys="{ id: 'value', name: 'label' }"
              item-class="px-12px py-7px leading-[16px] mb-8px text-14px mr-20px"
              multiply
            />
            <div v-else class="ml-12px text-[#666] h-30px mb-8px leading-[30px]">
              ---
            </div>
          </div>
        </div>
        <div class="w-full flex items-center justify-between px-2px text-15px">
          <div
            class="w-160px h-30px rounded-[4px] bg-[#6474FD] border border-solid border-[#6474FD] text-white flex-cc van-haptics-feedback"
            @click="confirm"
          >
            确定
          </div>
          <div
            class="w-160px h-30px rounded-[4px] bg-white border border-solid border-[#6474FD] text-[#6474FD] flex-cc van-haptics-feedback"
            @click="reset"
          >
            重置
          </div>
          <div
            class="w-160px h-30px rounded-[4px] bg-white border border-solid border-[#CCCCCC] text-[#6C6C74] flex-cc van-haptics-feedback"
            @click="onCancel"
          >
            取消
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped>
.box-h {
  height: calc(100vh - 26px - 9vh - 33px - 35px - 33px - 13px - 15px);
}

.list-h {
  height: calc(100% - 24px - 9px);
}

.no-title-w{
  width: calc(100vw - 26px - 26px - 237px - 100px - 10px - 43px - 18px);/* apply-without-convert */
}
</style>
