<script setup lang="ts">
import { getCorrectionFlow } from '@/api/activity'

const props = defineProps({
  questionItem: {
    type: Object,
    default: () => {},
  },
})

const emit = defineEmits(['update:show'])
let showDialog = defineModel<boolean>('show')
// 题目列表
let questionRecordList = $ref<any[]>([])
let showLoading = $ref(true)
let classMap = {
  1: 'bg-[rgba(255,70,70,0.1)] !border-[rgba(255,70,70,0.1)] !border-[transparent]', // 全错
  2: 'bg-[rgba(255,188,41,0.15)] !border-[transparent]', // 半对
  3: 'bg-[rgba(0,218,143,0.1)] !border-[transparent]', //  全对
  4: 'bg-[rgba(71,41,255,0.1)] !border-[transparent]', // 我不会
  5: 'bg-[#EEEEEE] !border-[transparent]', // 未答或未核对
}

let classMapChecked = {
  1: 'bg-[rgba(255,70,70,0.1)] border border-[rgba(255,68,70,1)]', // 全错
  2: 'bg-[rgba(255,188,41,0.15)] border border-[rgba(255,188,41,1)]', // 半对
  3: 'bg-[rgba(0,218,143,0.1)] border border-[rgba(0,218,143,1)]', //  全对
  4: 'bg-[rgba(71,41,255,0.1)] border border-[rgba(71,41,255,1)]', // 我不会
  5: 'bg-[#EEEEEE] border border-[rgba(102,102,102,1)]', // 未答或未核对
}

async function open() {
  try {
    showLoading = true
    let res = await getCorrectionFlow({
      errorBookId: props.questionItem.errorBookId,
      questionId: props.questionItem.questionId,
    })
    showLoading = false
    questionRecordList = res
  }
  catch (err) {
    showLoading = false
    questionRecordList = []
    console.log(err)
  }
}
const list = $computed(() => {
  let arr: any = [...questionRecordList]
  arr.forEach((v) => {
    if (v.exerciseSubRecords.length) {
      v.exerciseSubRecords.forEach((item, index) => {
        item.isChecked = index === 0
        if (item.answerType == 1) {
          item.content = $g.tool.isTrue(item.answer) && item.isCorrect != 4
            ? `我的答案：${item.answer}`
            : '我的答案：我不会'
          item.type = 2
        }
        if (item.answerType == 2) {
          item.url = item.whiteBoard.split(',')
          item.type = 1
        }
        if (item.answerType == 3) {
          item.content = item.keybord
          item.type = 2
        }
        if (item.answerType == 4) {
          item.url = item.image.split(',')
          item.type = 1
        }
        item.number = index + 1
        item.isCorrect =
          $g.tool.isTrue(item.isCorrect) == true ? item.isCorrect : 5
        let flagList: any = [
          item.answer,
          item.keybord,
          item.whiteBoard,
          item.image,
        ]
        let flag = flagList.some(v => $g.tool.isTrue(v))
        item.title =
          item.isCorrect == 1
            ? ''
            : item.isCorrect == 2
              ? '部分对'
              : item.isCorrect == 3
                ? ''
                : item.isCorrect == 4
                  ? '我不会'
                  : flag == true
                    ? '未核对'
                    : '未作答'
      })
    }
  })
  return arr
})

// 小题题号点击
function handleQuestionNumberClick(question, subQuestion) {
  question.exerciseSubRecords.forEach((item) => {
    if (item.number == subQuestion.number)
      item.isChecked = true

    else
      item.isChecked = false
  })
}

function getCheckedItem(questions) {
  let obj = questions.find(q => q.isChecked)
  return obj
}
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="custom-van-popup"
    teleport="#app"
    overlay-class="bg-[rgba(0,0,0,0.3)] "
    round
    v-bind="$attrs"
    closeable
    title="订正记录"
    @open="open"
  >
    <div class="text-left text-17px font-600 text-[#000000] mb-21px px-27px">
      订正记录
    </div>
    <div class="overflow-auto px-27px">
      <g-loading v-if="showLoading" class="h-200px"></g-loading>

      <template v-else>
        <template v-if="list?.length">
          <el-timeline>
            <el-timeline-item
              v-for="(item) in list"
              :key="item.exerciseRecordId"
              size="large"
              class="uni-line"
            >
              <div>
                <div class="text-13px text-[#333333] font-500">
                  <span>订正详情</span>
                  <span class="ml-10px text-[#666666]">{{ item.exerciseTime }}</span>
                </div>
                <div class="mt-17px flex flex-wrap select-none content-start">
                  <div
                    v-for="(subQuestion, index) in item.exerciseSubRecords"
                    :key="subQuestion.exerciseRecordId"
                    class="question-number relative"
                    :class="[
                      subQuestion.isChecked
                        ? classMapChecked[subQuestion.isCorrect]
                        : classMap[subQuestion.isCorrect],
                    ]"
                    @click="handleQuestionNumberClick(item, subQuestion)"
                  >
                    <span
                      class="text-12px leading-[16px] text-[#3c1500] font-500"
                    >
                      {{ index + 1 }}
                    </span>

                    <span
                      v-if="$g.tool.isTrue(subQuestion.title)"
                      class="text-11px text-[#666666] font-400"
                    >
                      {{ subQuestion.title }}
                    </span>
                    <div
                      v-if="subQuestion.isInErrorBook == 2"
                      class="bg-[#FF4446] absolute top-[-11px] right-[-8px] text-10px w-28px h-15px flex items-center justify-center rounded-r-[7px] rounded-tl-[7px] text-[#fff]"
                    >
                      <span>错题</span>
                    </div>
                  </div>
                </div>
                <!-- 订正内容 -->
                <div
                  v-if="getCheckedItem(item.exerciseSubRecords).type == 1"
                  class="flex flex-wrap content-start mb-17px"
                >
                  <g-img
                    v-for="(urls, index) in getCheckedItem(
                      item.exerciseSubRecords,
                    ).url"
                    :key="index"
                    preview
                    :src="urls"
                    radius="6px"
                    class="mr-21px cursor-pointer"
                  />
                </div>
                <div
                  v-if="getCheckedItem(item.exerciseSubRecords).type == 2"
                  class="w-610px rounded-[6px] max-h-[90px] overflow-auto p-13px bg-[#F5F5F5] text-[14px] text-[#666666] break-words whitespace-pre-wrap"
                >
                  {{ getCheckedItem(item.exerciseSubRecords).content }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </template>
        <g-empty v-else></g-empty>
      </template>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.custom-van-popup {
  @apply flex flex-col overflow-auto pt-21px pb-26px w-[704px] h-[80vh] br-[13px] bg-gradient-to-b from-[#ffe6d6] via-[70px] via-[#ffffff];
  :deep() {
    .van-icon-cross {
      color: #333333;
      font-size: 18px;
      font-weight: 600;
      top: 18px;
    }
  }
}

.uni-line {
  :deep() {
    padding-bottom: 0px !important;
    .el-timeline-item {
      padding-bottom: 0px !important;
    }
    .el-timeline-item__node {
      width: 6px;
      height: 6px;
      background: #cccccc;
      left: 2px;
      top: 4px;
    }
    .el-timeline-item__tail {
      border-left: 2px dashed #e8e8e8;
      top: 10px;
    }
  }
}

.question-number {
  @apply w-45px h-45px rounded-[100%] flex flex-col justify-center items-center mr-17px mb-11px cursor-pointer;

  &:last-child {
    margin-right: auto;
  }
}
</style>
