<script>
import { useMindMapStore } from '@/stores/modules/mindMap'
// import { getMindMapUid } from "@/api/bookMgt"
let bus = $g.bus
const store = useMindMapStore()

/**
 * @Desc: 右键菜单
 */
export default {
  name: 'Contextmenu',
  props: {
    mindMap: {
      type: Object,
    },
    isZenMode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShow: false,
      left: 0,
      top: 0,
      node: null,
      type: '',
      isMousedown: false,
      mosuedownX: 0,
      mosuedownY: 0,
    }
  },
  computed: {
    // isZenMode() {
    //   return store.localConfig.isZenMode
    // },
    expandList() {
      return [
        '一级主题',
        '二级主题',
        '三级主题',
        '四级主题',
        '五级主题',
        '六级主题',
      ]
    },
    insertNodeBtnDisabled() {
      return !this.node || this.node.isRoot || this.node.isGeneralization
    },
    upNodeBtnDisabled() {
      if (!this.node || this.node.isRoot || this.node.isGeneralization)
        return true

      let isFirst =
        this.node.parent.children.findIndex((item) => {
          return item === this.node
        }) === 0
      return isFirst
    },
    downNodeBtnDisabled() {
      if (!this.node || this.node.isRoot || this.node.isGeneralization)
        return true

      let children = this.node.parent.children
      let isLast =
        children.findIndex((item) => {
          return item === this.node
        }) ===
        children.length - 1
      return isLast
    },
    isGeneralization() {
      return this.node.isGeneralization
    },
    hasHyperlink() {
      return !!this.node.getData('hyperlink')
    },
    hasNote() {
      return !!this.node.getData('note')
    },
  },
  created() {
    bus.on('mind_map_node_contextmenu', this.show)
    bus.on('mind_map_node_click', this.hide)
    bus.on('mind_map_draw_click', this.hide)
    bus.on('mind_map_expand_btn_click', this.hide)
    bus.on('mind_map_svg_mousedown', this.onMousedown)
    bus.on('mind_map_mouseup', this.onMouseup)
  },
  beforeUnmount() {
    bus.off('mind_map_node_contextmenu', this.show)
    bus.off('mind_map_node_click', this.hide)
    bus.off('mind_map_draw_click', this.hide)
    bus.off('mind_map_expand_btn_click', this.hide)
    bus.off('mind_map_svg_mousedown', this.onMousedown)
    bus.off('mind_map_mouseup', this.onMouseup)
  },
  methods: {
    /**
     * @Desc: 节点右键显示
     */
    // mitt只能传一个参数
    show([e, node]) {
      this.type = 'node'
      this.left = e.clientX + 10
      this.top = e.clientY + 10
      this.isShow = true
      this.node = node
    },

    /**
     * @Desc: 鼠标按下事件
     */
    onMousedown(e) {
      if (e.which !== 3)
        return

      this.mosuedownX = e.clientX
      this.mosuedownY = e.clientY
      this.isMousedown = true
    },

    /**
     * @Desc: 鼠标松开事件
     */
    onMouseup(e) {
      if (!this.isMousedown)
        return

      this.isMousedown = false
      if (
        Math.abs(this.mosuedownX - e.clientX) > 3 ||
        Math.abs(this.mosuedownY - e.clientY) > 3
      ) {
        this.hide()
        return
      }
      this.show2(e)
    },

    /**
     * @Desc: 画布右键显示
     */
    show2(e) {
      this.type = 'svg'
      this.left = e.clientX + 10
      this.top = e.clientY + 10
      this.isShow = true
    },

    /**
     * @Desc: 隐藏
     */
    hide() {
      this.isShow = false
      this.left = 0
      this.top = 0
      this.type = ''
    },

    /**
     * @Desc: 执行命令
     */
    async exec(key, disabled, ...args) {
      const containsInsertAndNode = str =>
        /INSERT.*Node|Node.*INSERT/i.test(str)
      if (disabled)
        return

      switch (key) {
        case 'COPY_NODE':
          this.mindMap.renderer.copy()
          break
        case 'CUT_NODE':
          this.mindMap.renderer.cut()
          break
        case 'PASTE_NODE':
          this.mindMap.renderer.paste()
          break
        case 'RETURN_CENTER':
          this.mindMap.renderer.setRootNodeCenter()
          break
        case 'TOGGLE_ZEN_MODE':
          this.$emit('changeIsZenMode')
          break
        case 'FIT_CANVAS':
          this.mindMap.view.fit()
          break
        case 'REMOVE_HYPERLINK':
          this.node.setHyperlink('', '')
          break
        case 'REMOVE_NOTE':
          this.node.setNote('')
          break
        default:
          // bus.emit("mind_map_execCommand", [key, ...args])
          if (containsInsertAndNode(key)) {
            // let { id } = await getMindMapUid()
            this.mindMap.execCommand(...[key, ...args], true, null, {
              uid: $g.tool.uuid(),
            })
          }
          else {
            this.mindMap.execCommand(...[key, ...args])
          }
          break
      }
      this.hide()
    },
  },
}
</script>

<template>
  <div
    v-if="isShow"
    class="contextmenuContainer listBox"
    :style="{ left: `${left}px`, top: `${top}px`, zIndex: 9 }"
  >
    <template v-if="type === 'node'">
      <div
        class="item"
        :class="{ disabled: insertNodeBtnDisabled }"
        @click="exec('INSERT_NODE', insertNodeBtnDisabled)"
      >
        <span class="name">插入同级节点</span>
        <span class="desc">Enter</span>
      </div>
      <div
        class="item"
        :class="{ disabled: isGeneralization }"
        @click="exec('INSERT_CHILD_NODE')"
      >
        <span class="name">插入子级节点</span>
        <span class="desc">Tab</span>
      </div>
      <div
        class="item"
        :class="{ disabled: insertNodeBtnDisabled }"
        @click="exec('INSERT_PARENT_NODE')"
      >
        <span class="name">插入父节点</span>
        <span class="desc">Shift + Tab</span>
      </div>
      <div
        class="item"
        :class="{ disabled: insertNodeBtnDisabled }"
        @click="exec('ADD_GENERALIZATION')"
      >
        <span class="name">插入概要</span>
        <span class="desc">Ctrl + G</span>
      </div>
      <div
        class="item"
        :class="{ disabled: upNodeBtnDisabled }"
        @click="exec('UP_NODE')"
      >
        <span class="name">上移节点</span>
        <span class="desc">Ctrl + ↑</span>
      </div>
      <div
        class="item"
        :class="{ disabled: downNodeBtnDisabled }"
        @click="exec('DOWN_NODE')"
      >
        <span class="name">下移节点</span>
        <span class="desc">Ctrl + ↓</span>
      </div>
      <div class="item danger" @click="exec('REMOVE_NODE')">
        <span class="name">删除节点</span>
        <span class="desc">Delete</span>
      </div>
      <div class="item danger" @click="exec('REMOVE_CURRENT_NODE')">
        <span class="name">仅删除当前节点</span>
        <span class="desc">Shift + Backspace</span>
      </div>
      <div
        class="item"
        :class="{ disabled: isGeneralization }"
        @click="exec('COPY_NODE')"
      >
        <span class="name">复制节点</span>
        <span class="desc">Ctrl + C</span>
      </div>
      <div
        class="item"
        :class="{ disabled: isGeneralization }"
        @click="exec('CUT_NODE')"
      >
        <span class="name">剪切节点</span>
        <span class="desc">Ctrl + X</span>
      </div>
      <div class="item" @click="exec('PASTE_NODE')">
        <span class="name">粘贴节点</span>
        <span class="desc">Ctrl + V</span>
      </div>
      <div v-if="hasHyperlink"
           class="item"
           @click="exec('REMOVE_HYPERLINK')"
      >
        <span class="name">移除超链接</span>
      </div>
      <div v-if="hasNote"
           class="item"
           @click="exec('REMOVE_NOTE')"
      >
        <span class="name">移除备注</span>
      </div>
    </template>
    <template v-if="type === 'svg'">
      <div class="item" @click="exec('RETURN_CENTER')">
        <span class="name">回到根节点</span>
        <span class="desc">Ctrl + Enter</span>
      </div>
      <div class="item" @click="exec('EXPAND_ALL')">
        <span class="name">展开所有</span>
      </div>
      <div class="item" @click="exec('UNEXPAND_ALL')">
        <span class="name">收起所有</span>
      </div>
      <div class="item">
        <span class="name">展开到</span>
        <div class="subItems listBox">
          <div
            v-for="(item, index) in expandList"
            :key="item"
            class="item"
            @click="exec('UNEXPAND_TO_LEVEL', false, index + 1)"
          >
            {{ item }}
          </div>
        </div>
      </div>
      <div class="item" @click="exec('RESET_LAYOUT')">
        <span class="name">一键整理布局</span>
        <span class="desc">Ctrl + L</span>
      </div>
      <div class="item" @click="exec('FIT_CANVAS')">
        <span class="name">适应画布</span>
        <span class="desc">Ctrl + i</span>
      </div>
      <div class="item" @click="exec('TOGGLE_ZEN_MODE')">
        <span class="name">禅模式</span>
        {{ isZenMode ? '√' : '' }}
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.listBox {
  width: 260px;
  background: #fff;
  box-shadow: 0 4px 12px 0 hsla(0, 0%, 69%, 0.5);
  border-radius: 4px;
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.contextmenuContainer {
  position: fixed;
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #1a1a1a;

  .item {
    position: relative;
    height: 28px;
    line-height: 28px;
    padding: 0 16px !important;
    cursor: pointer;
    display: flex;
    justify-content: space-between;

    &.danger {
      color: #f56c6c;
    }

    &:hover {
      background: #f5f5f5;

      .subItems {
        visibility: visible;
      }
    }

    &.disabled {
      color: grey;
      cursor: not-allowed;
      pointer-events: none;

      &:hover {
        background: #fff;
      }
    }

    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .desc {
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .subItems {
      position: absolute;
      left: 100%;
      top: 0;
      visibility: hidden;
    }
  }
}
</style>
