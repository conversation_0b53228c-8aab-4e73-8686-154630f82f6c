<script setup lang="ts">
import StudentAnswerDetail from './studentAnswerDetail.vue'

const props = defineProps<{
  scoreStatistics: any
  examId: any
  sysCourseId: any
  questionId: any
  examPaperId: any
  structureNumber: any
  answer: string
  currentSubIndex: number
}>()

const currentSubIndexNew = $computed(() => {
  return props.scoreStatistics.length > 1 ? props.currentSubIndex : 0
})

// 是否是选择题
const choiceQuestion = $computed(() => {
  return [1,
2,
3].includes(props.scoreStatistics?.[currentSubIndexNew]?.type)
})

let showDialog = $ref(false)

let currentOption = $ref<any>(null)

function optionClick(option: any) {
  currentOption = option
  showDialog = true
}
</script>

<template>
  <div class="bg-[white] br-[9px] overflow-hidden">
    <template v-if="scoreStatistics?.length > 0">
      <div class="flex justify-between mb-17px">
        <div>
          <span class="text-16px font-500">点击柱状图可查看学生名单</span>
          <span v-if="choiceQuestion" class="text-[#52C41A] ml-13px">本题选项{{ answer }}</span>
        </div>
        <span v-if="choiceQuestion" class="text-[#999]">（无学生选择的选项未展示）</span>
      </div>
      <div v-if="scoreStatistics[currentSubIndexNew].items?.length > 0" class="flex">
        <div>
          <div
            v-for="(i, index) in scoreStatistics[currentSubIndexNew].items"
            :key="i.title"
            class="font-500"
            :class="{ 'mb-22px': index !== scoreStatistics[currentSubIndexNew].items.length - 1 }"
          >
            <span v-if="choiceQuestion" class="font-500">
              选项{{ i.title }}
            </span>
            <span v-else>分数{{ i.title }}</span>
          </div>
        </div>
        <div class="flex-1">
          <div
            v-for="(i, index) in scoreStatistics[currentSubIndexNew].items"
            :key="i.title"
            class="flex justify-between items-center cursor-pointer"
            :class="{ 'mb-22px': index !== scoreStatistics[currentSubIndexNew].items.length - 1 }"
            @click="optionClick(i)"
          >
            <div class="flex-1 mx-13px">
              <el-progress
                :percentage="$g.math(i.rate).multiply(100).value()"
                :stroke-width="19"
                text-inside
                striped
                :color="answer == i.title ? '#52C41A' : '#6474FD'"
              />
            </div>
            <div class="text-[#999] w-[45px] flex-shrink-0">
              {{ i.students?.length }}人
            </div>
          </div>
        </div>
      </div>
    </template>
    <g-empty v-else></g-empty>
    <!-- 详情弹窗 -->
    <!-- <template v-if="showDialog"> -->
    <StudentAnswerDetail
      v-model:show-dialog="showDialog"
      :choice-question="choiceQuestion"
      :current-option="currentOption"
      :params="{ examId, sysCourseId, questionId, examPaperId }"
      :structure-number="structureNumber"
      :current-sub-index="currentSubIndex"
    />
    <!-- </template> -->
  </div>
</template>

<style lang="scss" scoped>
</style>
