<script setup lang="ts">
const props = withDefaults(defineProps<{
  quesList: any[]
  currentIdx?: number
  plain?: boolean
}>(), {
  quesList: () => [],
  currentIdx: -1,
  plain: false,
})

const emit = defineEmits<{
  select: [args: any]
}>()

const sideRef = $ref<any>()
let boxStyle = $ref<any>({})
let startY = 0
let startTop = 0
let isMove = false

let showSide = $ref(false)

function selectQues(item, index) {
  emit('select', [item, index])
}

function onmousedown(e) {
  startY = e.clientY || e.touches[0].clientY
  startTop = sideRef?.offsetTop
  window.addEventListener('mousemove', onmousemove)
  window.addEventListener('touchmove', onmousemove)
  window.addEventListener('mouseup', mouseup)
  window.addEventListener('touchend', mouseup)
}

function onmousemove(e) {
  const clientY = e.clientY || e.touches[0].clientY
  const offsetY = startTop + (clientY - startY)
  if (Math.abs(clientY - startY) > 5)
    isMove = true

  const maxTop = window.innerHeight - sideRef?.clientHeight
  const top = offsetY < 0 ? 0 : offsetY > maxTop ? maxTop : offsetY
  boxStyle = {
    top: `${top}px`,
    bottom: 'auto',
  }
}

function mouseup() {
  window.removeEventListener('mousemove', onmousemove)
  window.removeEventListener('touchmove', onmousemove)
  window.removeEventListener('mouseup', mouseup)
  window.removeEventListener('touchend', mouseup)
  setTimeout(() => {
    isMove = false
  }, 0)
}

async function checkShow() {
  if (isMove) {
    isMove = false
    return
  }
  showSide = !showSide
  if (showSide) {
    await nextTick()
    const currentTop = sideRef?.offsetTop
    const maxTop = window.innerHeight - sideRef?.clientHeight
    const top = currentTop < 0 ? 0 : currentTop > maxTop ? maxTop : currentTop
    boxStyle = {
      top: `${top}px`,
      bottom: 'auto',
    }
  }
}

function closePanel() {
  if (!isMove)
    showSide = false
}

onBeforeMount(() => {
  window.addEventListener('click', closePanel)
})

onBeforeUnmount(() => {
  window.removeEventListener('click', closePanel)
})
</script>

<template>
  <div
    ref="sideRef"
    class="side-box fixed right-0 bottom-100px flex items-end z-[2]"
    :style="boxStyle"
  >
    <div
      class="w-51px h-156px z-10 flex flex-col items-center justify-center cursor-pointer active:brightness-110 select-none bg-white ques-panel rounded-[2px]"
      :class="{
        '!rounded-[2px_0_0_2px]': showSide,
      }"
      @click.stop="checkShow"
      @mousedown="onmousedown"
      @touchstart="onmousedown"
      @touchend="mouseup"
      @mouseup="mouseup"
    >
      <div class="w-17px leading-[20px] text-17px text-center font-600">
        题目面板
      </div>
      <div
        class="w-22px h-22px rounded-[22px] bg-[#CCCCCC80] flex items-center justify-center mt-12px arrow-icon pr-2px"
        :class="{
          'arrow-open': showSide,
        }"
      ></div>
    </div>
    <div
      v-if="showSide"
      class="rounded-[9px_9px_9px_0] w-533px h-352px overflow-y-auto no-bar bg-white ques-panel p-17px pr-32px"
      @click.stop
    >
      <div v-if="!plain" class="flex items-center text-[#666666] text-14px mb-21px">
        <div class="text-17px mr-17px">
          正确率
        </div>
        <div class="flex items-center mr-20px">
          <div class="w-15px h-15px rounded-[2px] bg-[#FF4646] mr-6px"></div>
          <div>&lt; 60%</div>
        </div>
        <div class="flex items-center mr-20px">
          <div class="w-15px h-15px rounded-[2px] bg-[#FAAD14] mr-6px"></div>
          <div>60%-70%</div>
        </div>
        <div class="flex items-center mr-20px">
          <div class="w-15px h-15px rounded-[2px] bg-[#6474FD] mr-6px"></div>
          <div>70%-85%</div>
        </div>
        <div class="flex items-center mr-20px">
          <div class="w-15px h-15px rounded-[2px] bg-[#52C41A] mr-6px"></div>
          <div>85%-100%</div>
        </div>
      </div>
      <div v-else class="text-[#666666] text-17px mb-21px font-600">
        题目面板
      </div>
      <div
        v-if="quesList.length"
        class="w-full grid grid-cols-5 gap-x-28px gap-y-21px"
      >
        <div v-for="(item, index) in quesList" :key="index">
          <div
            class="w-full h-49px rounded-[23px] text-center leading-[49px] border border-solid border-[transparent] mb-5px text-21px font-600 cursor-pointer active:opacity-80 select-none"
            :class="{
              'bg-[#F5222D1E] text-[#FF4646] bg-1':
                (60 > item.correctRate && !plain) || (plain && item.isCorrect !== 3),
              '!bg-[#FAAD141E] !text-[#FAAD14] bg-2':
                (60 <= item.correctRate
                  && item.correctRate < 70) && !plain,
              '!bg-[#1EA0F01E] !text-[#6474FD] bg-3':
                (70 <= item.correctRate
                  && item.correctRate < 85) && !plain,
              '!bg-[#52C41A1E] !text-[#52C41A] bg-4':
                (85 <= item.correctRate && !plain) || (plain && item.isCorrect === 3),
              '!bg-[#F3F4F9] !text-[#3B4983] bg-5':
                item.correctRate === null
                || item.correctRate === undefined,
              'item-active': currentIdx === index,
            }"
            @click.stop="selectQues(item, index)"
          >
            {{ item.questionIndex ?? index + 1 }}
          </div>
          <div
            v-if="!plain"
            class="w-full text-center h-19px leading-[19px] text-15px text-[#FF4646]"
            :class="{
              '!text-[#666666]': item.correctRate >= 60,
              '!text-[#3B4983]':
                item.correctRate === null
                || item.correctRate === undefined,
            }"
          >
            <template
              v-if="
                item.correctRate === null
                  || item.correctRate === undefined
              "
            >
              --
            </template>
            <template v-else>
              {{ item.correctRate || 0 }}%
            </template>
          </div>
        </div>
      </div>
      <g-empty v-else :size="200"></g-empty>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ques-panel {
  box-shadow: 0px 2px 16px 0px rgba(176, 177, 178, 0.35);
}

.arrow-icon {
  &::after {
    display: inline-block;
    content: "";
    width: 8px;
    height: 8px;
    border-left: 2px solid #666666;
    border-bottom: 2px solid #666666;
    transform: rotate(45deg) translateX(2px) translateY(-2px);
    opacity: 0.8;
  }
  &.arrow-open {
    &::after {
      transform: rotate(-135deg) translateX(0px) translateY(-1px);
    }
  }
}

.item-active {
  &.bg-1 {
    border: 1px solid #f5222d!important;
  }
  &.bg-2 {
    border: 1px solid #faad14!important;
  }
  &.bg-3 {
    border: 1px solid #1ea0f0!important;
  }
  &.bg-4 {
    border: 1px solid #52c41a!important;
  }
  &.bg-5 {
    border: 1px solid #80a0c5!important;
  }
}

.side-box {
  * {
    box-sizing: border-box;
  }
}
</style>
