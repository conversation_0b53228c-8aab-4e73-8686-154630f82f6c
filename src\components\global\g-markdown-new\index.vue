<script setup lang="ts">
interface IProps {
  // markdown内容
  content: string
  // 容器高度
  height?: string
  // 是否全部加载
  isAllLoad?: boolean
}

let props = withDefaults(defineProps<IProps>(), {
  height: '600px',
  isAllLoad: false,
})

let currentContent = $ref('')
let observer = $ref<IntersectionObserver | null>(null)
let loadMoreTrigger = $ref<HTMLElement | null>(null)
const CHUNK_SIZE = 3000

// 存储分割后的内容块
let contentChunks = $ref<string[]>([])
// 当前显示的块数
let currentChunkIndex = $ref(0)

// 最小内容长度阈值（字符数）
const MIN_CHUNK_LENGTH = 1000

// 是否正在加载
let isLoading = $ref(false)

/** 根据标题分割内容并合并短段落 */
function splitContentByHeadings(content: string): string[] {
  // 匹配以 # 开头的标题行
  const headingRegex = /^#{1,6}\s+[^\n]+/gm
  const positions: number[] = []

  // 找到所有标题的位置
  let match: any = headingRegex.exec(content)
  while (match !== null) {
    positions.push(match.index)
    match = headingRegex.exec(content)
  }

  // 如果没有找到标题，返回整个内容作为一个块
  if (positions.length === 0)
    return [content]

  // 根据标题位置分割内容
  let chunks: string[] = []
  positions.forEach((pos, index) => {
    const start = pos
    const end = positions[index + 1] || content.length
    chunks.push(content.slice(start, end))
  })

  // 如果第一个标题前有内容，添加到第一个块
  if (positions[0] > 0)
    chunks.unshift(content.slice(0, positions[0]))

  // 合并短段落
  const mergedChunks: string[] = []
  let currentMergedChunk = ''

  for (const chunk of chunks) {
    currentMergedChunk += chunk

    // 当累积内容超过最小长度时，保存并重置
    if (currentMergedChunk.length >= MIN_CHUNK_LENGTH) {
      mergedChunks.push(currentMergedChunk)
      currentMergedChunk = ''
    }
  }

  // 处理最后剩余的内容
  if (currentMergedChunk) {
    // 如果最后的内容太短，合并到前一个块
    if (
      mergedChunks.length > 0 &&
      currentMergedChunk.length < MIN_CHUNK_LENGTH
    ) 
      mergedChunks[mergedChunks.length - 1] += currentMergedChunk


    else 
      mergedChunks.push(currentMergedChunk)

  }

  return mergedChunks
}

function initObserver() {
  // 添加配置项，使观察器更敏感
  observer = new IntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting)
        loadMoreContent()
    },
    {
      // 设置根元素的外边距，提前触发加载
      rootMargin: '200px',
      // 降低触发阈值，提高灵敏度
      threshold: 0.1,
    },
  )
}

/** 加载更多内容 */
function loadMoreContent() {
  if (currentChunkIndex < contentChunks.length) {
    isLoading = true
    setTimeout(() => {
      currentContent += contentChunks[currentChunkIndex]
      currentChunkIndex++
      isLoading = false

      // 检查是否已加载所有内容
      if (currentChunkIndex >= contentChunks.length)
        observer?.disconnect()
    }, 1000)
  }
}

/** 为所有图片添加点击事件 */
function addClickEventToImages() {
  setTimeout(() => {
    const eleArr: HTMLImageElement[] = Array.from(
      document.querySelectorAll('.custom-md-preview img'),
    )
    if (eleArr.length) {
      eleArr.forEach((ele) => {
        ele.style.cursor = 'pointer' // 添加手型光标样式
        ele.addEventListener('click', () => handleImageClick(ele))
      })
    }
  }, 300)
}

/** 处理图片点击 */
function handleImageClick(ele: HTMLImageElement) {
  $g.flutter('previewImage', {
    urls: [ele.src],
    index: 0,
    useBigBackButton: true,
  })
}

onMounted(() => {
  // 初始化内容分块
  contentChunks = splitContentByHeadings(props.content)

  // 如果是全部加载模式，直接显示所有内容
  if (props.isAllLoad) {
    currentContent = props.content
  }
  else {
    // 加载第一块内容
    currentContent = contentChunks[0] || ''
    currentChunkIndex = 1

    initObserver()
    if (loadMoreTrigger)
      observer?.observe(loadMoreTrigger)
  }

  // 等待初始内容渲染完成后添加事件
  nextTick(() => {
    addClickEventToImages()
  })
})

onUnmounted(() => {
  observer?.disconnect()
})
</script>

<template>
  <div class="relative">
    <v-md-preview
      class="custom-md-preview"
      :text="currentContent"
      :height="height"
    ></v-md-preview>

    <div
      v-if="!props.isAllLoad"
      ref="loadMoreTrigger"
      class="absolute h-40px bottom-[0px] left-1/2 -translate-x-1/2"
    >
      <div
        v-if="isLoading"
        class="flex items-center justify-center h-full text-[#666] text-14px"
      >
        正在加载更多内容...
      </div>
    </div>
  </div>
</template>
