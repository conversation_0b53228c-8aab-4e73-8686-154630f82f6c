import type { AppRouteRecordRaw } from '/#/router'

import Layout from '@/layout/index.vue'
import tool from '@/utils/tool'

import { createRouter, createWebHashHistory } from 'vue-router'

import { setupPermissions } from './permissions'

export const routes: AppRouteRecordRaw[] = [
  {
    path: '/',
    name: 'Root',
    component: Layout,
    redirect: tool.isPCTest() ? '/debugging' : '',
    meta: { title: '其他' },
    children: [
      {
        path: 'debugging',
        name: 'Debugging',
        component: () => import('@/views/debugging/index.vue'),
        meta: {
          title: '开发调试页面',
          public: true,
        },
      },
      {
        path: 'demo',
        name: 'Demo',
        component: () => import('@/views/demo/index.vue'),
        meta: {
          title: 'Demo测试页面',
          public: true,
        },
      },
    ],
  },
]

const modules = import.meta.glob('./modules/**', { eager: true })

Object.keys(modules).forEach((key) => {
  const item: any = modules[key]
  if (Array.isArray(item.default))
    routes.push(...item.default)

  else
    routes.push(item.default)
})

const router = createRouter({
  history: createWebHashHistory('/teacher/'),
  routes: [
    ...routes,
    {
      path: '/:catchAll(.*)',
      component: () => import('@/views/error/404.vue'),
      meta: {
        public: true,
      },
    },
  ],
})

export async function setupRouter(app: any) {
  setupPermissions(router)
  app.use(router)
  await router.isReady()
}

export default router
