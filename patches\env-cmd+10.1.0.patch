diff --git a/node_modules/env-cmd/dist/parse-rc-file.js b/node_modules/env-cmd/dist/parse-rc-file.js
index 5e7b46b..f3df690 100644
--- a/node_modules/env-cmd/dist/parse-rc-file.js
+++ b/node_modules/env-cmd/dist/parse-rc-file.js
@@ -23,7 +23,7 @@ async function getRCFileVars({ environments, filePath }) {
     const ext = path_1.extname(absolutePath).toLowerCase();
     let parsedData;
     try {
-        if (ext === '.json' || ext === '.js') {
+        if (ext === '.json' || ext === '.js' || ext === '.cjs') {
             const possiblePromise = require(absolutePath); /* eslint-disable-line */
             parsedData = utils_1.isPromise(possiblePromise) ? await possiblePromise : possiblePromise;
         }