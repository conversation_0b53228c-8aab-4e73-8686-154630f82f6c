<script setup lang="ts" name="ChapterReportDetail">
import { getModuleQuestionList } from '@/api/activity'
import Search from '../components/Search.vue'

let route = useRoute()
let router = useRouter()
let title = $ref<any>('')

let bookList = $ref<any>([])
let bookLoading = $ref<boolean>(true)
let searchData = $ref<any>({})
title = route.query?.title ?? ''
let formatTime = (seconds: number = 0) => {
  if (seconds === 0) return '0秒'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  const res: Array<string> = []
  if (hours) res.push(`${hours}小时`)
  if (minutes) res.push(`${minutes}分`)
  if (secs) res.push(`${secs}秒`)
  return res.join('')
}
async function getList(obj) {
  try {
    bookLoading = true
    searchData = obj
    let {
      schoolId,
      gradeId,
      classId,
      dateRange,
    } = obj
    let params: any = {
      activityThemeModuleId: route.query?.activityThemeModuleId ?? '',
      schoolId,
      sysGradeId: gradeId == 'all' ? null : gradeId,
      schoolClassId: classId == 'all' ? null : classId,
    }
    if (dateRange?.[0] && dateRange?.[1]) {
      params = {
        ...params,
        beginDateTime: dateRange?.[0] ?? '',
        endDateTime: dateRange?.[1] ?? '',
      }
    }

    let res = await getModuleQuestionList(params)
    bookList = res ?? []
    bookLoading = false
    nextTick($g.tool.renderMathjax)
  }
  catch (err) {
    bookLoading = false
    console.log('err => ', err)
  }
}
function goReportDetail(obj, flag = false) {
  let query: any = {
    schoolId: searchData?.schoolId ?? '',
    exerciseSourceType: 1,
    exerciseSourceId: obj?.activityThemeModuleResourceId ?? '',
    title: obj?.originalName || obj?.nameAlias,
    gradeId: searchData?.gradeId == 'all' ? 0 : searchData?.gradeId,
    classId: searchData?.classId == 'all' ? 0 : searchData?.classId,
    startTime: searchData?.dateRange?.[0] ?? '',
    endTime: searchData?.dateRange?.[1] ?? '',
  }
  // 错误报告
  if (flag) {
    query = {
      ...query,
      activeName: 3,
    }
  }

  router.push({
    name: 'QuestionReport',
    query,
  })
}
</script>

<template>
  <div class="px-26px pt-26px">
    <g-navbar :title="title" class="mb-17px"></g-navbar>
    <div class="h-[calc(100vh-26px-34px-17px)]  rounded-[6px]  overflow-auto no-bar">
      <Search @get-list="getList" />
      <div class="my-[17px] bg-white p-[17px] rounded-[6px]">
        <g-loading v-if="bookLoading" class="h-200px"></g-loading>
        <div v-else>
          <template v-for="item in bookList" :key="item.bookId">
            <div class="mb-16px mt-10px font-600">
              <g-mathjax
                class="text-16px text-center mb-[13px]"
                :text="item.nameAlias || item.originalName"
              ></g-mathjax>
              <el-tree
                :render-after-expand="false"
                :data="item.bookCatalogStatisticsList"
                node-key="bookCatalogId"
                :props="{ label: 'nameAlias' }"
                default-expand-all
                :expand-on-click-node="false"
              >
                <template #default="{ data }">
                  <div class="w-full">
                    <g-mathjax
                      class="!text-15px font-[600] text-[#333333] leading-[21px] label mb-[13px]"
                      :text="data?.nameAlias ?? data?.originalName"
                    ></g-mathjax>
                    <div>
                      <div v-for="dataItem in data.resourceStatisticsList" :key="dataItem.activityThemeModuleResourceId">
                        <!-- <div>{{ dataItem.name }}</div> -->
                        <g-mathjax
                          class="!text-15px font-[600] text-[#333333] leading-[21px] label mb-[13px]"
                          :text="dataItem.name"
                        ></g-mathjax>
                        <div class="border border-[#DCDFE6] rounded-[4px] min-h-[123px] px-[16px] py-[13px] mb-[17px]">
                          <div class="flex items-center gap-[13px] mb-[17px]">
                            <div class="w-[47px] h-[29px] text-center leading-[29px] bg-[#F3F4F9] !font-[400] rounded-[4px] border border-[rgba(100,116,253,0.14)] text-[13px] text-[#6474FD] ">
                              测验
                            </div>
                            <g-mathjax
                              class="!text-15px font-[600] text-[#333333] leading-[21px] label "
                              :text="dataItem?.nameAlias ?? dataItem?.originalName"
                            ></g-mathjax>
                          </div>
                          <div style="border-top: 1px dashed #CCCCCC;"></div>
                          <div class="flex justify-between items-center  mt-[17px]">
                            <div class="flex  gap-[30px] !text-[#999] !text-[13px] leading-[18px] !font-[400]">
                              <div>平均进度(已作答学生统计)：{{ dataItem?.avgSchedule ?? 0 }}%</div>
                              <div>平均用时：{{ formatTime(dataItem?.avgTime) }}</div>
                              <div>平均答题数：{{ dataItem?.avgAnswerNum }}题</div>
                              <div>平均正确率：{{ dataItem?.avgCorrectRate ?? 0 }}%</div>
                            </div>
                            <div class=" flex gap-[17px]">
                              <el-button type="primary" @click="goReportDetail(dataItem)">
                                学生报告
                              </el-button>
                              <el-button
                                type="danger"
                                plain
                                class="!ml-0 border !border-[#FF4646]"
                                @click="goReportDetail(dataItem, true)"
                              >
                                错题重做报告
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__expand-icon {
    font-size: 0;
    background-image: url('@/assets/img/taskCenter/grayBottom.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;
    width: 16px;
    height: 16px;
    transform: none;
    position: relative;
    top: -1px;
    align-self: flex-start;
    transform: rotate(-90deg);
    transition: all 0.2s;
    margin-top: -2px;
  }

  /* 展开状态的图标 */
  .el-tree-node__expand-icon.expanded {
    transform: none;
    background-image: url('@/assets/img/taskCenter/grayBottom.png');
    position: relative;

    top: -2px;
  }

  .el-tree-node__content {
    height: auto;
    cursor: auto;
    align-items: flex-start;
    justify-content: flex-start;
  }

  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: transparent;
  }

  /* 移除之前的所有 hover 效果 */
  .el-tree-node__content:hover,
  .el-tree-node:hover,
  .el-tree-node.is-hover,
  .el-tree-node.hover-visible,
  .el-tree-node:not(.is-disabled):focus > .el-tree-node__content {
    background-color: transparent !important;
  }

  .el-tree-node > .el-tree-node__children {
    overflow: visible;
  }
}
/* 添加树形容器的样式 */
.label {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow-wrap: break-word;
}
</style>
