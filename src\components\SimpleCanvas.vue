<script setup lang="ts">
import { Canvas } from 'fabric'
import { onMounted, ref } from 'vue'

const canvas = ref<HTMLCanvasElement | null>(null)
let fabricCanvas: Canvas | null = null

onMounted(() => {
  if (!canvas.value) return

  // 初始化 Fabric.js 画布
  fabricCanvas = new Canvas(canvas.value, {
    width: 800,
    height: 600,
    backgroundColor: '#ffffff',
  })

  // 启用自由绘制模式
  if (fabricCanvas) {
    fabricCanvas.isDrawingMode = true

    // 设置画笔样式
    if (fabricCanvas.freeDrawingBrush) {
      fabricCanvas.freeDrawingBrush.width = 2
      fabricCanvas.freeDrawingBrush.color = '#000000'
    }
  }
})
</script>

<template>
  <div class="canvas-container">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<style scoped>
.canvas-container {
  border: 1px solid #ccc;
  margin: 20px;
}

canvas {
  border: 1px solid #ddd;
}
</style>
