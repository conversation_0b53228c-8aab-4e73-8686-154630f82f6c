<script lang="ts" setup>
// import lottie from 'lottie-web'
import lottie from 'lottie-web/build/player/lottie_light.min'

const props = defineProps({
  options: {
    type: Object,
  },
  height: {
    type: Number,
  },
  width: {
    type: Number,
  },
})

const emit = defineEmits(['animCreated'])

const style = computed(() => {
  return {
    width: props.width ? `${props.width}px` : '100%',
    height: props.height ? `${props.height}px` : '100%',
    overflow: 'hidden',
    margin: '0 auto',
  }
})

let anim
const id = $g._.uniqueId('id_')

onMounted(async () => {
  await nextTick()
  initLottie()
})

function initLottie() {
  if (!lottie)
    return

  anim = lottie.loadAnimation({
    container: document.querySelector(`#${id}`) as any,
    renderer: 'svg',
    ...props.options,
  })
  emit('animCreated', anim)
}
</script>

<template>
  <div :id="id"
       ref="lavContainer"
       class="lottie"
       :style="style"
  />
</template>
