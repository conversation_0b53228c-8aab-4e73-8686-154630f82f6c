<script setup lang="ts">
import { getErrorExamQuestionDetailApi } from '@/api/taskCenter'

const props = defineProps<{
  choiceQuestion: boolean
  currentOption: any
  params: any
  structureNumber: any
  currentSubIndex: number
}>()
const showDialog = defineModel<boolean>('showDialog', { required: true })
let currentStudent = $ref<any>(null)
let studentAnswerList: any = $ref([])
const contentRef = $ref<any>(null)
const choiceRef = $ref<any>(null)

// 当前小题答案
const currentSubQuestionAnswer = $computed(() => {
  return studentAnswerList?.[props.currentSubIndex] || null
})

function studentClick(student: any) {
  currentStudent = student
  getStudentAnswer()
}

async function getStudentAnswer() {
  const data = await getErrorExamQuestionDetailApi({
    ...props.params,
    schoolStudentId: currentStudent.schoolStudentId,
  })
  studentAnswerList = data.subQuestionResultList || []
}

function previewImg(urls, index) {
  $g.flutter('previewImage', {
    urls,
    index,
  })
}

function open() {
  nextTick(() => {
    if (!props.choiceQuestion && contentRef) contentRef.scrollTop = 0
    if (props.choiceQuestion && choiceRef) choiceRef.scrollTop = 0
  })
  if (props.choiceQuestion || !props.currentOption.students?.length) return
  currentStudent = props.currentOption.students[0]
  getStudentAnswer()
}

// 判断是否需要显示tooltip
function isOpenTip(data, id) {
  if ('openTip' in data) return
  const dom = document.querySelector(`#${id}`)
  const open = dom ? dom.scrollWidth <= dom.clientWidth : true
  data.openTip = open
}
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    position="right"
    class="w-[600px] h-full bg-[#F3F4F9]"
    @open="open"
  >
    <div class="p-20px h-full">
      <div class="font-600 text-17px mb-21px flex justify-between items-center">
        <span>题号：{{ structureNumber }}</span>
        <img
          :src="$g.tool.getFileUrl('taskCenter/close.png')"
          alt=""
          class="h-15px w-15px van-haptics-feedback"
          @click="showDialog = false"
        >
      </div>
      <div ref="choiceRef" class="h-[calc(100%-47px)] overflow-auto">
        <template v-if="choiceQuestion">
          <div class="bg-[white] br-[6px] p-17px mb-17px">
            <div>
              <span class="font-600">选项{{ currentOption.title }}</span>
              <span class="text-14px ml-11px">学生名单（{{ currentOption.students?.length || 0 }}人，占比{{ $g.math(currentOption.rate).multiply(100).value() || 0 }}%）</span>
            </div>
            <div class="grid grid-cols-4 gap-13px bg-[#F3F4F9] br-[6px] p-13px mt-17px">
              <div v-for="student in currentOption.students"
                   :key="student.schoolStudentId"
                   class="van-ellipsis text-center"
              >
                <div class="w-full van-ellipsis">
                  {{ student.studentName }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div ref="contentRef" class="bg-[white] br-[6px] p-17px h-full overflow-auto">
            <div>
              <span class="font-600">分数段{{ currentOption.title }}</span>
              <span class="text-14px ml-11px">（{{ currentOption.students?.length || 0 }}人，占比{{ $g.math(currentOption.rate).multiply(100).value() || 0 }}%）</span>
            </div>
            <template v-if="currentOption.students?.length > 0">
              <div class="grid grid-cols-4 gap-13px bg-[#F3F4F9] br-[6px] p-13px mt-17px">
                <div
                  v-for="student in currentOption.students"
                  :key="student.schoolStudentId"
                  class="h-30px px-9px flex-cc text-center bg-[#F3F4F9] border border-[#DADDE8] text-[#6C6C74] br-[7px] text-13px cursor-pointer"
                  :class="{ '!bg-[#F3F4F9] !border-[#6474FD] !text-[#6474FD]': student.schoolStudentId == currentStudent?.schoolStudentId }"
                  @click="studentClick(student)"
                  @mouseover="isOpenTip(student, `id${student.schoolStudentId}`)"
                >
                  <el-tooltip
                    effect="dark"
                    :content="`${student.studentName} ${student.score}分`"
                    placement="top"
                    :disabled="student.openTip"
                    :hide-after="10"
                  >
                    <div :id="`id${student.schoolStudentId}`" class="w-full van-ellipsis">
                      {{ `${student.studentName} ${student.score}` }}分
                    </div>
                  </el-tooltip>
                </div>
              </div>
              <div v-if="$g.tool.isTrue(currentSubQuestionAnswer)" class="border border-[#DADDE8] br-[6px] mt-17px p-10px">
                <div class="text-15px text-[#6C6C74] flex-1 flex items-center flex-wrap">
                  <div v-if="currentSubQuestionAnswer?.answerType === 1 || currentSubQuestionAnswer?.answerType === 3" class="text-15px text-[#6C6C74] mt-13px">
                    <g-mathjax :text="currentSubQuestionAnswer?.answer || currentSubQuestionAnswer?.keyboard || '未作答'" />
                  </div>
                  <div v-else-if="currentSubQuestionAnswer?.answerType === 2" class="mt-13px">
                    <div
                      v-for="(boardItem, boardIndex) in currentSubQuestionAnswer?.whiteBoard"
                      :key="boardIndex"
                      class=" rounded-[4px] overflow-hidden relative cursor-pointer"
                      :class="{
                        'mt-13px': boardIndex !== 0,
                      }"
                      @click="previewImg(currentSubQuestionAnswer?.whiteBoard, boardIndex)"
                    >
                      <img
                        src="@/assets/img/question/full.png"
                        alt="full"
                        class="absolute right-0 top-0 w-17px h-17px z-[2] pointer-events-none"
                      >
                      <img :src="boardItem"
                           alt="img cover"
                           class="w-full h-full object-cover"
                      >
                    </div>
                    <div v-if="!currentSubQuestionAnswer?.whiteBoard?.length" class="text-15px text-[#ddd]">
                      没有答题数据
                    </div>
                  </div>
                  <div v-else-if="currentSubQuestionAnswer?.answerType === 4" class="mt-13px">
                    <div
                      v-for="(imgItem, imgIndex) in currentSubQuestionAnswer?.image"
                      :key="imgIndex"
                      class="rounded-[4px] overflow-hidden relative cursor-pointer"
                      :class="{
                        'mt-13px': imgIndex !== 0,
                      }"
                      @click="previewImg(currentSubQuestionAnswer?.image, imgIndex)"
                    >
                      <img
                        src="@/assets/img/question/full.png"
                        alt="full"
                        class="absolute right-0 top-0 w-17px h-17px z-[2] pointer-events-none"
                      >
                      <img :src="imgItem"
                           alt="img cover"
                           class="w-full h-full object-cover"
                      >
                    </div>
                    <div v-if="!currentSubQuestionAnswer?.image?.length" class="text-15px text-[#ddd]">
                      没有答题数据
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <g-empty v-else></g-empty>
          </div>
        </template>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>

</style>
