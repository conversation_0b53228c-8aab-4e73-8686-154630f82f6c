<script setup lang="ts">
const props = defineProps({
  formOption: {
    type: Object,
    default: () => {},
  },
})

const emit = defineEmits(['change'])
const filterItem = $computed(() => {
  let itemNum = 0
  for (let i in props.formOption.items) 
    if (!props.formOption.items[i].isHidden) { itemNum++ }


  return itemNum
})
</script>

<template>
  <div>
    <div
      v-for="(key, index) in Object.keys(props.formOption.items)"
      v-show="!props.formOption.items[key].isHidden"
      :key="index"
      class="flex items-center min-h-30px"
      :class="[props.formOption.items[key].className, index < filterItem - 1 ? 'mb-17px' : '']"
    >
      <div class="w-70px font-600 text-right text-[#333] text-15px flex-shrink-0 mr-21px">
        {{ props.formOption.items[key].label }}
      </div>
      <template v-if="props.formOption.items[key].slot">
        <slot :name="key"></slot>
      </template>
      <g-radio
        v-else
        v-model="formOption.data[key]"
        :option="props.formOption.items[key].option"
        item-class="px-10px py-7px text-15px text-[#333] !font-400"
        active-item-class="bg-[#ECEFFF] text-[#5864F8]"
        @change="emit('change')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
