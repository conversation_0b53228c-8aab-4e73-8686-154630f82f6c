import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_PAD_API, VITE_APP_BASE_API, VITE_JZT_API } = config

/* --------------- 教学工具开始 --------------- */

/* 年级列表 */
export function getGradeList(data) {
  return request.get(`${VITE_PAD_API}/pad/teacher/module/grade`, data, { delay: false })
}
/* 班级列表 */
export function getClassList(data) {
  return request.get(`${VITE_PAD_API}/pad/teacher/module/grade/teacher/class`, data, { delay: false })
}

/* 学生列表 */
export function getStudentList(data) {
  return request.get(`${VITE_PAD_API}/pad/admin/student-online/studentOnLineList`, data, { delay: false })
}

/* 获取班级锁屏状态 */
export function getStatus(data) {
  return request.get(`${VITE_PAD_API}/pad/teacher/screenshot/class/lock/status`, data, { delay: false })
}

/* 检查是否可以锁屏 */
export function checkPad(data) {
  return request.get(`${VITE_PAD_API}/pad/teacher/screenshot/lock/check`, data, { delay: false })
}

/* 锁屏 */
export function lockScreen(data) {
  return request.post(`${VITE_PAD_API}/pad/teacher/screenshot/lock`, data, { delay: false })
}

/* 解除锁屏 */
export function unLockScreen(data) {
  return request.post(`${VITE_PAD_API}/pad/teacher/screenshot/unlock`, data, { delay: false })
}

/* 下线 */
export function kickOff(data) {
  return request.post(`${VITE_PAD_API}/pad/admin/student-online/kick-off`, data, { delay: false })
}

/* 重置密码 */
export function resetPassword(data) {
  return request.post(`${VITE_PAD_API}/pad/admin/password/resetStudentPassword`, data, { delay: false })
}

/* --------------- 教学工具结束 --------------- */

/* --------------- 平板监控开始 --------------- */

/* 学生统计列表 */
export function getStatisticsList(data) {
  return request.get(`${VITE_PAD_API}/pad/teacher/module/student/statisticsList`, data, { delay: false })
}

/* 获取上课班级信息 */
export function getAttendInfo(data?) {
  return request.get(`${VITE_PAD_API}/pad/teacher/screenshot/mqtt/info`, data, { delay: false })
}

/* 开始截屏 */
export function getScreenshot(data) {
  return request.post(`${VITE_PAD_API}/pad/teacher/screenshot/begin`, data, { delay: false, headers: { Platform: 'TEACHER_PC' } })
}

/* --------------- 平板监控结束 --------------- */

/* --------------- 模块开关开始 --------------- */

/* 学段信息 */
export function getStageInfo() {
  return request.get(`${VITE_APP_BASE_API}/v3/teacher/info/periodLevels`)
}
/* 年级信息 */
export function getGradeInfo(data) {
  return request.get(`${VITE_APP_BASE_API}/v3/teacher/info/grades`, data, { delay: false })
}
/* 班级信息 */
export function getClassInfo(data) {
  return request.get(`${VITE_APP_BASE_API}/v3/teacher/info/classList`, data, { delay: false })
}
/* 模块列表 */
export function getModuleList(data) {
  return request.get(`${VITE_PAD_API}/pad/teacher/module/useSwitch`, data, { delay: false })
}
/* 保存学校模块 班级 */
export function saveSchoolModuleClass(data) {
  return request.post(`${VITE_PAD_API}/pad/teacher/module/saveUseSwitchClass`, data, { delay: false })
}
/* 同步数据 */
export function syncData(data) {
  return request.post(`${VITE_PAD_API}/pad/teacher/module/syncModuleClassData`, data, { delay: false })
}

/* --------------- 模块开关结束 --------------- */
