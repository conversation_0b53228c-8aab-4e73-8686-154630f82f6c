<script setup lang="ts">
import Count from './Count.vue'
import FullScreen from './FullScreen.vue'
import MouseAction from './MouseAction.vue'
import Scale from './Scale.vue'

const props = defineProps({
  mindMap: {
    type: Object,
  },
  isZenMode: {
    type: Boolean,
  },
})
const emit = defineEmits(['changeShowSearch'])
function showSearch() {
  emit('changeShowSearch')
}
</script>

<template>
  <div
    class="navigatorContainer w-full flex justify-between"
    style="pointer-events: none"
  >
    <Count :mind-map="mindMap" style="pointer-events: auto"></Count>
    <div class="flex items-center rightBar h-full px-10px">
      <!-- <div class="item">
        <div class="btn iconfont iconsousuo" @click="showSearch"></div>
      </div> -->
      <!-- <div class="item">
        <MouseAction :mindMap="mindMap"></MouseAction>
      </div> -->
      <!-- <div class="item">
        <FullScreen :mindMap="mindMap" :isZenMode="isZenMode"></FullScreen>
      </div> -->
      <div class="item" style="pointer-events: auto">
        <Scale :mind-map="mindMap"></Scale>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navigatorContainer {
  z-index: 1;
  padding: 0 12px;
  position: absolute;
  bottom: 20px;
  border-radius: 5px;
  opacity: 0.8;
  height: 44px;
  font-size: 12px;
  display: flex;
  align-items: center;
  .item {
    margin-right: 20px;

    &:last-of-type {
      margin-right: 0;
    }
    .btn {
      cursor: pointer;
      font-size: 18px;
    }
  }
  .rightBar {
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(255, 255, 255, 0.6);
  }
}
</style>
