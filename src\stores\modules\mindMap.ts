import { defineStore } from 'pinia'
import exampleData from 'simple-mind-map/example/exampleData'

export const useMindMapStore = defineStore('mindMap', {
  state: () => ({
    mindMapData: {
      root: {
        data: {
          text: '根节点',
        },
        children: [
          {
            data: {
              text: '二级节点',
            },
            children: [
              {
                data: {
                  text: '分支主题',
                },
                children: [],
              },
              {
                data: {
                  text: '分支主题',
                },
                children: [],
              },
            ],
          },
        ],
      },
      theme: {
        template: 'seaBlueLine',
        config: {},
      },
      layout: 'logicalStructure',
      config: {},
    }, // 思维导图数据
    isHandleLocalFile: false, // 是否操作的是本地文件
    localConfig: {
      // 本地配置
      isZenMode: false, // 是否是禅模式
      openNodeRichText: true, // 是否开启节点富文本
      useLeftKeySelectionRightKeyDrag: false, // 鼠标行为
      isShowScrollbar: false, // 是否显示滚动条
    },
    activeSidebar: '', // 当前显示的侧边栏
    isOutlineEdit: false, // 是否是大纲编辑模式
    isReadonly: false, // 是否只读,
    fullScreen: false, // 是否全屏
  }),

  // Actions 相当于 Vuex 的 actions 和 mutations 的结合体
  actions: {
    /**
     * @Desc: 设置思维导图数据
     */
    setMindMapData(data) {
      this.mindMapData = data
    },
    /**
     * @Desc: 设置操作本地文件标志位
     */
    setIsHandleLocalFile(data) {
      this.isHandleLocalFile = data
    },
    /**
     * @Desc: 设置本地配置
     */
    setLocalConfig(data) {
      this.localConfig = {
        ...this.localConfig,
        ...data,
      }
    },
    /**
     * @Desc: 设置当前显示的侧边栏
     */
    setActiveSidebar(data) {
      this.activeSidebar = data
    },
    /**
     * @Desc: 设置大纲编辑模式
     */
    setIsOutlineEdit(data) {
      this.isOutlineEdit = data
    },
    // 设置是否只读
    setIsReadonly(data) {
      this.isReadonly = data
    },

    /**
     * @Desc: 设置初始思维导图数据
     */
    async getUserMindMapData() {
      try {
        const { data } = {
          data: {
            data: {
              mindMapData: exampleData,
            },
          },
        }
        this.setMindMapData(data.data)
      }
      catch (error) {
        console.error(error)
      }
    },
    /**
     * @Desc: 重置所有状态数据到初始值
     */
    resetState() {
      this.$reset()
    },
  },
})
