<script setup lang="ts">
import { getTaskStudentExerciseList } from '@/api/aiTask'
import { getQuestionListDetailApi } from '@/api/taskCenter'
import { useUserStore } from '@/stores/modules/user'
import QuestionStemItem from '@/views/teacher/task/questionReport/components/QuestionStemItem.vue'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const props = defineProps({
  currentCategoryId: {
    type: Number,
    default: 0,
  },
  catalogCourseType: {
    type: String,
    default: '',
  },
})
const userStore = useUserStore()
let showLoading = $ref(true)
const router = useRouter()
const route = useRoute()
let questionList = $ref<any[]>([])
let totalCount = $ref<any>(0)
let gxErrorCount = $ref<any>(0)

// 获取题目列表
async function getQuestionList(isLoading = true) {
  try {
    if (props.currentCategoryId === null) return
    if (isLoading)
      showLoading = true

    const res = await getTaskStudentExerciseList(
      {
        taskId: route.query.taskId,
        bookCatalogId: props.currentCategoryId || null,
        catalogCourseType: props.catalogCourseType,
        schoolId: userStore.userInfo.schoolId,
      },
    )
    questionList = await getQuestionListDetail(res)
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
  finally {
    showLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
}

// 获取题目详情
async function getQuestionListDetail(data) {
  if (!data.paperReport.questionList.length) return
  const list = data.paperReport.questionList
  gxErrorCount = 0
  let res = await getQuestionListDetailApi({
    questionIdList: list.map(v => v.questionId),
  })
  totalCount = list.length
  list.forEach((item, index) => {
    // 共性错题，错题人数大于1
    if (item.errorNum > 1) gxErrorCount++
    let find = res?.find(v => v.questionId == item.questionId) || {}
    Object.assign(item, {
      ...find,
      questionIndex: index + 1,
      isObjective: find.subQuestions.every(h => [1, 2, 3].includes(h.subQuestionType)),
    })
  })
  return list
}

function goDetail(item: any) {
  router.push({
    name: 'QuestionDetail',
    query: {
      taskId: route.query.taskId,
      currentCategoryId: props.currentCategoryId,
      catalogCourseType: props.catalogCourseType,
      questionId: item?.questionId ?? '',
      schoolId: userStore.userInfo.schoolId,
    },
  })
}

onMounted(() => {
  getQuestionList()
})

watch(() => props.currentCategoryId, () => {
  getQuestionList()
})

onActivated(() => {
  getQuestionList(false)
})
</script>

<template>
  <div class="h-full bg-[white] br-[6px] p-17px">
    <span class="font-600 mb-6px">课程概况</span>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <template v-else>
      <template v-if="questionList?.length">
        <div class="text-14px text-[#666] mb-16px">
          共{{ totalCount }}道题，{{ gxErrorCount }}道共性题（已按作答人数排序）
        </div>
        <DynamicScroller
          ref="scrollerRef"
          :items="questionList"
          :min-item-size="300"
          :buffer="500"
          key-field="questionId"
          class="h-[calc(100%_-_37px_-_23px)]"
        >
          <template #default="{ item, index, active }">
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :size-dependencies="['questionTitle']"
              :data-index="index"
              class="pb-1px"
            >
              <QuestionStemItem
                :key="item.questionId"
                :question-item="item"
                :index="item.questionIndex"
                class="border border-[#e3e3e3]"
              >
                <template #footer>
                  <div class="mt-12px flex justify-between ml-16px">
                    <div class="flex items-center text-15px text-[#666666] font-400">
                      <el-popover
                        trigger="hover"
                        :width="200"
                        append-to="#app"
                        :disabled="!item.answerNum"
                      >
                        <template #reference>
                          <div :class="item.answerNum && 'cursor-pointer'">
                            {{ item.answerNum }}人作答
                          </div>
                        </template>
                        <div class="pb-15px max-h-200px overflow-y-auto">
                          <div class="flex items-center mb-8px">
                            <img
                              src="@/assets/img/taskCenter/pen.png"
                              alt="pen icon"
                              class="w-16px h-16px mr-6px"
                            />
                            <div class="text-14px text-[#333]">
                              作答
                            </div>
                          </div>
                          <div
                            class="flex flex-wrap gap-10px"
                          >
                            <template
                              v-if="item.answerStudentList?.length"
                            >
                              <div
                                v-for="(finishItem, finishIdx) in item.answerStudentList"
                                :key="finishIdx"
                                class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                              >
                                {{ finishItem.studentName }}
                              </div>
                            </template>
                            <span v-else class="text-[#999]">无学生</span>
                          </div>
                        </div>
                      </el-popover>
                      <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                      <el-popover
                        trigger="hover"
                        append-to="#app"
                        :width="200"
                        :disabled="!item.errorNum"
                      >
                        <template #reference>
                          <div :class="item.errorNum && 'cursor-pointer'">
                            {{ item.errorNum }}人错误
                          </div>
                        </template>
                        <div class="pb-20px max-h-200px overflow-y-auto">
                          <div class="w-full flex items-center mb-8px">
                            <div class="flex items-center w-246px">
                              <img
                                src="@/assets/img/taskCenter/cross.png"
                                alt="cross icon"
                                class="w-16px h-16px mr-6px"
                              />
                              <div class="text-14px text-[#333]">
                                错误
                              </div>
                            </div>
                          </div>
                          <div
                            class="flex flex-wrap gap-10px"
                          >
                            <template
                              v-if="item.errorStudentList?.length"
                            >
                              <div
                                v-for="(errorItem, errorIdx) in item
                                  .errorStudentList"
                                :key="errorIdx"
                                class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                              >
                                {{ errorItem.studentName }}
                              </div>
                            </template>
                            <span v-else class="text-[#999]">无学生</span>
                          </div>
                        </div>
                      </el-popover>
                      <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                      <div>
                        正确率：<span
                          v-if="typeof item.correctRate === 'number'"
                          :class="{
                            'text-[#F5222D]': 60 > item.correctRate,
                            'text-[#FAAD14]':
                              60 <= item.correctRate
                              && item.correctRate < 70,
                            'text-[#1EA0F0]':
                              70 <= item.correctRate
                              && item.correctRate < 85,
                            'text-[#52C41A]': 85 <= item.correctRate,
                          }"
                        >
                          {{ item.correctRate }}%
                        </span>
                        <span v-else>--</span>
                      </div>
                    </div>
                    <el-button class="w-64px h-30px bg-white text-[#6474FD] border border-[#6474FD]" @click="goDetail(item)">
                      查看
                    </el-button>
                  </div>
                </template>
              </QuestionStemItem>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </template>
      <g-empty v-else></g-empty>
    </template>
  </div>
</template>

<style lang="scss" scoped>

</style>
