import { useRouterStore } from '@/stores/modules/router'
import { useUserStore } from '@/stores/modules/user'
import { getToken } from '@/utils/token'
import axios from 'axios'

/**
 * @description 路由守卫
 */
// 是否显示初始loading
let isShowLoading = true
// 是否第一次进入项目页面
let isOncePage = true
export function setupPermissions(router) {
  router.beforeEach(async (to: any, from: any, next: any) => {
    try {
      versionCheck()
      const userStore = useUserStore()
      if (
        !to.meta.public &&
        (!userStore.token || from.path == '/') &&
        $g.isFlutter
      ) {
        if (localStorage.getItem('update'))
          localStorage.removeItem('update')

        else
          await userStore.getFlutterToken()
      }
      await handleZxsToken('beforeEach', to, userStore)
      removeLoading()
      restoreRollingBar(to, from, 1)
      keepAlive(to, from)
      needFullPage(to)
      next()
    }
    catch (error: any) {
      $g.showToast(error.message)
    }
  })
  router.afterEach(async (to: any, from) => {
    await handleZxsToken('afterEach', to)
    removeLoading()
    recordPageTime('进入路由')
    restoreRollingBar(to, from, 2)
    updatePageTitle(to)
    nextTick(() => {
      $g.flutter('setStatusBarColor', 'transparent')
    })
  })
  router.onError((error) => {
    console.error('🎃 error ==> ', error)
  })
}

/**
 * 描述 keepalive滚动条恢复
 * @param {any} to
 * @param {any} from
 */

function restoreRollingBar(to, from, mode = 1) {
  const routerStore = useRouterStore()
  const App = document.body
  if (!App)
    return

  if (mode == 1) {
    // 是否需记录滚动条位置
    if (from.meta.keepAlive || from.meta.keepAliveArr?.includes(to.name)) {
      routerStore.changeMenuMeta({
        name: from.name,
        meta: {
          scrollTop: App?.scrollTop || 0,
        },
      })
    }
  }
  else if (mode == 2) {
    nextTick(() => {
      if (to.meta.keepAlive && to.meta?.scrollTop)
        App.scrollTop = to.meta?.scrollTop

      else
        App.scrollTop = 0
    })
  }
}

async function versionCheck() {
  if (import.meta.env.VITE_APP_ENV.includes('development'))
    return false

  const response = await axios.get(
    `version.json?timestamp=${new Date().getTime()}`,
  )
  const vueVersion = response?.data?.version
  const localVueVersion = localStorage.getItem('vueVersion')
  localStorage.setItem('vueVersion', vueVersion)
  if ((localVueVersion || __APP_VERSION__) != vueVersion) {
    $g.showDialog({
      title: '更新提示',
      message: '发现新内容，为了更好的体验，请点击确认刷新当前页面',
      theme: 'round-button',
    }).then(() => {
      if ($g.isFlutter) localStorage.setItem('update', 'true')
      window.location.reload() // 例如，刷新页面
      $g.flutter('clearCache', { reload: true })
    })
  }
}

/* 指定页面缓存 */
function keepAlive(to: any, from: any) {
  const routerStore = useRouterStore()
  // 用于一些公用预览页面默认缓存当前页面，需要给当前页面添加name
  const defaultCacheRouteArr = ['PreviewDoc']
  routerStore.changeMenuMeta({
    name: from.name,
    meta: {
      keepAlive:
        from.meta.keepAliveArr?.includes(to.name) ||
        defaultCacheRouteArr.includes(to.name),
    },
  })
  routerStore.changeMenuMeta({
    name: to.name,
    meta: {
      keepAlive:
        to.meta.keepAliveArr?.includes(from.name) ||
        defaultCacheRouteArr.includes(from.name),
    },
  })
}

/* 关闭loading */
function removeLoading() {
  // 移除loading
  if (isShowLoading) {
    const appLoadingId = document.getElementById('appLoadingId') as HTMLElement
    appLoadingId?.remove()
    isShowLoading = false
    recordPageTime('关闭loading')
  }
}

function recordPageTime(title) {
  if (isOncePage) {
    const nowTime = new Date().getTime()
    console.log(`${title}耗时`, (nowTime - window._time) / 1000)
    if (title == '进入路由')
      isOncePage = false
  }
}

function needFullPage(to: any) {
  if (!to.meta.fullPage) {
    const body = document?.querySelector('body')
    body?.classList.add('full-page')
    const top: any = document?.querySelectorAll(
      '.full-page #TopAdaptationDistance',
    )[0]
    top && (top.style.display = 'block')
  }
  else {
    const body = document?.querySelector('body')
    body?.classList.remove('full-page')
  }
}

/**
 * 处理智习室携带参数兑换token的逻辑
 * @param mode - 路由钩子模式: 'beforeEach' 或 'afterEach'
 * @param to - 目标路由对象
 * @param userStore - 用户状态管理
 */
async function handleZxsToken(
  mode: 'beforeEach' | 'afterEach',
  to: any,
  userStore?: any,
): Promise<void> {
  // 如果跳转路径携带token，兑换token
  if (to.query.token) {
    if (mode == 'beforeEach') {
      userStore.token = to.query.token
      await userStore.getEncryptApi()

      userStore.currentRole = Number(to.query?.currentRole)
    }
    else if (mode == 'afterEach') {
      const newQuery = $g._.cloneDeep(to.query)
      // 删除 token
      delete newQuery.token
      delete newQuery.currentRole

      const path = to.path
      const queryString = new URLSearchParams(newQuery).toString()
      const newUrl = queryString ? `${path}?${queryString}` : path

      // 更新 URL
      history.replaceState(null, '', `#${newUrl}`)
    }
  }
}

/**
 * 更新页面标题
 * @param to - 目标路由对象
 */
function updatePageTitle(to: any): void {
  // 获取路由title，如果没有则使用默认值"教师用户端"
  const title = to.meta?.title || '教师用户端'
  // 更新浏览器标签页标题
  document.title = title
}
