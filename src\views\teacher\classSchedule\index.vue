<script lang="ts" setup name="ClassScheduleMain">
import {
  getTimetableClassList,
  getTimetableClassPage,
  getTimetableGradeList,
  getTimetableIsGraduate,
} from '@/api/aiTask'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const router = useRouter()

let currentGrade = $ref<any>(null)
let gradeList = $ref<any>([])
let currentClass = $ref<any>(null)
let classList = $ref<any>([])
let currentGraduate = $ref<any>(null)
let graduateList = $ref<any>([])
const tableOptions = reactive({
  ref: null as any,
  key: '',
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    {
      type: 'index',
      label: '序号',
    },
    {
      prop: 'schoolName',
      label: '学校',
    },
    {
      prop: 'sysGradeName',
      label: '年级',
    },
    {
      prop: 'className',
      label: '班级',
    },
    {
      prop: 'graduateYear',
      label: '毕业年份',
    },
    {
      prop: 'isGraduateName',
      label: '毕业状态',
    },
    {
      prop: 'phaseName',
      label: '学期',
    },
    {
      prop: 'cz',
      label: '操作',
      slot: true,
    },
  ],
  data: [],
})

const range = $computed(() => {
  return userStore.currentRole == 2 ? 2 : 1
})

async function initList() {
  try {
    tableOptions.loading = true
    const {
      list,
      total,
    } = await getTimetableClassPage({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
      range,
      sysGradeId: currentGrade,
      schoolClassId: currentClass,
      isGraduate: currentGraduate,
    })
    tableOptions.pageOptions.total = total
    tableOptions.data = list
    tableOptions.loading = false
  }
  catch (e) {
    tableOptions.data = []
    tableOptions.loading = false
  }
}

// 年级
async function getGradeList() {
  try {
    const res = await getTimetableGradeList({ range })
    gradeList = res || []
  }
  catch (e) {
    gradeList = []
  }
}

// 班级
async function getClassListApi(val) {
  try {
    const res = await getTimetableClassList({
      sysGradeId: val,
      range,
    })
    classList = res || []
  }
  catch (e) {
    classList = []
  }
}

// 毕业状态
function getGraduate() {
  getTimetableIsGraduate().then((res) => {
    graduateList = res || []
  }).catch((e) => {
      graduateList = []
    })
}

function toMgt(row) {
  router.push({
    name: 'ScheduleMgt',
    query: {
      schoolClassId: row.schoolClassId,
      className: row.sysGradeName + row.className,
    },
  })
}

async function changeGrade(val) {
  if (!$g.tool.isTrue(val)) {
    currentClass = ''
    classList = []
  }
  else {
    getClassListApi(val)
  }
  await initList()
}

onBeforeMount(() => {
  getGradeList()
  initList()
  getGraduate()
})
</script>

<template>
  <div class="h-screen p-26px" style="width: 100vw">
    <g-navbar title="班级课程表">
    </g-navbar>

    <div class="flex my-15px">
      <div class="flex items-center mx-26px">
        <div class="mr-11px">
          年级
        </div>
        <el-select
          v-model="currentGrade"
          placeholder="请选择年级"
          class="w-[200px]"
          clearable
          @change="changeGrade"
        >
          <el-option
            v-for="item in gradeList"
            :key="item.sysGradeId"
            :label="item.sysGradeName"
            :value="item.sysGradeId"
          />
        </el-select>
      </div>

      <div class="flex items-center mx-26px">
        <div class="mr-11px">
          班级
        </div>
        <el-select
          v-model="currentClass"
          placeholder="请选择班级"
          class="w-[200px]"
          clearable
          @change="initList"
        >
          <el-option
            v-for="item in classList"
            :key="item.schoolClassId"
            :label="item.className"
            :value="item.schoolClassId"
          />
        </el-select>
      </div>

      <div class="flex items-center ">
        <div class="mr-11px">
          毕业状态
        </div>
        <el-select
          v-model="currentGraduate"
          placeholder="请选择状态"
          class="w-[200px]"
          clearable
          @change="initList"
        >
          <el-option
            v-for="item in graduateList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </div>
    </div>
    <g-table :table-options="tableOptions" @change-page="initList">
      <template #cz="{ row }">
        <el-button
          text
          type="primary"
          @click="toMgt(row)"
        >
          课表管理
        </el-button>
      </template>
    </g-table>
  </div>
</template>
