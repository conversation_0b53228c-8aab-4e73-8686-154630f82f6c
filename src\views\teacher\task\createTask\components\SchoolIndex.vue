<script setup lang="ts">
import { getBookList<PERSON>pi, getGradesApi, getSchoolsApi } from '@/api/taskCenter'

let gradeList = $ref<any>([])
let schoolList = $ref<any>([])
let currentGrade = $ref<any>(null)
let currentSchool = $ref<any>(null)
let loading = $ref<any>(true)
const route = useRoute()
let currentName = $computed(() => {
  return schoolList?.find(item => item?.schoolId == currentSchool)?.schoolName
})
let bookList = $ref<any>([])
function onGradeSelect(item) {
  currentGrade = item?.sysGradeId
}
let boxRef: any = $ref(null)
let bs: any = $ref(null)
async function getSchools() {
  if (!route?.query?.subjectId) {
    loading = false
    return
  }
  try {
    const res = await getSchoolsApi({ sysSubjectId: route?.query?.subjectId })
    schoolList = res || []
    currentSchool = schoolList?.find(item => item?.defaultSelected)?.schoolId
    if (!currentSchool)
      loading = false
  }
  catch (err) {}
}
const pageOption = $ref({
  page: 1,
  page_size: 50,
  total: 0,
})
async function getBookList() {
  try {
    const res = await getBookListApi({
      sysSubjectId: route?.query?.subjectId,
      sysGradeId: currentGrade,
      schoolId: currentSchool,
      page: pageOption?.page,
      pageSize: pageOption?.page_size,
    })
    bookList = [...bookList, ...res?.list]
    pageOption.total = res?.total || 0
    loading = false
    nextTick($g.tool.renderMathjax)
  }
  catch (err) {
    loading = false
  }
}
async function pulldown() {
  pageOption.page = 1
  bookList = []
  loading = true
  getBookList()
}
async function pullup() {
  pageOption.page += 1
  getBookList()
}
watch(
  () => currentGrade,
  () => {
    bookList = []
    pageOption.page = 1
    if (currentGrade != null) {
      loading = true
      getBookList()
    }
  },
)
const setCurrentSchool = inject('setCurrentSchool') as any
watch(
  () => currentSchool,
  () => {
    gradeList = []
    currentGrade = null
    if (currentSchool != null) {
      loading = true
      getGrades()
      setCurrentSchool(currentSchool)
    }
  },
)
onMounted(() => {
  getSchools()
})
const setStepVal = inject('setStepVal') as any
const setCurrentBook = inject('setCurrentBook') as any
function onDetailClick(book) {
  setCurrentBook(book)
  setStepVal(4)
}
async function getGrades() {
  try {
    const res = await getGradesApi({
      schoolId: currentSchool,
      sysSubjectId: route?.query?.subjectId,
    })
    gradeList = res || []
    currentGrade = gradeList?.length ? gradeList[0]?.sysGradeId : null
    if (!currentGrade)
      loading = false
  }
  catch (err) {}
}
</script>

<template>
  <div
    :style="{
      height: $g.isPC ? 'calc(100vh - 264px)' : 'calc(100vh - 9vh - 156px)',
    }"
    class="bg-white px-[21px] py-[17px] w-full br-[13px]"
  >
    <div class="flex items-center justify-between mb-[13px]">
      <div
        v-if="gradeList?.length"
        ref="boxRef"
        class="selcet-none cursor-pointer overflow-hidden border-[1px] border-[#DADDE8] bg-[#FBFBFB] br-[6px]"
      >
        <div class="flex px-[5px] py-[4px]">
          <div
            v-for="(item, index) in gradeList"
            :key="index"
            :class="{
              'bg-[#E9ECF5]  br-[4px] text-[#6C6C74]':
                item?.sysGradeId == currentGrade,
            }"
            class="min-w-[57px] flex-shrink-0 px-[14px] py-[3px] flex-cc cursor-pointer text-[15px]"
            @click="onGradeSelect(item)"
          >
            {{ item?.sysGradeName }}
          </div>
        </div>
      </div>
      <div
        v-else
        class="w-[333px] h-[38px] border-[1px] border-[#ffffff]"
      ></div>
      <div class="relative min-h-[34px]">
        <el-select-v2
          v-model="currentSchool"
          placeholder="请选择学校"
          size="large"
          :style="{ width: '128px', opacity: 0, position: 'absolute' }"
          :options="schoolList"
          :props="{
            value: 'schoolId',
            label: 'schoolName',
          }"
        >
        </el-select-v2>
        <div
          v-if="currentName"
          class="w-[128px] justify-between cursor-pointer flex items-center border text-[15px] text-[#6C6C74] border-[#DADDE8] br-[5px] bg-[#FBFBFB] px-[8px] py-[4px]"
        >
          <span class="truncate">{{ currentName }}</span>
          <svg-common-expand class="w-[17px] ml-[2px] h-[17px]" />
        </div>
      </div>
    </div>
    <div
      class="overflow-y-auto"
      :style="{
        height: $g.isPC
          ? 'calc(100vh - 264px - 69px)'
          : 'calc(100vh - 9vh - 240px)',
      }"
    >
      <g-list
        ref="scrollRef"
        v-model:data="bookList"
        :show-loading="loading"
        :page-option="pageOption"
        url="/tutoring/admin/task/book/list"
        @pulldown="pulldown"
        @pullup="pullup"
      >
        <g-loading v-if="loading" class="h-[200px]"></g-loading>
        <div v-else>
          <div
            v-if="bookList?.length"
            :class="!$g.isPC ? 'grid-cols-3' : 'grid-cols-5'"
            class="grid gap-x-[16px] gap-y-[16px]"
          >
            <div
              v-for="(item, index) in bookList"
              :key="index"
              class="bookBg cursor-pointer mb-[16px] py-[12px] px-[16px] br-[6px] w-full min-h-[92px]"
              @click="onDetailClick(item)"
            >
              <div
                class=" mb-[4px] h-[43px]"
              >
                <g-mathjax :text="item?.bookName" class="text-[#333333] line-2 h-[43px] text-[15px] font-600"></g-mathjax>
              </div>
              <div class="text-[#6C6C74] text-[14px] flex items-center">
                <div>{{ item?.sysGradeName }}</div>
                <div
                  v-if="item?.sysGradeName"
                  class="bg-[#6C6C74] w-[1px] h-[10px] mx-[12px]"
                ></div>
                <div>{{ item?.sysSubjectName }}</div>
                <div
                  v-if="item?.schoolList?.length"
                  class="bg-[#6C6C74] w-[1px] h-[10px] mx-[12px]"
                ></div>
                <div v-if="item?.schoolList?.length > 1">
                  {{ item?.schoolList?.[0]?.schoolName }}...
                </div>
                <div v-else>
                  {{ item?.schoolList?.[0]?.schoolName }}
                </div>
              </div>
            </div>
          </div>
          <g-empty v-else></g-empty>
        </div>
      </g-list>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-select--large .el-select__wrapper {
    min-height: 34px !important;
  }
}
.bookBg {
  background: url(@/assets/img/taskCenter/bookBg.png) center / cover no-repeat;
}
</style>
