<script setup lang="ts" name="AiTaskEdit">
import { editCatalogResModify, getCatalogResourceDetail } from '@/api/aiTask'
import { useAiTaskStore } from '@/stores/modules/aiTask'
import QuestionChangeDialog from '@/views/teacher/task/errorTaskNew/components/QuestionChangeDialog/index.vue'
import CourseList from './components/CourseList.vue'
import QuestionItem from './components/QuestionItem.vue'

const aiTaskStore = useAiTaskStore()
const route = useRoute()
let showLoading = $ref(true)
let pageAllData = $ref<any>({})
let resourceList = $ref<any>([])
let currentTab = $ref(0)
let showDialog = $ref(false) // 换题弹框
let currentQuestionItem = $ref<any>() // 当前试题

const isAllEmpty = $computed(() => {
  const arrs = [
    pageAllData?.articleList,
    pageAllData?.attachList,
    pageAllData?.children,
    pageAllData?.questionList,
    pageAllData?.videoList,
  ]
  return arrs.every(arr => !Array.isArray(arr) || arr.length === 0)
})

function getSubTitle() {
  let title = '训练试题将从下方的列表中随机抽取给学生练习'
  const bookCatalogName = pageAllData.children?.[currentTab]?.bookCatalogName
  if (bookCatalogName == '基础必备')
    title = '以下题目为必过题，系统将从中抽取<span class="text-[#6474FD]"> 2道题 </span>供学生练习使用'

  else if (bookCatalogName == '能力提升')
    title = '以下题目为非必过题，系统将从中抽取<span class="text-[#6474FD]"> 1道题 </span>供学生练习使用'

  return title
}

async function fetchPageData() {
  try {
    const res = await getCatalogResourceDetail({
      bookCatalogId: route.query?.bookCatalogId,
      selectModification: aiTaskStore.isModified(route.query?.bookCatalogId),
    })
    if (res?.articleList) {
      res.articleList.forEach((v) => {
        v.bookCatalogResourceType = 2
        v.bookCatalogAttachId = v.bookCatalogArticleId
      })
    }
    if (res?.videoList) {
      res.videoList.forEach((v) => {
        v.bookCatalogResourceType = 3
        v.hasMsg = aiTaskStore.isLeaveMsg(v.videoResourceId)
      })
    }
    pageAllData = res
    resourceList = $g.tool.isTrue(pageAllData?.articleList) ? pageAllData?.articleList : pageAllData?.videoList
    showLoading = false
  }
  catch (e) {
    showLoading = false
  }
}

function openChangeQes(item) {
  currentQuestionItem = item
  showDialog = true
}

function getVariantQuestion(data) {
  editCatalogResModify({
    bookCatalogId: pageAllData.children[currentTab]?.bookCatalogId,
    taskResourceType: 1,
    addTaskResourceFromIdList: [data?.questionId],
    removeTaskResourceFromIdList: [currentQuestionItem?.questionId],
  }).then((e) => {
    // 手动替换试题
    const list = pageAllData.children[currentTab]?.questionList
    const idx = list?.findIndex(v => v.questionId == currentQuestionItem.questionId)
    if (idx !== -1)
      list[idx] = data

    const bookCatalogId = route.query?.bookCatalogId
    if (!bookCatalogId) return
    if (aiTaskStore.isModified(bookCatalogId))
      return

    aiTaskStore.setModified(bookCatalogId)
  })
}

onBeforeMount(() => {
  fetchPageData()
})
</script>

<template>
  <div class="min-h-screen p-26px" style="width: 100vw;">
    <g-navbar title="课程编辑" class="mb-26px">
    </g-navbar>

    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <template v-if="!showLoading && !isAllEmpty">
      <CourseList
        v-if="resourceList?.length"
        :resource-list="resourceList"
        :book-catalog-name="pageAllData.bookCatalogName"
        @update-detail="fetchPageData"
      />
      <div v-if="pageAllData?.children?.length" class="p-13px pt-17px bg-white rounded-[13px] mt-17px">
        <template
          v-for="(child, cIdx) in pageAllData.children"
          :key="child.bookCatalogId"
        >
          <span
            class="inline-block font-600 text-15px mr-7px text-[#9A9A9A] pl-9px cursor-pointer"
            :class="{ '!text-[#333]': cIdx == currentTab }"
            @click="currentTab = cIdx"
          >
            {{ child.bookCatalogName }}（{{ child.questionList?.length }}）
          </span>
        </template>
        <div class="text-12px text-[#9A9A9A] mb-17px mt-6px pl-9px" v-html="getSubTitle()"></div>
        <template v-if="pageAllData?.children?.[currentTab]?.questionList?.length">
          <div
            v-for="(item, index) in pageAllData.children[currentTab].questionList"
            :key="index"
            class="br-[6px] mb-[9px] border border-[#DCDFE6] bg-[#FFFFFF]"
          >
            <QuestionItem
              :question-item="item"
              :question-index="index"
              render-if-change
              @open-change-qes="openChangeQes"
            />
          </div>
        </template>
      </div>
    </template>
    <g-empty v-if="!showLoading && isAllEmpty"></g-empty>

    <!-- 换题弹窗 -->
    <QuestionChangeDialog
      v-if="$g.tool.isTrue(currentQuestionItem)"
      v-model:show="showDialog"
      :question-id="currentQuestionItem?.questionId"
      :book-id="54675"
      :difficulty="currentQuestionItem?.sysQuestionDifficultyName"
      :knowledge-point="currentQuestionItem?.knowledgePoints"
      :structure-number="currentQuestionItem?.structureNumber"
      :parallel-paper-type="currentQuestionItem?.sysQuestionDifficultyId"
      :sys-course-id="currentQuestionItem?.sysCourseId"
      :question-item="currentQuestionItem"
      :variant-question-id="currentQuestionItem?.questionId"
      from="aiTask"
      @get-variant-question="getVariantQuestion"
    />
  </div>
</template>

<style scoped lang="scss">
</style>
