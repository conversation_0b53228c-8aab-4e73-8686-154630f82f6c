<script setup lang="ts">
import { getDiffData, getQuestionList } from '@/api/taskCenter'
import QuestionItem from './QuestionItem.vue'

const questionList = inject<Ref<any[]>>('questionList', ref([]))
const currentPaperId = inject<Ref<any>>('currentPaperId', ref(null))
const setStepVal = inject('setStepVal') as any

let paperData = $ref<any>(null)
let isAddAll = $ref(false)
let paperQuestionList = $ref<any>([])

let rightLoading = $ref(true)

const tableData = $computed(() => {
  if (!paperData?.questionTypeList.length)
    return []

  let count = 0
  const res = paperData.questionTypeList.map((v) => {
    count += v.questionTotal
    return {
      label: v.sysQuestionTypeName,
      count: v.questionTotal,
    }
  })
  return [
    ...res,
    {
      label: '合计',
      count,
    },
  ]
})

const colorMap = {
  容易: '#8CE48C',
  较易: '#959BFF',
  一般: '#6474FD',
  较难: '#6ED3F4',
  困难: '#FF9945',
  其他: '#C7CAE4',
}

const gChartRef = $ref<any>()

let baseOption: any = {
  legend: {
    bottom: 0,
  },
  series: [
    {
      name: '难度',
      type: 'pie',
      center: ['50%', '40%'],
      radius: ['40%', '60%'],
      data: [
        // {
        //   name: '较难',
        //   value: 2,
        //   itemStyle: {
        //     color: '#6ED3F4',
        //   },
        // },
        // {
        //   name: '一般',
        //   value: 11,
        //   itemStyle: {
        //     color: '#6474FD',
        //   },
        // },
        // {
        //   name: '较易',
        //   value: 10,
        //   itemStyle: {
        //     color: '#959BFF',
        //   },
        // },
      ],
      labelLine: {
        length: 5,
        length2: 5,
      },
      avoidLabelOverlap: false,
      label: {
        formatter: '{b}:{c}',
      },
    },
  ],
}

let chartOption = $ref<any>(null)

useEventListener('resize', () => {
  gChartRef?.resize()
})

function goBack() {
  $g.bus.emit('update-count')
  setStepVal(2)
}

async function getDiffDataApi() {
  const data = await getDiffData({
    bookId: currentPaperId.value,
  })
  paperData = data
  nextTick(() => {
    $g.tool.renderMathjax()
  })
  initChart()
}

function initChart() {
  if (paperData?.questionDiffList) {
    const list = paperData?.questionDiffList.map(v => ({
      name: v.sysQuestionDifficultyName,
      value: v.questionTotal,
      itemStyle: {
        color: colorMap[v.sysQuestionDifficultyName] || colorMap['其他'],
      },
    }))
    baseOption.series[0].data = list
    chartOption = baseOption
  }
}

async function getQuestionListApi() {
  try {
    rightLoading = true
    const { list } = await getQuestionList({
      bookId: currentPaperId.value,
      pageSize: 9999,
    })
    paperQuestionList = list.map(v => ({
      ...v,
      isAdd: questionList.value.some(vv => vv.questionId === v.questionId),
    }))
    isAddAll = paperQuestionList.every(v => v.isAdd)
    rightLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    rightLoading = false
    console.error(e)
  }
}

function handleAdd() {
  if (isAddAll) {
    isAddAll = false
    questionList.value = questionList.value.filter(v =>
      paperQuestionList.every(vv => vv.questionId !== v.questionId))
    paperQuestionList.forEach(v => (v.isAdd = false))
  }
  else {
    isAddAll = true
    const existingIds = new Set(questionList.value.map(v => v.questionId))
    const newQuestions = paperQuestionList.filter(
      v => !existingIds.has(v.questionId),
    )
    questionList.value.push(...newQuestions) // 批量推送
    paperQuestionList.forEach(v => (v.isAdd = true))
  }
}

function selectChange([id, flag]) {
  if (flag)
    isAddAll = paperQuestionList.every(v => v.isAdd)

  else
    isAddAll = false
}

onBeforeMount(() => {
  getDiffDataApi()
  getQuestionListApi()
})
</script>

<template>
  <div>
    <div class="w-full flex items-start justify-between mb-23px">
      <div class="flex items-start">
        <g-navbar :on-back="goBack" class="!mr-0"></g-navbar>
        <div class="-ml-2px">
          <div class="flex items-center mb-7px h-21px leading-[21px]">
            <div class="no-title-w text-15px text-[#333] font-600 truncate mr-9px">
              <g-mathjax :text="paperData?.bookName || '-'" />
            </div>
            <div
              v-if="paperData?.sysGradeName"
              class="px-5px h-20px leading-[20px] border border-solid border-[#D7DDE9] bg-[#E9ECF7] rounded-[3px] text-13px text-[#6C6C74] mr-6px"
            >
              {{ paperData?.sysGradeName }}
            </div>
            <div
              v-if="paperData?.sysSubjectName"
              class="px-5px h-20px leading-[20px] border border-solid border-[#D7DDE9] bg-[#E9ECF7] rounded-[3px] text-13px text-[#6C6C74]"
            >
              {{ paperData?.sysSubjectName }}
            </div>
          </div>
          <div class="flex items-center text-13px text-[#6C6C74] h-18px leading-[18px]">
            <div>{{ paperData?.year || '-' }}</div>
            <div class="w-1px h-10px bg-[#6C6C74] opacity-30 mx-4px"></div>
            <div>共{{ paperData?.questionTotal || 0 }}题</div>
          </div>
        </div>
      </div>
      <div class="pt-10px">
        <el-button v-if="paperQuestionList.length"
                   :type="isAddAll ? 'danger' : 'primary'"
                   @click="handleAdd"
        >
          {{ isAddAll ? '取消' : '' }}全部加入
        </el-button>
      </div>
    </div>
    <div class="box-h flex">
      <div v-if="paperData" class="w-301px h-full">
        <el-scrollbar class="pr-10px">
          <div class="border border-solid border-[#DADDE8] rounded-[6px] bg-white px-19px py-13px mb-13px">
            <div class="text-15px text-[#333] font-600 h-21px leading-[21px] mb-15px">
              题型统计
            </div>
            <table class="w-full text-14px text-[#333]">
              <thead>
                <tr class="h-42px">
                  <th class="w-1/2 bg-[#6474FD1A] rounded-[4px_0_0_4px] text-left pl-13px">
                    题型
                  </th>
                  <th class="w-1/2 bg-[#6474FD1A] rounded-[0_4px_4px_0] text-left pl-13px">
                    数量
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in tableData"
                  :key="index"
                  class="h-42px"
                  :class="{
                    'border-t border-solid border-[#f1f1f1]': index !== 0,
                  }"
                >
                  <td class="pl-13px">
                    {{ item.label }}
                  </td>
                  <td class="pl-13px">
                    {{ item.count }}
                  </td>
                </tr>
              </tbody>
            </table>
            <g-empty v-if="!tableData.length"></g-empty>
          </div>
          <div class="border border-solid border-[#DADDE8] rounded-[6px] bg-white px-19px py-13px">
            <div class="text-15px text-[#333] font-600 h-21px leading-[21px] mb-15px">
              难度结构
            </div>
            <div class="w-full h-240px">
              <g-chart ref="gChartRef" :option="chartOption" />
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="h-full flex-1 translate-x-10px">
        <el-scrollbar class="pr-10px">
          <g-loading v-if="rightLoading" class="h-200px"></g-loading>
          <template v-else>
            <template v-if="paperQuestionList.length">
              <div
                v-for="(item, index) in paperQuestionList"
                :key="item.questionId"
                class="border border-solid border-[#DADDE8] rounded-[6px] bg-white mb-9px"
              >
                <QuestionItem :question-item="item"
                              :question-index="index"
                              @change="selectChange"
                />
              </div>
            </template>
            <g-empty v-else></g-empty>
          </template>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.box-h {
  height: calc(100vh - 9vh - 54px - 46px - 18px);
}

.no-title-w{
  max-width: calc(100vw - 26px - 26px - 34px - 11px - 88px - 80px - 20px - 40px); /* apply-without-convert */
}
</style>
