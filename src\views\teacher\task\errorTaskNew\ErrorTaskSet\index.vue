<script setup lang="ts">
import type { PropType } from 'vue'
import { deduplicateByKey } from '@/views/teacher/task/createTask/tool'

const props = defineProps({
  classList: {
    type: Array,
    default: () => [],
  },
  formData: {
    type: Object as PropType<any>,
    default: () => {},
  },
  gradeName: {
    type: String,
    default: '',
  },
})
const route = useRoute()
const questionList = inject<Ref<any>>('questionList', ref([]))
const resources = inject<Ref<any[]>>('resources', ref([]))
const setStepVal = inject('setStepVal') as any
let updatePicker = $ref<any>('')
// 禁用当前时间之前的日期和时间
function disabledDate(time) {
  const now = $g.dayjs().startOf('day') // 获取今天的00:00:00
  const selectedTime = $g.dayjs(time) // 被选择的时间

  // 禁用今天以前的日期
  return selectedTime.isBefore(now, 'day')
}

const recommendedTime = $computed(() => {
  // 如果为试题类型任务
  let minutes = 0
  if ((route.query.pageType as any) != 3) {
    const [objectArr, subjectiveArr] = questionList.value
      .flatMap(item => item.subQuestions)
      .reduce(
        (res, item) => {
          res[[1,
2,
3].includes(item.subQuestionType)
            ? 0
            : 1].push(item)
          return res
        },
        [[], []],
      )
    minutes = objectArr.length * 2 + subjectiveArr.length * 5
  }
  else {
    minutes = Math.ceil(
      resources.value.reduce(
        (res, item) => res + (item.fileDuration ?? 0),
        0,
      ) / 60,
    )
  }

  let n
  if (!minutes) { // 如果全为文件类资源 默认n为4
    n = 4
  }
  else {
    n = Math.ceil(minutes / 45) // 课时数
  }
  let m = Math.round(n / 2)
  props.formData.courseNum = n
  props.formData.estimateTime = minutes || n * 45
  return $g
    .dayjs(props.formData.releaseTime)
    .endOf('day')
    .add(m - 1, 'day')
    .format('YYYY-MM-DD HH:mm')
})
const releaseTimeShortcuts = [
  {
    text: '立即发布',
    value: () => $g.dayjs().format('YYYY-MM-DD HH:mm'),
  },
]
let requireCompleteShortcuts = $ref<any>([])
const releaseTimeFormat = $computed(() => {
  if ($g.dayjs(props.formData.releaseTime).isSame($g.dayjs().format('YYYY-MM-DD HH:mm'), 'minute')) {
    props.formData.isImmediate = true
    return '立即发布'
  }
  props.formData.isImmediate = false
  return 'YYYY-MM-DD HH:mm'
})
const requireCompleteTimeFormat = $computed(() => {
  if (
    $g
      .dayjs(props.formData.requireCompleteTime)
      .isSame(recommendedTime, 'minute')
  ) 
    return 'YYYY-MM-DD HH:mm(推荐)'


  return 'YYYY-MM-DD HH:mm'
})
const studentData = inject<Ref<any>>('studentData', ref({}))
let ifUnfold = $ref(false)

watch(
  () => recommendedTime,
  () => {
    requireCompleteShortcuts = [
      {
        text: `推荐时间${recommendedTime}`,
        value: recommendedTime,
      },
    ]
    updatePicker = new Date()
    if (props.formData.notIsRecommend) return
    props.formData.requireCompleteTime = recommendedTime
  },
  {
    immediate: true,
  },
)
function initData() {
  props.formData.questionRange = 1
  props.formData.releaseTime = $g.dayjs().format('YYYY-MM-DD HH:mm')
  props.formData.taskName =
    `${$g.dayjs().format('MM/DD') + route.query.subjectName}错题任务`
  props.formData.configCorrect = 2
}
onBeforeMount(() => {
  initData()
})

function goBack() {
  setStepVal(1)
}

function handleRecommend(val) {
  props.formData.notIsRecommend = !$g.dayjs(val).isSame(recommendedTime, 'minute')
}
</script>

<template>
  <div class="flex flex-col h-full px-26px">
    <g-navbar title="布置错题任务"
              class="mb-17px"
              :on-back="goBack"
    >
    </g-navbar>

    <div class="flex-1 overflow-auto no-bar">
      <div class="flex items-center">
        <span class="text-16px leading-[24px] font-600">3.任务设置</span>
      </div>
      <div
        class="bg-white mt-17px min-h-[509px] flex flex-col items-center pt-17px"
      >
        <el-form :model="formData"
                 label-width="auto"
                 class="w-[61.72vw]"
        >
          <el-form-item label="任务名称"
                        required
                        class="mb-32px"
          >
            <el-input v-model="formData.taskName"
                      maxlength="30"
                      show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="试题范围"
                        required
                        class="mb-32px"
          >
            <el-radio-group v-model="formData.questionRange">
              <el-radio :value="1">
                只发原错题
              </el-radio>
              <el-radio :value="2">
                只发变式题
              </el-radio>
              <el-radio :value="3">
                发错题和变式题
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="任务时间"
                        required
                        class="mb-32px"
          >
            <div
              class="h-109px bg-[#F3F3FB] br-[4px] w-full flex px-11px text-13px pt-17px"
            >
              <div
                v-if="classList.length"
                class="text-13px w-84px truncate h-18px lh-[18px] mt-29px"
              >
                {{ classList.map((item: any) => `${gradeName} ${item.className}`).join('、') }}
              </div>
              <div class="mx-17px">
                <div class="h-18px lh-[18px] mb-5px text-[#929296]">
                  发布时间
                </div>
                <el-date-picker
                  v-model="formData.releaseTime"
                  type="datetime"
                  :show-now="false"
                  :shortcuts="releaseTimeShortcuts"
                  :format="releaseTimeFormat"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD HH:mm"
                />
              </div>
              <div>
                <div class="h-18px lh-[18px] mb-5px text-[#929296]">
                  要求完成时间
                </div>
                <el-date-picker
                  :key="updatePicker"
                  v-model="formData.requireCompleteTime"
                  type="datetime"
                  :show-now="false"
                  :format="requireCompleteTimeFormat"
                  :shortcuts="requireCompleteShortcuts"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD HH:mm"
                  @change="handleRecommend"
                />
              </div>
            </div>
            <div class="mt-12px">
              <div class="mt-13px text-14px text-[#333]">
                <div class="filterItem">
                  <div class="flex items-center w-136px">
                    <span>批改设置：</span>
                    <el-tooltip
                      class="box-item"
                      placement="top"
                      trigger="hover"
                    >
                      <template #content>
                        <div style="max-width: 200px">
                          <div>
                            教师批改-教师批改(纠正)学生自查后的主观题对错
                          </div>
                          <div>学生自查-公布答案后学生自评主观题的对错</div>
                        </div>
                      </template>
                      <img
                        :src="$g.tool.getFileUrl('taskCenter/info.png')"
                        alt=""
                        class="w-12px h-12px cursor-pointer"
                      />
                    </el-tooltip>
                  </div>
                  <el-radio-group v-model="formData.configCorrect">
                    <el-radio :value="1" class="w-86px">
                      <div class="text-[#333333]">
                        教师批改
                      </div>
                    </el-radio>
                    <el-radio :value="2">
                      <div class="text-[#333333]">
                        学生自查
                      </div>
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="老师留言">
            <el-input
              v-model="formData.teacherMessage"
              type="textarea"
              :autosize="{ minRows: 5 }"
              maxlength="100"
              show-word-limit
              placeholder="请输入留言内容"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep() {
  .el-form-item__label {
    font-size: 15px;
    color: #333;
  }
}

.filterItem {
  height: 17px;
  line-height: 17px;
  display: flex;
  align-items: center;
  margin-bottom: 9px;
}
</style>
