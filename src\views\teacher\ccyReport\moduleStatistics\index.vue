<script setup lang="ts" name="ModuleStatistics">
import {
  exportStudentActivityAbnormal,
  getActivityStudentModuleListSummary,
  getActivityStudentModuleListV2,
  getActivitySubjectList,
  getStudentList,
} from '@/api/activity'
import TabBar from '@/views/teacher/ccyReport/reportDetail/components/TabBar.vue'
import CorrectionList from '../components/CorrectionList.vue'
import { shortcuts } from '../reportDetail/constant'
import { TASK_TYPE_MAP } from './constant'

let activeTab = $ref('1')
let dateRange = $ref<any>(null)
const route = useRoute()
let subjectList = $ref<any>([])
let showLoading = $ref(true)
let statistics = $ref<any>({})
let activedModuleTab = $ref<string>('moduleProgress')
let exerciseRecordList = $ref<any>([])
let testBox = $ref<any>()
let myLoading = $ref(true)
let downLoading = $ref(false)

const pageOption = reactive({
  page: 1,
  page_size: 5,
  total: 0,
})
const statisticsPageOption = reactive({
  page: 1,
  page_size: 5,
  total: 0,
})
// 对应ExamTable文件的tabValue（只展示测验完成情况）
const isOnlyTest = $computed(() => {
  return ['3',
'4',
'5',
'6',
'7'].includes(route.query.tabValue as string)
})

// 测验类型样式
const taskTypeMap = TASK_TYPE_MAP

const title = $computed(() => {
  return `学生：${route.query.userName} 启鸣号：${route.query.thirdUniqueId ?? '-'}`
})
let getCorrectRate = $computed(() => {
  return (val) => {
    return $g.math(val).multiply(100).toFixed(0).value()
  }
})
const column = [
  {
    prop: 'activityThemeModuleName',
    label: '模块名称',
    className: 'activityThemeModuleName',
  },
  {
    prop: 'test',
    label: '试题进度(做对/总数)',
    slot: true,
  },
  {
    prop: 'rightQuestionNum',
    label: '做对试题数',
  },
  {
    prop: 'totalQuestionNum',
    label: '总试题',
  },
  {
    prop: 'errorQuestionNum',
    label: '待重做',
    slot: true,
  },
  {
    prop: 'totalAttachNum',
    label: '课件',
    formatter: (row) => {
      return `${row.attachReadNum ?? 0}/${row.totalAttachNum ?? 0}`
    },
  },
  {
    prop: 'videoWatchNum',
    label: '微课',
    formatter: (row) => {
      return `${row.videoWatchNum}/${row.totalVideoNum}`
    },
    slot: true,
  },
  {
    prop: 'articleReadNum',
    label: '知识卡片',
    formatter: (row) => {
      return `${row.articleReadNum}/${row.totalArticleNum}`
    },
    slot: true,
  },
  {
    prop: 'abnormalQuestionNum',
    label: '异常答题数',
  },
  {
    prop: 'cz',
    label: '操作',
    slot: true,
    width: 150,
  },
]

let getDateTime = $computed(() => {
  return (val, type = 'day') => {
    if (type === 'day') {
      return $g.dayjs(val).format('MM-DD')
    }
    else if (type === 'time') {
      return $g.dayjs(val).format('HH:mm')
    }
    else if (type === 'second') {
      let minte = Math.floor(val / 60)
      let second = val % 60
      return `${minte ? `${minte}分` : ''}${second}秒`
    }
  }
})
function getFast(list) {
  return list.filter(item => item?.abnormalState == 1)
}
function getSlow(list) {
  return list.filter(item => item?.abnormalState == 2)
}
function getAllTotal(type) {
  return statisticsArr.find(v => v.value == type).num
}

let statisticsArr = $ref<any>([
  {
    label: '做对试题数',
    value: 'rightQuestionNum',
  },
  {
    label: '待重做错题',
    value: 'errorQuestionNum',
  },
  {
    label: '已重做错题',
    value: 'correctiveNum',
  },
  {
    label: '观看微课数',
    value: 'videoWatchNum',
  },
  {
    label: '学习知识卡片数',
    value: 'articleReadNum',
  },
  {
    label: '学习课件数',
    value: 'attachReadNum',
  },
  {
    label: '异常答题数',
    value: 'abnormalQuestionNum',
    className: ['text-[#FF4646]'],
  },

])
/* 获取统计数据 */
async function fetchStatisticsData() {
  try {
    let res = await getActivityStudentModuleListSummary({
      activityId: route.query.activityId,
      sysCourseId: activeTab,
      schoolStudentId: route.query.schoolStudentId,
      beginDateTime: dateRange?.[0] ?? null,
      endDateTime: dateRange?.[1] ?? null,
    })
    statisticsArr.forEach((item) => {
      item.num = res[item.value]
    })
  }
  catch (err) {
    console.log('获取统计数据出错', err)
  }
}
async function getList() {
  if (activedModuleTab == 'moduleProgress') {
    statisticsPageOption.page = 1
    statisticsPageOption.total = 0
    fetchStatisticsData()
    await fetchStudentStatistics()
  }
  else if (activedModuleTab === 'testComplete') {
    pageOption.page = 1
    pageOption.total = 0
    exerciseRecordList = []
    await getStudentListApi()
  }
}
onBeforeMount(async () => {
  if (isOnlyTest)
    activedModuleTab = 'testComplete'

  if (route.query.beginDate && route.query.endDate)
    dateRange = [route.query?.beginDate ?? '', route.query?.endDate ?? '']

  await fetchSubjectList()
  // await fetchStudentStatistics()

  await getList()
})
/* 获取学科 */
async function fetchSubjectList() {
  try {
    let res = await getActivitySubjectList({
      activityId: route.query.activityId,
    })
    subjectList = res?.map((v) => {
      return {
        ...v,
        label: v.sysSubjectName,
        key: v.sysCourseId,
      }
    }) || []
    activeTab = route.query.sysCourseId as any
  }
  catch (err) {
    showLoading = false
    console.log('获取学科失败', err)
  }
}

const handleChange = useDebounceFn(async (val) => {
  if (val?.[0] == 'Invalid Date' || val?.[1] == 'Invalid Date')
    dateRange = ['', '']

  else
    dateRange = val ?? ['', '']

  setDefaultSelected()
  // fetchStudentStatistics()
  await getList()
}, 100)
// 日期选择器显示隐藏
function handleVisibleChange(visible) {
  if (visible)
    setDefaultSelected()
}
// 设置日期选择器选中样式
function setDefaultSelected() {
  // shortcuts 数据格式化
  const tempDateList = shortcuts.map(item => ({
    ...item,
    value: item.value(),
  }))
  // 全部时返回【'Invalid Date'，'Invalid Date'】
  const isInvalidDate = dateRange?.[0] === 'Invalid Date' || dateRange?.[1] === 'Invalid Date'
  const isEmptyDate = dateRange?.[0] == '' || dateRange?.[1] == ''
  // 对应的选中index
  const selectedItemIndex = isInvalidDate || isEmptyDate
    ? 0
    : tempDateList.findIndex(
        v => v.value[0] == $g.dayjs(dateRange?.[0]).format('YYYY-MM-DD HH:mm') && v.value[1] == $g.dayjs(dateRange?.[1]).format('YYYY-MM-DD HH:mm'),
      )
  const sidebarDom = document.querySelector('.el-picker-panel__sidebar')
  if (sidebarDom?.children) {
    Array.from(sidebarDom.children).forEach((child: any, index) => {
      const isSelected = index === selectedItemIndex
      child.style.backgroundColor = isSelected ? '#F5F7FF' : ''
      child.style.color = isSelected ? '#6474FD' : ''
    })
  }
}
/* 获取学生统计数据 */
async function fetchStudentStatistics(add = false) {
  try {
    showLoading = true
    let params: any = {
      activityId: route.query.activityId,
      sysCourseId: activeTab,
      schoolStudentId: route.query.schoolStudentId,
      page: statisticsPageOption.page,
      pageSize: statisticsPageOption.page_size,
    }
    if (dateRange?.[0] && dateRange?.[1]) {
      params = {
        ...params,
        beginDateTime: $g.dayjs(dateRange[0]).format('YYYY-MM-DD HH:mm:ss'),
        endDateTime: $g.dayjs(dateRange[1]).format('YYYY-MM-DD HH:mm:ss'),
      }
    }

    let res = await getActivityStudentModuleListV2(params)
    /* res的每一项的moduleStatisticsList的每一项的rightQuestionNum / totalQuestionNum */
    res.list.forEach((item) => {
      item.moduleStatisticsList.forEach((item2) => {
        // NAN默认为0
        item2.percentage = (item2.rightQuestionNum / item2.totalQuestionNum) * 100 || 0
        item2.showText = `${item2.rightQuestionNum}/${item2.totalQuestionNum}`
      })
    })
    statistics = add
      ? statistics.concat(res?.list)
      : res?.list
    statisticsPageOption.total = res.total
    setTimeout(() => {
      showLoading = false
    }, 200)
  }
  catch (err) {
    showLoading = false
    statistics = []
    console.log('获取学生统计数据失败', err)
  }
}
async function pulldownStatistics() {
  statisticsPageOption.page = 1
  await fetchStudentStatistics()
  await nextTick()
  testBox?.scrollTo({ top: 0 })
}
async function pullupStatistics() {
  statisticsPageOption.page += 1
  await fetchStudentStatistics(true)
}
/**
 * 查看数据
 * @param {object} row 当前行数据
 * @param {number} type 1:查看报告 2:查看错题
 */
function viewData(row, type = 1) {
  console.log('⚡[ $g.isFlutter ] >', $g.isFlutter)
  let url = `${
    import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
  }/#/student/sprintCamp/contents?activityThemeModuleId=${
    row.activityThemeModuleId
  }&preview=true&accountId=${route.query.accountId}&showBack=${$g.isFlutter}&source=${route?.query?.source ?? 'teacher'}&beginDateTime=${dateRange?.[0] ? $g.dayjs(dateRange?.[0]).format('YYYY-MM-DD HH:mm:ss') : ''}&endDateTime=${dateRange?.[1] ? $g.dayjs(dateRange?.[1]).format('YYYY-MM-DD HH:mm:ss') : ''}`
  // 查看错题加参数
  if (type == 2)
    url = `${url}&showWrongOnly=true`

  if ($g.isPC) {
    window.open(url, '_blank')
  }
  else {
    let option = {
      url,
      refreshCallJs: false,
      inSafeArea: {
        top: false,
        left: true,
        bottom: false,
        right: false,
      },
      beforeEnter: {
        // orientation: {
        //   portraitUp: false,
        //   portraitDown: false,
        //   landscapeLeft: true,
        //   landscapeRight: false,
        // },
        fullPage: true,
      },
      afterEnter: {
        // orientation: {
        //   portraitUp: false,
        //   portraitDown: false,
        //   landscapeLeft: true,
        //   landscapeRight: false,
        // },
        fullPage: true,
      },
    }
    $g.flutter('launchInNewWebView2', option)
  }
}
/* 返回一个tableOptions */
function getTableOptions(statistics) {
  return {
    column,
    data: statistics,
  }
}
async function pulldown() {
  pageOption.page = 1
  await getStudentListApi()
  await nextTick()
  testBox?.scrollTo({ top: 0 })
}
async function pullup() {
  pageOption.page += 1
  await getStudentListApi(true)
}

async function getStudentListApi(isScroll = false) {
  try {
    myLoading = true
    let res = await getStudentList({
      schoolStudentId: route.query.schoolStudentId,
      sysCourseId: activeTab,
      beginDateTime: route?.query?.beginDate ?? '',
      endDateTime: route?.query?.endDate ?? '',
      page: pageOption.page,
      pageSize: pageOption.page_size,
      exerciseTaskTypes: {
        2: 'ACTIVE',
        3: 'STUDENT_TRAIN_MONTHLY_EXAM',
        4: 'STUDENT_TRAIN_WEEKLY_EXAM',
        5: 'STUDENT_TRAIN_EVALUATE_PAPER,STUDENT_TRAIN_DIAGNOSE_PAPER',
        6: 'PERSONALITY_SIMULATION_TEST_STANDARD_EXAM',
        7: 'GAOKAO_MOCK',
      }[route.query.tabValue as string] || null,
    })
    exerciseRecordList = isScroll
      ? exerciseRecordList.concat(res?.list)
      : res?.list
    pageOption.total = res?.total
  }
  catch (error) {
    console.log(error)
  }
  finally {
    myLoading = false
  }
}
function goReport(obj) {
  let source = route?.query?.source ?? 'teacher'
  let url = `${
    import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
  }/#/student/studentTest/testReport?exerciseTaskId=${obj?.exerciseTaskId}&showBack=false&source=${source}${isOnlyTest ? '&showCumulativeTime=1' : ''}`
  if ($g.isPC) {
    window.open(url, '_blank')
  }
  else {
    let option = {
      url,
      refreshCallJs: false,
      inSafeArea: {
        top: false,
        left: true,
        bottom: false,
        right: false,
      },
      beforeEnter: {
        // orientation: {
        //   portraitUp: false,
        //   portraitDown: false,
        //   landscapeLeft: true,
        //   landscapeRight: false,
        // },
        fullPage: true,
      },
      afterEnter: {
        // orientation: {
        //   portraitUp: false,
        //   portraitDown: false,
        //   landscapeLeft: true,
        //   landscapeRight: false,
        // },
        fullPage: true,
      },
    }
    $g.flutter('launchInNewWebView2', option)
  }
}
function changeTab(tab) {
  activedModuleTab = tab
  getList()
}
// 导出异常答题数
async function exportDataToExcel() {
  try {
    downLoading = true
    let params: any = {
      activityId: route.query.activityId,
      schoolStudentId: route.query.schoolStudentId,
    }
    if (dateRange?.[0] || dateRange?.[1]) {
      params = {
        ...params,
        beginDateTime: dateRange?.[0],
        endDateTime: dateRange?.[1],
      }
    }
    let res = await exportStudentActivityAbnormal(params)
    let headers = [
      {
        label: '学生姓名',
        prop: 'studentName',
      },
      {
        label: '专题',
        prop: 'activityThemeName',
      },
      {
        label: '模块名称',
        prop: 'activityThemeModuleName',
      },
      {
        label: '题目ID',
        prop: 'questionId',
      },
      {
        label: '异常原因',
        prop: 'abnormalReason',
      },
      {
        label: '异常时间',
        prop: 'abnormalTime',
      },
    ]
    let sheetData = res?.courseList?.map((item: any) => {
      let sheetItem = {
        name: item?.sysSubjectName,
        data: [],
        headers,
      }
      sheetItem.data = item?.questionList?.map((question: any) => {
        return {
          studentName: res?.studentName ?? '',
          activityThemeName: question?.activityThemeName ?? '',
          activityThemeModuleName: question?.activityThemeModuleName ?? '',
          questionId: question?.questionId ?? '',
          abnormalReason: question?.abnormalReason ?? '',
          abnormalTime: question?.abnormalTime,
        }
      })
      return sheetItem
    })
    $g.tool.downloadExcel(sheetData, route?.query?.excelName, '无可下载的数据')
    downLoading = false
  }
  catch (err) {
    downLoading = false
    console.log('导出数据失败', err)
  }
}
</script>

<template>
  <div class="px-26px pt-26px">
    <g-navbar :title="title" class="mb-17px"></g-navbar>
    <div class="h-[calc(100vh-26px-34px-17px)]">
      <div class="h-[calc(100%-21px)] overflow-auto relative no-bar">
        <div class="bg-white p-[17px] rounded-[6px] ">
          <TabBar v-model:active-tab="activeTab"
                  :data="subjectList"
                  @handle-change="getList"
          />
          <div class="flex items-center justify-end mt-19px ">
            <div v-if="activedModuleTab == 'moduleProgress'" class="flex items-center ">
              <div class="flex-shrink-0 mr-13px">
                时间范围
              </div>
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                :shortcuts="shortcuts"
                :class="$g.isPC ? 'w-400px' : 'w-360px'"
                :teleported="false"
                clearable
                :editable="false"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="handleChange"
                @visible-change="handleVisibleChange"
              />
            </div>
          </div>
        </div>

        <div class="flex mt-[15px] mb-[17px] justify-between ">
          <div class="flex gap-[26px]">
            <div
              v-if="!isOnlyTest"
              class="text-[15px] font-[600] text-[#6C6C74] leading-[21px] cursor-pointer"
              :class="{ '!text-[#333] activedTab': activedModuleTab == 'moduleProgress' }"
              @click="changeTab('moduleProgress')"
            >
              学生进度统计
            </div>
            <div
              class="text-[15px] font-[600] text-[#6C6C74] leading-[21px] cursor-pointer"
              :class="{ '!text-[#333] activedTab': activedModuleTab == 'testComplete' }"
              @click="changeTab('testComplete')"
            >
              测验完成情况
            </div>
            <div
              class="text-[15px] font-[600] text-[#6C6C74] leading-[21px] cursor-pointer"
              :class="{ '!text-[#333] activedTab': activedModuleTab == 'aiCorrection' }"
              @click="changeTab('aiCorrection')"
            >
              AI批改结果
            </div>
          </div>

          <el-button
            v-if="activedModuleTab == 'moduleProgress'"
            :loading="downLoading"
            :disabled="!statistics.length"
            :class="{ '!cursor-not-allowed !text-[#999] !border-[#999]': $g.tool.isTrue(!statistics.length) }"
            class="min-w-[147px] h-[34px] bg-[#fff] br-[17px] border border-[#6474FD] cursor-pointer flex items-center justify-center mr-[17px]"
            @click="exportDataToExcel"
          >
            <svg-ri-download-line class="w-17px h-17px flex-shrink-0 text-[#6474FD]" :class="{ '!text-[#999]': $g.tool.isTrue(!statistics.length) }" />
            <span
              class="text-[13px] text-[#6474FD] font-500 pl-[4px] leading-[19px]"
              :class="{ '!text-[#999] ': $g.tool.isTrue(!statistics.length) }"
            >
              下载异常答题报告
            </span>
          </el-button>
        </div>
        <div
          class="bg-white p-[17px] rounded-[6px]"
        >
          <!-- 学习进度统计 -->
          <div v-if="activedModuleTab == 'moduleProgress'">
            <div class="grid grid-cols-7 bg-[#F3F4F9] h-[103px] rounded-[9px] box-border pt-21px mb-13px">
              <div v-for="(it, index) in statisticsArr" :key="it.value">
                <div class="text-center ">
                  <div
                    class="text-[#333] text-[26px] font-600 mb-5px border-r border-[#E5E5E5]"
                    :class="[{ '!border-r-0': index == statisticsArr.length - 1 }, it.className?.join(',')]"
                  >
                    {{ getAllTotal(it.value) }}
                  </div>
                  <div
                    class="text-[#929296] text-[13px] text-center "
                  >
                    {{ it.label }}
                  </div>
                </div>
              </div>
            </div>
            <div class="relative">
              <div
                v-if="statistics.length"
                class="text-[14px] text-[#636772] leading-[20px] absolute top-4px right-0"
              >
                更新时间：实时统计
              </div>
              <!-- 学生进度统计 -->
              <g-list
                v-model:data="statistics"
                :page-option="statisticsPageOption"
                url="/tutoring/admin/activity/statistics/student/module/listV2"
                :show-loading="showLoading"
                @pulldown="pulldownStatistics"
                @pullup="pullupStatistics"
              >
                <div v-for="item in statistics"
                     :key="item.activityThemeId"
                     class="mb-21px "
                >
                  <div class="text-[#333] text-[17px] font-600 mb-17px">
                    {{ item.activityThemeName }}
                  </div>
                  <g-table :table-options="getTableOptions(item.moduleStatisticsList)" :border="false">
                    <template #test="{ row }">
                      <div class="flex-cc">
                        <el-progress
                          class="w-[60px]"
                          :percentage="row.percentage"
                          color="#6474FD"
                          :stroke-width="10"
                          :show-text="false"
                        />
                        <div class="ml-3px">
                          <span>{{ row.rightQuestionNum }}</span><span class="text-[#999999]">/{{ row.totalQuestionNum }}</span>
                        </div>
                      </div>
                    </template>
                    <template #errorQuestionNum="{ row }">
                      <div :class="{ 'text-[red]': row.errorQuestionNum != 0 }">
                        {{ row.errorQuestionNum }}
                      </div>
                    </template>
                    <template #videoWatchNum="{ row }">
                      <span>{{ row.videoWatchNum }}</span><span class="text-[#999999]">/{{ row.totalVideoNum }}</span>
                    </template>
                    <template #articleReadNum="{ row }">
                      <span>{{ row.articleReadNum }}</span><span class="text-[#999999]">/{{ row.totalArticleNum }}</span>
                    </template>
                    <template #cz="{ row }">
                      <div class="flex gap-[10px] justify-center" :class="{ 'flex-nowrap': $g.isPC, 'flex-wrap': !$g.isPC }">
                        <div class="text-[#6474FD] cursor-pointer" @click="viewData(row, 1)">
                          查看报告
                        </div>
                        <div v-if="row.errorQuestionNum != 0"
                             class="text-[red] cursor-pointer"
                             @click="viewData(row, 2)"
                        >
                          重做错题
                        </div>
                      </div>
                    </template>
                  </g-table>
                </div>
              </g-list>
            </div>
          </div>
          <!-- 测验完成情况 -->
          <div v-else-if="activedModuleTab == 'testComplete'"
               ref="testBox"
               class="relative"
          >
            <div
              v-if="exerciseRecordList.length"
              class="text-[14px] text-[#636772] leading-[20px] absolute top-0 right-0"
            >
              更新时间：实时统计
            </div>
            <g-list
              v-model:data="exerciseRecordList"
              :page-option="pageOption"
              url="/tutoring/admin/activity/statistics/module/student/task/list"
              :show-loading="myLoading"
              @pulldown="pulldown"
              @pullup="pullup"
            >
              <div v-for="(item, index) in exerciseRecordList" :key="item?.exerciseTaskId">
                <div class="h-[115px] flex gap-[14px]" :class="{ '!h-[145px]': $g.isPC }">
                  <div class="text-[15px] text-[#333] leading-[21px] font-600">
                    {{ getDateTime(item?.finishTime, 'day') }}
                  </div>
                  <div
                    class="h-inherit  relative mt-6px"
                    :class="{ 'border-r border-dashed border-[#CCCCCC]': index + 1 != pageOption.total }"
                  >
                    <div
                      class="w-6px h-6px bg-[#CCCCCC] rounded-[6px] absolute top-0 left-[-3px]"
                    ></div>
                  </div>
                  <div
                    class="min-h-[98px]  border rounded-[4px] w-[563px] border-[#DCDFE6] bg-[rgba(216,216,216,0.08)] mb-[17px] mt-6px p-17px cursor-pointer flex flex-col justify-between"
                    @click="goReport(item)"
                  >
                    <div class="flex justify-between items-center">
                      <div class="flex flex-1 items-center">
                        <div
                          class=" h-[29px] w-fit px-[6px] border  text-center rounded-[4px] leading-[29px] text-[13px]"
                          :style="taskTypeMap[item.taskType]"
                        >
                          {{ item.taskType }}
                        </div>
                        <div
                          class="ml-[13px] text-[15px] text-[#333]  font-[500] w-[300px] overflow-hidden text-ellipsis whitespace-nowrap"
                          :title="item?.exerciseTaskName"
                        >
                          {{ item?.exerciseTaskName }}
                        </div>
                      </div>
                      <div class="w-[127px] h-18px accuracy relative">
                        <div
                          class="text-[26px] text-[#6474FD] leading-[30px] absolute bottom-[-5px] right-[6px]"
                        >
                          {{ getCorrectRate(item?.correctRate)
                          }}<span class="!text-[16px]">%</span>
                        </div>
                      </div>
                    </div>
                    <div
                      class="flex justify-between items-center text-[13px] text-[#999999] leading-[18px] mt-[17px]"
                    >
                      <div v-if="item?.taskSource">
                        来源：{{ item?.taskSource == 1 ? '学生创建' : '老师创建' }}
                      </div>
                      <div>提交时间：{{ getDateTime(item?.finishTime, 'time') }}</div>
                      <div class="flex items-center gap-[23px]">
                        <el-popover
                          append-to="#app"
                          placement="top"
                          :disabled="!item?.abnormalQuestionList?.length"
                          :width="280"
                          trigger="hover"
                        >
                          <div class=" max-h-300px overflow-y-auto">
                            <div v-if="getFast(item?.abnormalQuestionList)?.length" :class="{ 'mb-[11px]': getSlow(item?.abnormalQuestionList)?.length }">
                              <div class="flex items-center">
                                <img
                                  class="w-[16px] h-[16px]"
                                  src="@/assets/img/report/up.png"
                                /><span class="text-[15px] text-[#333333] font-500">
                                  应答过快
                                </span>
                              </div>
                              <div class="px-[7px] pt-[7px] pb-[2px] bg-[#F3F4F9]/50 mt-[6px] br-[4px]">
                                <div v-for="(fast, fastIndex) in getFast(item?.abnormalQuestionList)"
                                     :key="fastIndex"
                                     class="text-[14px] text-[#333333] mb-[5px] flex justify-between"
                                >
                                  题{{ fast.index }}<span class="text-[#666666]">（合理时间：{{ fast?.reasonableMinTime }}s~{{ fast?.reasonableMaxTime }}s）</span>
                                </div>
                              </div>
                            </div>
                            <div v-if="getSlow(item?.abnormalQuestionList)?.length">
                              <div class="flex items-center">
                                <img
                                  class="w-[16px] h-[16px]"
                                  src="@/assets/img/report/down.png"
                                /><span class="text-[15px] font-500 text-[#333333]">应答过慢</span>
                              </div>
                              <div class="px-[7px] pt-[7px] pb-[2px] bg-[#F3F4F9]/50 mt-[6px] br-[4px]">
                                <div
                                  v-for="(slow, slowIndex) in getSlow(item?.abnormalQuestionList)"
                                  :key="slowIndex"
                                  class="text-[14px] flex justify-between text-[#333333] mb-[5px]"
                                >
                                  题{{ slow.index }}<span class="text-[#666666]">（合理时间：{{ slow?.reasonableMinTime }}s~{{ slow?.reasonableMaxTime }}s）</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <template #reference>
                            <div :class="{ 'flex items-center text-theme-error': item?.abnormalQuestionList?.length }">
                              <img
                                v-if="item?.abnormalQuestionList?.length"
                                class="w-[14px] h-[14px] mr-[2px]"
                                src="@/assets/img/report/error.png"
                                @click.stop
                              />
                              <span class="leading-[14px]" @click.stop>答题时长：{{ !isOnlyTest ? getDateTime(item?.seconds, 'second') : getDateTime(item?.cumulativeTime, 'second') }}</span>
                            </div>
                          </template>
                        </el-popover>

                        <div>答题数：{{ item?.completeNum }}/{{ item?.questionNum }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </g-list>
          </div>
          <!-- AI批改结果 -->
          <CorrectionList v-else-if="activedModuleTab == 'aiCorrection'" :current-subject="activeTab" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {

  thead {
    height: 34px !important;
    th {
      background-color: #F5F5F5;
    }
  }
  .el-progress-bar__outer{
    background-color: rgba(121, 138, 254, 0.2);
  }
  .activityThemeModuleName {
    .cell {
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }
}
.activedTab {
  position: relative;
  padding-bottom: 9px;
  &::after {
    display: inline-block;
    content: '';
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    border: 1px solid #6474FD;
    border-radius: 1px;
  }
}
.accuracy {
  background: url('@/assets/img/report/accuracy.png') no-repeat center /
    127px 100%;
}
</style>
