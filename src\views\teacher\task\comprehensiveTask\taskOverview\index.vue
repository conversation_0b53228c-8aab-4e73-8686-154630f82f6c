<script setup lang="ts" name="ComprehensiveTaskTaskOverview">
import {
  getGroupList,
  getStudentList,
  getSubjectList,
  studentAllComplete,
  taskCorrectionList,
  taskStateList,
} from '@/api/comprehensiveTask'
import { ElLoading } from 'element-plus'
import DataPresentation from '../components/DataPresentation.vue'
import SearchFrom from '../components/SearchForm.vue'
import studentTables from './components/studentTables.vue'

let currentGroupId: any = $ref('')
let groupList: any = $ref([])
let sortOption = $ref({})
const tableRef: any = $ref(null)
const route = useRoute()

const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,
  column: [],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})

const currentGroup: any = $computed(() => {
  const group = groupList.find(i => i.taskScheduleGroupId == currentGroupId)
  return group
})

const TASK_TYPE = {
  1: '学科网选题',
  2: '校本练习题',
  3: '资源',
  4: '考试错题任务',
}

const TASK_PATTERN_TYPE = {
  1: '标准',
  2: '默写',
  3: '阅题',
  4: '举一反一',
}

const formOptions: any = $ref({
  items: {
    keyword: {
      label: '搜索学生',
      slot: true,
    },
    sysSubjectId: {
      label: '任务科目',
      idName: 'sysSubjectId',
      labelName: 'sysSubjectName',
      list: [],
    },
    taskState: {
      label: '任务状态',
      idName: 'id',
      labelName: 'title',
      list: [],
    },
    correctionState: {
      label: '批改状态',
      idName: 'id',
      labelName: 'title',
      list: [],
    },
  },
  data: {
    keyword: '',
    sysSubjectId: 1,
    taskState: '',
    correctionState: '',
  },
})

// 概况
// let dataList: any = $ref([])
// const memoryWrite = [{ title: '参与人数', value: 0 }, { title: '满分通关率', value: null }, { title: '任务平均用时', value: null }]
// const readTask = [{ title: '参与人数', value: 0 }, { title: '自动通关率', value: null }, { title: '任务平均用时', value: null }]
// const testTask = [{ title: '任务参与', value: 0 }, { title: '首轮通关率', value: null }, { title: '平均轮数', value: null }, { title: '任务平均用时', value: null }]
// const LIST_MAP = {
//   1: memoryWrite,
//   2: readTask,
//   3: testTask,
// }

// 筛选搜索条件
function filterSearchItem() {
  if (currentGroup?.headerType == 2)
    formOptions.items.correctionState.isHidden = true

  else
    formOptions.items.correctionState.isHidden = false
}

// 任务tab切换
async function handleClick() {
  try {
    formOptions.data.keyword = ''
    formOptions.data.taskState = ''
    formOptions.data.correctionState = ''
    tableOptions.loading = true
    sortOption = {}
    await getSubjectListApi()
    filterSearchItem()
    initPage()
  }
  catch (error) {
    tableOptions.loading = false
    console.log('⚡[ error ] >', error)
  }
}

// 获取任务状态列表
async function getStatusList() {
  const res = await taskStateList()
  res.unshift({
    id: '',
    title: '全部',
  })
  formOptions.items.taskState.list = res
  formOptions.data.taskState = res[0].id
}

// 获取批改状态列表
async function getCorrectStatusList() {
  const res = await taskCorrectionList()
  res.unshift({
    id: '',
    title: '全部',
  })
  formOptions.items.correctionState.list = res
  formOptions.data.correctionState = res[0].id
}

// 获取任务科目
async function getSubjectListApi() {
  if (!currentGroupId) return
  const res = await getSubjectList({ taskScheduleGroupId: currentGroupId })
  formOptions.items.sysSubjectId.list = res
  formOptions.data.sysSubjectId = res[0]?.sysSubjectId
}

// 获取任务概况
function getTaskOverview() {}

// 获取所有任务
async function getTaskList() {
  const res = await getGroupList({ taskScheduleId: route.query.taskScheduleId || 1 })
  groupList = res.map((item) => {
    item.headerType = item.taskPatternType == 2 ? 1 : item.taskPatternType == 3 ? 2 : 3
    item.showName = `${TASK_TYPE[item.taskType]}${item.taskPatternType ? '-' : ''}${TASK_PATTERN_TYPE[item.taskPatternType] || ''}`
    return { ...item }
  })
  currentGroupId = groupList[0]?.taskScheduleGroupId
  filterSearchItem()
  // 概况逻辑暂不使用
  // dataList = LIST_MAP[currentGroup?.headerType]
}

// 页码重置
function initPage(option: any = null) {
  tableOptions.pageOptions.page = 1
  getData(option)
}

// 获取学生数据
async function getData(option: any = null) {
  try {
    if (!currentGroupId) return
    tableOptions.loading = true
    if (option?.order) sortOption = option
    if (option?.cancelSort) sortOption = {}
    const {
      page = 1,
      pageSize = 10,
    } = tableOptions.pageOptions
    const query: any = {
      page,
      pageSize,
      taskScheduleGroupId: currentGroupId,
      ...formOptions.data,
      ...sortOption,
    }
    const res = await getStudentList(query)
    tableOptions.data = res?.list || []
    tableOptions.pageOptions.total = res?.total || 0
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
  finally {
    tableOptions.loading = false
  }
}

// 一键通关
async function handleAllComplete() {
  try {
    if (!currentGroup?.taskScheduleGroupId) {
      $g.msg('当前任务组不存在！', 'error')
      return
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在处理中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })

    await studentAllComplete({ taskScheduleGroupId: currentGroup.taskScheduleGroupId })
    loading.close()
    $g.msg('一键通关成功！', 'success')
    getData() // 刷新数据
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
    $g.msg('一键通关失败！', 'error')
  }
}

onMounted(async () => {
  getStatusList()
  getCorrectStatusList()
  await getTaskList()
  getTaskOverview()
  await getSubjectListApi()
  getData()
})

onActivated(async () => {
  getTaskOverview()
  getData()
})
</script>

<template>
  <div class="p-26px" style="width: 100vw;">
    <g-navbar title="任务概况">
    </g-navbar>
    <!-- <DataPresentation class="mt-26px" :data-list="dataList" /> -->
    <el-tabs v-model="currentGroupId"
             class="mt-14px"
             @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="item in groupList"
        :key="item.taskScheduleGroupId"
        :label="item.groupName"
        :name="item.taskScheduleGroupId"
      ></el-tab-pane>
    </el-tabs>
    <div class="mt-2px br-[6px] p-17px bg-[white]">
      <SearchFrom :form-option="formOptions" @change="initPage">
        <template #keyword>
          <el-input
            v-model="formOptions.data.keyword"
            style="width: 181px"
            class="h-34px"
            clearable
            placeholder="请输入学生姓名或ID"
            @keyup.enter.prevent="initPage()"
          />
          <el-button color="#6474FD"
                     class="w-64px ml-6px h-34px border-none"
                     @click="initPage()"
          >
            搜索
          </el-button>
        </template>
      </SearchFrom>
    </div>
    <div class="mt-17px br-[6px] p-17px bg-[white]">
      <div class="flex justify-between items-center">
        <div>
          <p class="text-13px">
            <span class="font-600 text-15px mr-11px">任务列表</span><span>当前任务为：{{ currentGroup?.showName }}</span>
          </p>
          <p class="text-13px text-[#999999] mt-6px">
            更新时间: 实时统计
          </p>
        </div>
        <el-button
          type="primary"
          class="h-34px"
          :disabled="!tableOptions.data.length || currentGroup?.isFinish === 2"
          @click="handleAllComplete"
        >
          一键通关
        </el-button>
      </div>
      <studentTables
        ref="tableRef"
        :current-group="currentGroup"
        :table-options="tableOptions"
        @get-data="getData"
        @init-page="initPage"
      ></studentTables>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
  .el-tabs__nav-wrap:after {
    height: 0px;
  }
  .el-tabs__item{
    color:#6C6C74
  }
  .el-tabs__item:hover{
    color:#333;
  }
  .is-active{
    color:#333;
    font-weight: 600;
  }
}
</style>
