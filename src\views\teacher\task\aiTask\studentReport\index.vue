<script setup lang="ts" name="AiTaskStudentReport">
import { getTaskBookCatalogList } from '@/api/aiTask'
import Report from './components/Report.vue'
import ReportQuestionList from './components/ReportQuestionList.vue'

const route = useRoute()
const router = useRouter()
let activeName = $ref('report')
let treeData = $ref<any>([]) // 树形结构数据
let currentCategoryId = $ref<any>(null) // 当前选中章节ID
let currentType = $ref<any>(null) // 当前选中章节标签类型
let firstCategoryId = $ref<any>(null) // 第一级章节ID
let treeLoading = $ref(false) // 树形结构加载状态

const title = $computed(() => {
  return route.query?.title as string || '学生报告'
})

const isShowOverviewTable = $computed(() => {
  return currentCategoryId === firstCategoryId
})

function nodeClick(data) {
  if (currentCategoryId === data.bookCatalogId) return
  currentCategoryId = data.bookCatalogId
  currentType = data?.catalogCourseType || null
}

// 任务tab切换
async function handleClick() {}

// 获取树数据
async function getTreeData() {
  try {
    treeLoading = true
    const res = await getTaskBookCatalogList({
      taskId: route.query.taskId,
      showChildren: 1,
    })
    if (!res) return
    treeData = [
      {
        bookCatalogId: '',
        bookCatalogName: route.query.title || '整个任务',
        children: [...res],
      },
    ]
    currentCategoryId = treeData?.[0]?.bookCatalogId
    firstCategoryId = treeData?.[0]?.bookCatalogId
  }
  catch (error) {
    console.log(error)
  }
  finally {
    treeLoading = false
  }
}

function handleSyncData() {
  router.push({
    name: 'AiTaskDataEdit',
    query: {
      taskId: route.query.taskId,
      bookCatalogId: currentCategoryId,
      catalogCourseType: currentType,
    },
  })
}

onMounted(() => {
  getTreeData()
})
</script>

<template>
  <div class="p-26px">
    <g-navbar :title="title">
    </g-navbar>
    <div class="mt-26px flex overflow-hidden">
      <div class="w-[258px] bg-[white] br-[6px] flex-shrink-0">
        <div class="pt-17px text-[15px] pl-17px font-600">
          分层教学
        </div>
        <div class="w-full h-[calc(100vh_-_52px_-_34px_-_26px_-_54px)] bg-[white] br-[6px] p-17px overflow-auto no-bar">
          <g-loading v-if="treeLoading" class="h-200px"></g-loading>
          <template v-else>
            <g-tree
              v-if="treeData?.length"
              :border="false"
              tree-name="RightTree"
              node-key="bookCatalogId"
              class="text-[13px]"
              :default-expanded-keys="[currentCategoryId]"
              :default-checked-keys="[currentCategoryId]"
              :current-node-key="currentCategoryId"
              :tree-data="treeData"
              :highlight-current="true"
              :tree-line="false"
              @node-click="nodeClick"
              @node-expand="
                () => {
                  $g.tool.renderMathjax()
                }
              "
            >
              <template #body="{ data }">
                <div class="w-full">
                  <g-mathjax class="van-ellipsis" :text="data.bookCatalogName" />
                </div>
              </template>
            </g-tree>
            <g-empty v-else></g-empty>
          </template>
        </div>
      </div>
      <div class="flex-1 ml-26px overflow-hidden">
        <div class="flex justify-between items-center w-full">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="学生报告" name="report">
            </el-tab-pane>
            <el-tab-pane label="答题结果" name="question">
            </el-tab-pane>
          </el-tabs>
          <div class="w-98px h-34px leading-[34px] text-center text-13px text-[#6474FD] border border-[#6474FD] br-[20px] cursor-pointer" @click="handleSyncData">
            同步数据监控
          </div>
        </div>
        <div class="h-[calc(100vh_-_52px_-_34px_-_26px_-_54px)] w-full">
          <Report
            v-if="activeName === 'report'"
            :is-show-overview-table="isShowOverviewTable"
            :current-category-id="currentCategoryId"
            :catalog-course-type="currentType"
          />
          <ReportQuestionList
            v-if="activeName === 'question'"
            :current-category-id="currentCategoryId"
            :catalog-course-type="currentType"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
  .el-tabs__nav-wrap::after {
    height: 0;
  }
  .el-tabs__item{
    color:#6C6C74;
    font-size: 15px;
  }
  .el-tabs__item:hover{
    color:#333;
  }
  .is-active{
    color:#333;
    font-weight: 600;
  }
  .el-tree-node__content {
    height: auto;
    padding: 5px 0;
    margin-bottom: 5px;
    background-color: transparent !important;
    transition: color 0.1s linear;
    border-radius: 6px;
  }
  .custom-tree-node {
    overflow: hidden;
    white-space: normal;
  }
  .is-current {
    .el-tree-node__content {
      background-color: rgb(100 116 253 / 10%) !important;
      color: #6474fd !important;
      .expanded {
        color: #6474fd !important;
      }
      .el-icon {
        color: #6474fd !important;
      }
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
        color: #6c6c74 !important;
        .expanded {
          color: #6c6c74 !important;
        }
        .el-icon {
          color: #676a88 !important;
        }
      }
    }
  }
}
</style>
