import cookie from 'js-cookie'

/**
 * @description 获取token
 */
export function getToken(tokenTableName: string, storage: string) {
  if (storage) {
    if (storage === 'localStorage')
      return localStorage.getItem(tokenTableName)

    else if (storage === 'sessionStorage')
      return sessionStorage.getItem(tokenTableName)

    else if (storage === 'cookie')
      return cookie.get(tokenTableName)

    else
      return localStorage.getItem(tokenTableName)
  }
  else {
    return localStorage.getItem(tokenTableName)
  }
}

/**
 * @description 存储token
 * @param token
 * @returns {void|*}
 */
export function setToken(tokenTableName: string, tokenValue: string, storage: string) {
  if (storage) {
    if (storage === 'localStorage') {
      return localStorage.setItem(tokenTableName, tokenValue)
    }
    else if (storage === 'sessionStorage') {
      return sessionStorage.setItem(tokenTableName, tokenValue)
    }
    else if (storage === 'cookie') {
      return cookie.set(tokenTableName, tokenValue, {
        expires: 36500,
        path: '/',
        domain: '.qimingdaren.com',
      })
    }
    else {
      return localStorage.setItem(tokenTableName, tokenValue)
    }
  }
  else {
    return localStorage.setItem(tokenTableName, tokenValue)
  }
}

/**
 * @description 移除token
 * @returns {void|Promise<void>}
 */
export function removeToken(tokenTableName: string, storage: string) {
  if (storage) {
    if (storage === 'localStorage') {
      return localStorage.removeItem(tokenTableName)
    }
    else if (storage === 'sessionStorage') {
      return sessionStorage.clear()
    }
    else if (storage === 'cookie') {
      console.log(1)
      return cookie.remove(tokenTableName, {
        path: '/',
        domain: '.qimingdaren.com',
      })
    }
    else {
      return localStorage.removeItem(tokenTableName)
    }
  }
  else {
    return localStorage.removeItem(tokenTableName)
  }
}
