import config from '@/config/index'
import router from '@/router/index'
import { useUserStore } from '@/stores/modules/user'

const CODE_MESSAGE = {
  400001: 'token不存在',
  400002: '登录已过期,请重新登录',
  400003: '设置token失败',
  400004: '登录已过期,请重新登录',
  500: '服务器错误',
}

function delay(milliseconds) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, milliseconds)
  })
}

const successCode = [200,
0,
'200',
'0']
// 异常需要重置code
const { resetArr } = config

async function handleData({
  config,
  data,
  status,
  statusText,
}) {
  // 若data.code存在，覆盖默认code
  let code = data && data.code ? data.code : status
  // 若code属于操作正常code，则status修改为200
  if (successCode.includes(data.code))
    code = 200

  switch (code) {
    case 200:
      if (config.returnAll) {
        return Promise.resolve({
          data: data.data,
          config,
          msg: data.msg,
          code: data.code,
        })
      }
      else {
        if (config.delay)
          await delay(150)

        return Promise.resolve(data.data)
      }
    case 400004:
    case 200506: // 未绑定学生
      $g.flutter('bindChild')
      $g.user.isBindChild = false
      return Promise.resolve(data.data)
    case 401:
      break
    case 403:
      break
  }
  // 异常处理
  // 若data.msg存在，覆盖默认提醒消息
  let errMsg = `${data && data.msg ? data.msg : CODE_MESSAGE[code] ? CODE_MESSAGE[code] : statusText}`
  let duration = 2000
  if (resetArr.includes(code) && config.isLogin) {
    duration = 3000
    errMsg += ',3s后将跳转至登录页'
    useUserStore().resetAll()
    setTimeout(() => {
      if ($g.isFlutter) {
        $g.flutter('login')
      }
      else if ($g.isPC) {
        const host = import.meta.env.VITE_ZXS_TEACHER
        // pc端并且不为开发环境出现token错误跳转老版智习室老师端
        $g.tool.isPCDevelopment()
          ? router.replace({ name: 'Debugging' })
          : (window.location.href = `${host}`)
      }
    }, 3000)
  }
  if (config.showTip) {
    setTimeout(() => {
      $g.showToast({
        message: errMsg,
        duration,
      })
    }, 200)
  }

  // 使用 Error 对象进行 reject
  return Promise.reject(new Error(errMsg))
}

export default handleData
