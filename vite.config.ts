import process from 'node:process'
import { fileURLToPath, URL } from 'node:url'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { createLogger, defineConfig, loadEnv } from 'vite'

import vueDevTools from 'vite-plugin-vue-devtools'
import { createBuildConfig } from './vite/build'

import { setupVitePlugins } from './vite/plugin'

// 创建自定义logger
const logger = createLogger()
const originalWarn = logger.warn

// 重写warn方法过滤特定警告
logger.warn = (msg, options) => {
  // 忽略auto-fill在IE中不支持的警告
  if (msg.includes('auto-fill') && msg.includes('not supported by IE')) { return }
  originalWarn(msg, options)
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const ENV = loadEnv(mode, process.cwd(), '')
  const version = new Date().getTime()
  return {
    base: '/teacher/',
    server: {
      host: '0.0.0.0',
      open: false,
      port: 5222,
    },
    plugins: [
      vue(),
      vueJsx(),
      vueDevTools(),
      ...setupVitePlugins({ ENV, version }),
    ],
    customLogger: logger,
    build: {
      ...createBuildConfig({ ENV }),
    },
    define: {
      __APP_VERSION__: version,
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '#': fileURLToPath(new URL('./types', import.meta.url)),
      },
    },
    optimizeDeps: {
      include: [
        'xgplayer',
        'xgplayer-flv.js',
        'xgplayer-hls',
        'vue-virtual-scroller',
        'lottie-web/build/player/lottie_light.min',
        'vant/es',
        'vant/es/popup/style/index',
        'vant/es/floating-bubble/style/index',
        'vant/es/config-provider/style/index',
        'vant/es/dialog/style/index',
        'vant/es/field/style/index',
        'vant/es/pull-refresh/style/index',
        'vant/es/loading/style/index',
        'element-plus/es',
        'element-plus/es/components/base/style/css',
        'element-plus/es/components/config-provider/style/css',
        'element-plus/es/components/empty/style/css',
        'element-plus/es/components/button/style/css',
        'element-plus/es/components/progress/style/css',
        'element-plus/es/components/segmented/style/css',
        'element-plus/es/components/input/style/css',
        'element-plus/es/components/upload/style/css',
        'element-plus/es/components/tag/style/css',
        'element-plus/es/components/switch/style/css',
        'element-plus/es/components/loading/style/css',
        '@vant/touch-emulator',
        '@better-scroll/core',
        'element-plus/es/components/radio-group/style/css',
        'element-plus/es/components/radio/style/css',
        'element-plus/es/components/select/style/css',
        'element-plus/es/components/option/style/css',
        'element-plus/es/components/radio-group/style/css',
        'element-plus/es/components/radio/style/css',
        'element-plus/es/components/dropdown/style/css',
        'element-plus/es/components/dropdown-menu/style/css',
        'element-plus/es/components/dropdown-item/style/css',
        'vant/es/list/style/index',
        'vant/es/empty/style/index',
        'element-plus/es/components/date-picker/style/css',
        'element-plus/es/components/dialog/style/css',
        'element-plus/es/components/checkbox/style/css',
        'element-plus/es/components/drawer/style/css',
        'element-plus/es/components/tree/style/css',
        'echarts',
        'element-plus/es/components/checkbox-group/style/css',
        'element-plus/es/components/divider/style/css',
        'fabric',
      ],
    },
    css: {
      preprocessorOptions: {
        scss: {
          sassOptions: {
            silenceDeprecations: ['legacy-js-api'],
          },
        },
      },
    },
  }
})
