<!doctype html>
<html lang="">
  <head>
    <script>
      window._time = new Date().getTime()
    </script>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>教师用户端</title>
  </head>
  <link rel="stylesheet" href="/static/css/loading.css" />
  <link rel="stylesheet" href="/static/css/error.css" />
  <!-- vMdEditor所需资源start -->
  <link
    href="//frontend-cdn.qimingdaren.com/cdn/jquery/katex-v3/katex.min.css"
    rel="stylesheet"
  />
  <script src="https://frontend-cdn.qimingdaren.com/pad/js/mqtt.min.js"></script>

  <!-- vMdEditor所需资源end -->
  <!-- 检测错误提示，放到顶部，捕获页面中后续js加载出现的错误 -->
  <script src="/js/error.js"></script>
  <body>
    <!-- 页面加载初始loading -->
    <div
      class="app-loading"
      id="appLoadingId"
      style="background-color: white; display: none"
    >
      <div class="app-loading-wrap">
        <img src="/static/img/logo.png" class="app-loading-logo" alt="Logo" />
        <div class="app-loading-dots" style="height: 82px">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
      </div>
    </div>
    <div id="app"></div>
    <script>
      // 延迟1秒判断#app是否有内容
      setTimeout(function () {
        // 检查#app元素是否为空
        if (!document.getElementById('app').innerHTML.trim()) {
          let loadingElement = document.getElementById('appLoadingId')
          if (loadingElement) loadingElement.style.display = 'block'
        }
      }, 1000)
    </script>
    <!-- fabric -->
    <!-- <script
      src="https://frontend-cdn.qimingdaren.com/zxs/fabric.min.js"
      async
    ></script> -->
    <script type="module" src="/src/main.ts"></script>
    <!-- mathjax初始配置 -->
    <script>
      window.MathJax = {
        loader: { load: ['[tex]/noerrors'] },
        tex: {
          packages: { '[+]': ['noerrors'] },
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)'],
          ], // ⾏内公式选择符
          displayMath: [
            ['$$', '$$'],
            ['\\[', '\\]'],
          ], // 段内公式选择符
          autoload: {
            upgreek: ['uppi', 'Uppi'],
          },
          processEscapes: true,
        },
        options: {
          enableMenu: false,
          ignoreHtmlClass: 'tex2jax_ignore',
          processHtmlClass: 'tex2jax_process',
        },
      }
    </script>
    <script
      src="https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/mathjax/tex-chtml.js"
      id="MathJax-script"
      async
    ></script>
    <!-- vMdEditor所需资源start -->
    <script src="//frontend-cdn.qimingdaren.com/cdn/jquery/katex-v3/katex.min.js"></script>
    <script src="//frontend-cdn.qimingdaren.com/cdn/jquery/mermaid.min-v2.js"></script>
    <!-- vMdEditor所需资源end -->
  </body>
</html>
