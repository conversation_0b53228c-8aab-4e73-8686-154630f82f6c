<script setup lang="ts">
import { deduplicateByKey } from '../tool'

const studentData = inject<Ref<any>>('studentData', ref({}))

const list: any = $computed(() => {
  if (!studentData.value.classList.length && !studentData.value.specialClass)
    return []

  let deepCloneArr = JSON.parse(JSON.stringify(studentData.value.classList))
  let classList
  let find = deepCloneArr.find(h => h.schoolClassId === studentData.value.specialClass?.schoolClassId)
  if (find) {
    find.selectStudentArr = studentData.value.specialClass.selectStudentArr
    find.arrangeObjectType = 1
    classList = deepCloneArr
  }
  else {
    classList = [...deepCloneArr, ...(studentData.value.specialClass ? [studentData.value.specialClass] : [])]
  }
  // 合并并去重选择的学生
  classList.forEach((item) => {
    item.combineStudent = deduplicateByKey([...(item.selectStudentArr || []), ...(item.selectGroupArr?.flatMap(v => v.list).filter(v => (v.studentJoinSchoolClassIdList)?.includes(item.schoolClassId)) || [])], 'schoolStudentId')
  })
  return classList
})
</script>

<template>
  <div>
    <el-drawer
      v-bind="$attrs"
      size="46.4vw"
      style="background:#F3F4F9;"
    >
      <template #header>
        <div class="text-[#333] text-17px font-600">
          已选择的学生（{{ studentData.selectStudentList.length }}）
        </div>
      </template>
      <div v-if="list.length">
        <div v-for="item in list"
             :key="item.schoolClassId"
             class="   bg-[#FFFFFF] br-[6px] p-17px pb-23px mb-17px"
        >
          <div class="font-600 mb-17px">
            {{ item.sysGradeName }}{{ item.className }}（{{ item.combineStudent.length }}）
          </div>
          <div class="flex flex-wrap bg-[#F3F4F9] br-[6px] gap-x-25px gap-y-13px" style="padding: 13px 13px 15px 13px;">
            <div v-for="student in item.combineStudent"
                 :key="student.schoolClassStudentId"
                 class="w-75px truncate text-[#666666] !font-400"
            >
              {{ student.userName }}
            </div>
          </div>
        </div>
      </div>
      <g-empty v-else></g-empty>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
  padding-bottom: 21px !important;

}
:deep(){
  .el-drawer__body{
    padding-top: 0 !important;
    &::-webkit-scrollbar {
      display: none;
    }
  }

}

:deep(.el-drawer__close-btn) {
  .el-drawer__close {
    display: none;
  }
  &::after {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url(@/assets/img/taskCenter/close.png);
    background-size: contain;
  }
}
</style>
