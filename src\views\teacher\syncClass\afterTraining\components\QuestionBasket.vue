<script setup lang="ts">
import QuestionItem from './QuestionItem.vue'

const props = defineProps({
  questionBasketList: {
    type: Array<any>,
    required: true,
  },
})

const emit = defineEmits(['clearQuestion'])
const offset = ref<any>({
  x: undefined,
  y: 273,
})
let drawerVisible = $ref(false)

function handleClick() {
  drawerVisible = true
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}

function dealQuestion(questionId) {
  let index = props.questionBasketList.findIndex(h => h.questionId === questionId)
  props.questionBasketList.splice(index, 1)
}

function clearQuestion() {
  $g.showConfirmDialog(
    {
      message: '是否清空试题篮？',
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      zIndex: 10000,
    },
  ).then(() => {
    emit('clearQuestion')
  }).catch(() => {})
}

function moveUp(index) {
  if (index > 0) {
    const item = props.questionBasketList.splice(index, 1)[0]
    props.questionBasketList.splice(index - 1, 0, item)
    document.getElementById(`question${index - 1}`)?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    })
    $g.tool.renderMathjax()
  }
}

function moveDown(index) {
  const item = props.questionBasketList.splice(index, 1)[0]
  props.questionBasketList.splice(index + 1, 0, item)
  document.getElementById(`question${index + 1}`)?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
    inline: 'nearest',
  })
  $g.tool.renderMathjax()
}
</script>

<template>
  <div class="question-basket">
    <van-floating-bubble
      v-model:offset="offset"
      class="br-[0] z-[100] !w-[0px] bg-[transparent] h-fit active:opacity-100 !overflow-visible !transition-none"
      teleport="#app"
      @click="handleClick"
    >
      <div class="py-13px bg-[#FFF] shadow-[0_2px_17px_0px_#E3E3E4] text-17px font-500 text-[#333333] br-[2px_0_0_2px]  px-14px flex flex-col items-center">
        <div
          class="w-[26px] h-[26px]  mx-auto flex-shrink-0 bg-center bg-no-repeat bg-contain relative"
          :style="{ backgroundImage: `url(${$g.tool.getFileUrl('syncClass/basket.png')})` }"
        >
          <div v-if="questionBasketList.length > 0" class="absolute top-[-2px] right-[-8px] w-16px h-16px bg-[#FF4D4F] text-white text-12px font-500 rounded-full flex items-center justify-center leading-[12px]">
            {{ questionBasketList.length }}
          </div>
        </div>
        <div class="vertical-text my-9px">
          试题篮
        </div>
        <div
          class="w-[26px] h-[26px]  mx-auto flex-shrink-0 bg-center bg-no-repeat bg-contain"
          :style="{ backgroundImage: `url(${$g.tool.getFileUrl('syncClass/back.png')})` }"
        ></div>
      </div>
    </van-floating-bubble>
    <el-drawer
      v-model="drawerVisible"
      size="46.48vw"
      style="background:#F3F4F9;"
    >
      <template #header>
        <div class="text-[#333] text-17px font-600">
          试题篮（共{{ questionBasketList.length }}题）
        </div>
      </template>
      <div>
        <div v-if="questionBasketList.length" class="flex justify-between items-center">
          <span class="text-[#797979] text-13px font-400">点击上升/下降调整题目在试卷中排序</span>
          <div class="flex items-center text-13px font-500 text-[#6474FD]   van-haptics-feedback" @click="clearQuestion">
            <img :src="$g.tool.getFileUrl('syncClass/clear.png')"
                 alt=""
                 class="w-15px h-15px mr-4px"
            >
            <span class="h-17px lh-[17px]"> 清空试题</span>
          </div>
        </div>
        <QuestionItem
          v-for="(questionItem, index) in questionBasketList"
          :id="`questin${index}`"
          :key="questionItem.id"
          :question-item="questionItem"
          :index="index + 1"
          class="mt-13px"
        >
          <template #footer>
            <div class="flex items-center justify-between mt-11px mb-13px ">
              <div class="flex items-center">
                <div class="flex items-center text-13px mr-17px"
                     :class="index !== 0 && 'van-haptics-feedback '"
                     @click="moveUp(index)"
                >
                  <img :src="index == 0 ? $g.tool.getFileUrl('syncClass/grey-up.png') : $g.tool.getFileUrl('syncClass/purple-up.png')"
                       alt=""
                       class="w-17px h-17px mr-2px"
                  >
                  <span :class="index == 0 ? 'text-[#676A88]' : 'text-[#6474FD]'">上升</span>
                </div>
                <div class="flex items-center text-13px font-500 text-[#6474FD] "
                     :class="index !== questionBasketList.length - 1 && 'van-haptics-feedback '"
                     @click="moveDown(index)"
                >
                  <img :src="index == questionBasketList.length - 1 ? $g.tool.getFileUrl('syncClass/grey-down.png') : $g.tool.getFileUrl('syncClass/purple-down.png')"
                       alt=""
                       class="w-17px h-17px mr-2px"
                  >
                  <span :class="index == questionBasketList.length - 1 ? 'text-[#676A88]' : 'text-[#6474FD]'">下降</span>
                </div>
              </div>

              <div class="flex items-center van-haptics-feedback border w-113px h-30px rounded-[4px] justify-center border-[#FF4646] text-[#FF4646]" @click="dealQuestion(questionItem.questionId)">
                <img :src="$g.tool.getFileUrl('syncClass/minus.png')"
                     alt=""
                     class="w-13px h-13px mr-4px"
                >
                <span class="ml-4px text-15px h-17px lh-[17px]">移除试题篮</span>
              </div>
            </div>
          </template>
        </QuestionItem>
        <g-empty v-if="!questionBasketList.length " class="mt-13px">
        </g-empty>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">
.vertical-text {
  writing-mode: vertical-lr;
  text-orientation:upright;
  white-space: nowrap;
  font-size: 17px;
  letter-spacing: 0.4em;
  font-weight: 500;
 margin-left: -3px;
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
  padding-bottom: 26px !important;

}
:deep(){
  .el-drawer__body{
    padding-top: 0 !important;
    // &::-webkit-scrollbar {
    //   display: none;
    // }
  }

}

:deep(.el-drawer__close-btn) {
  .el-drawer__close {
    display: none;
  }
  &::after {
    content: '';
    display: block;
    width: 15px;
    height: 15px;
    background: url(@/assets/img/taskCenter/close.png);
    background-size: contain;
  }
}
</style>
