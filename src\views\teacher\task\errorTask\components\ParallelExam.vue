<script setup lang="ts">
import { getQuestionRecommendListApi } from '@/api/taskCenter'
import QuestionStemItem from '@/views/teacher/task/questionReport/components/QuestionStemItem.vue'

const props = defineProps({
  bookId: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
})
const typeMap = {
  1: 'ONE',
  2: 'TWO',
  3: 'THREE',
}
let questionList = $ref<any>([])
let showLoading = $ref(true)
function openAnswer(item: any) {
  item.showAnswer = !item.showAnswer
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}
/* 获取题目推荐列表 */
async function getQuestionRecommendList() {
  try {
    showLoading = true
    if (!props.bookId || !props.type) {
      showLoading = false
      return
    }
    const res = await getQuestionRecommendListApi({
      bookId: props.bookId,
      type: typeMap[props.type],
    })
    questionList = res || []
    showLoading = false
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (error) {
    console.log(error)
  }
}
watchDebounced(() => [props.bookId, props.type], () => {
  getQuestionRecommendList()
}, {
  debounce: 150,
  immediate: true,
})
</script>

<template>
  <div>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <template v-else>
      <template v-if="questionList.length">
        <QuestionStemItem
          v-for="(item, index) in questionList"
          :key="item.id"
          :question-item="item"
          :index="index + 1"
        >
          <template #footer>
            <div>
              <div v-if="item.showAnswer" class="mt-13px">
                <div class="flex items-start">
                  <div class="text-[15px] text-[#6474FD] mr-13px flex-shrink-0">
                    【详情】
                  </div>
                  <div class="text-16px text-[#333] pb-18px border-b border-dashed border-[#CCCCCC] ">
                    <div
                      v-for="(v, i) in item?.subQuestions"
                      :key="i"
                      class="flex items-start"
                      :class="{
                        'mt-10px': i !== 0,
                      }"
                    >
                      <div v-if="item?.subQuestions.length > 1" class="mr-5px">
                        ({{ i + 1 }})
                      </div>
                      <g-mathjax :text="v.subQuestionParse" class="text-16px" />
                    </div>
                  </div>
                </div>
                <div class="flex items-start mt-13px">
                  <div class="text-[15px] text-[#6474FD] mr-13px flex-shrink-0">
                    【答案】
                  </div>
                  <div class="text-16px text-[#333] pb-18px   mb-17px">
                    <div
                      v-for="(v, i) in item?.subQuestions"
                      :key="i"
                      class="flex items-start"
                      :class="{
                        'mt-10px': i !== 0,
                      }"
                    >
                      <div v-if="item?.subQuestions.length > 1" class="mr-5px">
                        ({{ i + 1 }})
                      </div>
                      <g-mathjax :text="v.subQuestionAnswer" class="text-16px" />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="flex items-center mt-15px w-100px cursor-pointer"
                @click="openAnswer(item)"
              >
                <svg-menu-eye-close v-if="!item.showAnswer" class="w-17px h-17px"></svg-menu-eye-close>
                <svg-menu-eye-open v-else class="w-17px h-17px"></svg-menu-eye-open>
                <div class="text-15px text-[#6474FD] ml-5px ">
                  答案及解析
                </div>
              </div>
            </div>
          </template>
        </QuestionStemItem>
      </template>
      <g-empty v-else></g-empty>
    </template>
  </div>
</template>

<style lang="scss" scoped>

</style>
