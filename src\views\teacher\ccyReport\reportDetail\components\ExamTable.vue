<script setup lang="ts">
import type { PropType } from 'vue'
import { getActivityStudentCount, getExamStatistics, getExerciseTaskType } from '@/api/activity'

const props = defineProps({
  currentTab: {
    type: String,
    default: '',
  },
  searchData: {
    type: Object,
    default: () => ({}),
  },
  activityId: {
    type: String as PropType<any>,
    default: '',
  },
  sysCourseId: {
    type: String as PropType<any>,
    default: '',
  },
})

// 定义练习任务类型映射
const EXERCISE_TASK_TYPES = {
  weekly: 'STUDENT_TRAIN_WEEKLY_EXAM',
  monthly: 'STUDENT_TRAIN_MONTHLY_EXAM',
  assessment: 'STUDENT_TRAIN_EVALUATE_PAPER,STUDENT_TRAIN_DIAGNOSE_PAPER',
}

// 定义排序字段映射
const SORT_FIELD_MAP = {
  testNum: 'TEST_NUM',
  maxCorrectRate: 'MAX_CORRECT_RATE',
  minCorrectRate: 'MIN_CORRECT_RATE',
}

// 定义导航的标签映射
const TAB_MAP = {
  'STUDENT_TRAIN_WEEKLY_EXAM': 4, // 周考
  'STUDENT_TRAIN_MONTHLY_EXAM': 3, // 月考
  'STUDENT_TRAIN_EVALUATE_PAPER,STUDENT_TRAIN_DIAGNOSE_PAPER': 5, // 评估和诊断试卷
  'PERSONALITY_SIMULATION_TEST_STANDARD_EXAM': 6, // 个性化模拟考试
  'GAOKAO_MOCK': 7, // 高考模拟
} as const

// 表格配置
const tableOptions = reactive<any>({
  ref: null as any,
  loading: true,
  column: [
    {
      label: '姓名',
      prop: 'userName',
    },
    {
      label: '启鸣号',
      prop: 'thirdUniqueId',
    },
    {
      label: '测验次数',
      prop: 'testNum',
      sort: true,
    },
    {
      label: '最高正确率',
      prop: 'maxCorrectRate',
      sort: true,
      formatter: (row: any) => `${Math.round(row.maxCorrectRate * 100)}%`,
    },
    {
      label: '最低正确率',
      prop: 'minCorrectRate',
      sort: true,
      formatter: (row: any) => `${Math.round(row.minCorrectRate * 100)}%`,
    },
    {
      label: '操作',
      prop: 'cz',
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})

// 响应式变量
let keyword = $ref<string>('')
let statisticsCount = $ref<Record<string, any>>({})
let sortType = $ref<any>(null)
let sort = $ref<any>(null)

const router = useRouter()
const route = useRoute()
let form = reactive<any>({
  testType: '', // 测试类型
  simulateType: '', // 模拟类型

})
let testType = $ref<Array<{
  label: string
  value: string
}>>([

])// 测试类型
const simulateType = $ref<Array<{
  label: string
  value: string
}>>([
  {
    label: '标准模考',
    value: 'standardTest',
  },
  // {
  //   label: '个性化模考',
  //   value: 'simulate',
  // },
])// 模拟类型
// 格式化日期范围用于API请求

function getFormattedDateRange() {
  return {
    beginDateTime: props.searchData.beginDateTime,
    endDateTime: props.searchData.endDateTime,
  }
}

/* 跳转详情 */
function toDetail(row: any) {
  const dateRange = getFormattedDateRange()

  router.push({
    name: 'ModuleStatistics',
    query: {
      tabValue: TAB_MAP[form.testType],
      activityId: props.activityId,
      userName: row.userName,
      thirdUniqueId: row.thirdUniqueId || row.accountName,
      schoolStudentId: row.schoolStudentId,
      accountId: row.accountId,
      sysCourseId: props.sysCourseId,
      beginDateTime: dateRange.beginDateTime,
      endDateTime: dateRange.endDateTime,
      source: route?.query?.source ?? null,
    },
  })
}

/* 获取学生数量统计 */
async function fetchStudentStatistics() {
  try {
    const dateRange = getFormattedDateRange()
    console.log(props.searchData)
    const params = {
      activityId: props.activityId,
      sysCourseId: props.sysCourseId,
      schoolId: props.searchData.schoolId,
      sysGradeId: props.searchData.sysGradeId,
      schoolClassId: props.searchData.schoolClassId === 'all' ? null : props.searchData.schoolClassId,
      beginDateTime: dateRange.beginDateTime,
      endDateTime: dateRange.endDateTime,
    }

    const res = await getActivityStudentCount(params)
    statisticsCount = res
  }
  catch (err) {
    console.log('获取学生统计数据失败', err)
  }
}

/* 获取周考、月考统计 */
async function fetchExamStatistics() {
  try {
    if (!props.searchData.schoolId || !props.activityId || !props.searchData.sysGradeId) {
      tableOptions.loading = false
      return
    }
    tableOptions.loading = true
    const dateRange = getFormattedDateRange()

    const params = {
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.pageSize,
      activityId: props.activityId,
      sysCourseId: props.sysCourseId,
      schoolId: props.searchData.schoolId,
      sysGradeId: props.searchData.sysGradeId,
      schoolClassId: props.searchData.schoolClassId,
      exerciseTaskTypes: form?.testType,
      beginDateTime: dateRange.beginDateTime,
      endDateTime: dateRange.endDateTime,
      sortType,
      sort,
      keyword,
    }

    const res = await getExamStatistics(params)
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
    tableOptions.loading = false
  }
  catch (err) {
    console.log(err)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
    tableOptions.loading = false
  }
}

/* 排序 */
async function sortChange({
  order,
  prop,
}) {
  sortType = SORT_FIELD_MAP[prop]
  sort = order ? order.toUpperCase() : null
  await fetchExamStatistics()
}

/* 搜索 */
async function search() {
  tableOptions.pageOptions.page = 1
  await fetchExamStatistics()
}
// 切换测试类型
async function changeTestType(val: any) {
  keyword = ''
  tableOptions.pageOptions.page = 1
  // fetchStudentStatistics()
  fetchExamStatistics()
}
async function getExerciseTaskTypeList() {
  try {
    const res = await getExerciseTaskType()
    testType = res?.map((item: any) => ({
      label: item?.exerciseTaskTypeName ?? '',
      value: item?.exerciseTaskType ?? '',
    }))
    form.testType = testType[0].value
  }
  catch (err) {
    console.log(err)
  }
}

// 监听搜索数据变化
watchDebounced(
  () => [props.searchData, props.currentTab],
  async ([_, tab], oldValue) => {
    if (!oldValue || tab !== oldValue[1]) {
      keyword = ''
      sortType = null
      sort = null
    }
    tableOptions.pageOptions.page = 1
    fetchStudentStatistics()
    await getExerciseTaskTypeList()
    await fetchExamStatistics()
  },
  {
    debounce: 150,
    immediate: true,
  },
)
</script>

<template>
  <div class="bg-white p-[17px] rounded-[6px]">
    <div>
      <el-form :model="form"
               label-width="auto"
               class=" !text-[#333] !text-[15px]"
      >
        <el-form-item label="测试类型：">
          <el-radio-group v-model="form.testType" @change="changeTestType">
            <el-radio
              v-for="item in testType"
              :key="item.value"
              class="!text-[#333] !text-[15px]"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="模拟类型：">
          <el-radio-group v-model="form.simulateType">
            <el-radio
              v-for="item in simulateType"
              :key="item.value"
              class="!text-[#333] !text-[15px]"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
    </div>
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="text-[17px] font-600">
          学生列表
        </div>
        <div class="ml-10px text-[14px]">
          开通活动人数：<span class="text-[#65CD64]">{{ statisticsCount.openedStudentNum ?? 0 }}</span>人,
          有使用数据的总计 <span class="text-[#65CD64]">{{ tableOptions.pageOptions.total ?? 0 }}</span>人,
          下方表格仅展示<span class="text-[#FF4646]">有使用数据</span>的学生
        </div>
      </div>
      <div>
        <el-input
          v-model="keyword"
          clearable
          style="width: 181px"
          placeholder="输入学生姓名/启鸣号"
        />
        <el-button color="#6474FD"
                   class="w-64px ml-5px cursor-pointer"
                   @click="search"
        >
          搜索
        </el-button>
      </div>
    </div>
    <div class="text-[#636772] text-14px mt-13px">
      更新时间：实时统计
    </div>
    <g-table
      :key="currentTab"
      :table-options="tableOptions"
      @change-page="fetchExamStatistics"
      @sort-change="sortChange"
    >
      <template #cz="{ row }">
        <div class="text-[#6474FD] van-haptics-feedback" @click="toDetail(row)">
          查看
        </div>
      </template>
    </g-table>
  </div>
</template>

<style lang="scss" scoped>
:deep() {

  .el-form-item{
    margin-bottom: 10px!important;
  }
  .el-dialog__header{
    padding-bottom: 0px!important;
  }
  .el-form-item__label{
    font-weight: 400;
    font-size: 15px;
    color: #333;
  }
}
</style>
