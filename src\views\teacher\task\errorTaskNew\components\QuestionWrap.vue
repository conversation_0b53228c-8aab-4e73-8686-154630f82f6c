<script setup lang="ts">
import AnswerParse from './AnswerParse.vue'
import Statistics from './Statistics.vue'
import VariantCard from './VariantCard.vue'

const props = defineProps({
  questionItem: {
    required: true,
    type: Object,
  },
  index: {
    type: Number,
  },
  examId: {
    type: Number,
    required: true,
  },
  sysCourseId: {
    type: Number,
    required: true,
  },
  examPaperId: {
    type: Number,
    required: true,
  },
  bookId: {
    type: String,
    required: true,
  },
  jztSysCourseId: {
    type: Number,
    required: true,
  },
})
const cardItem: any = $computed(() => {
  return {
    ...(props.questionItem ?? {}),
    subQuestionList: props.questionItem.subQuestions?.map((h, i) => {
      return {
        statistics: props.questionItem.scoreStatistics.length == 1
          ? props.questionItem.scoreStatistics[0]
          : props.questionItem.scoreStatistics[i],
        ...h,
        optionArr: Object.keys(h)
          .filter(
            key => key.includes('option') && h[key] && key !== 'optionNumbers',
          )
          .map((realKey) => {
            return {
              name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
              title: h[realKey].toString(),
            }
          }),
      }
    }),
  }
})
let expand = $ref(false)
let hasExpand = $ref(false)
let maxHeight = $ref(300)
let observer: any = null
const contentRef = $ref<any>()

async function init() {
  observer?.disconnect?.()
  observer = null
  await nextTick()
  $g.tool.renderMathjax()
  observer = new ResizeObserver(() => {
    hasExpand = contentRef?.scrollHeight > maxHeight
    if (hasExpand) {
      observer?.disconnect?.()
      observer = null
    }
  })
  observer.observe(contentRef)
}

watch(() => props.questionItem, (val) => {
  if (val)
    init()
}, {

  immediate: true,
})
let rate = $ref<any>(95)
const colorMap = {
  difficult: 'rgba(255,70,70,0.1)',
  medium: 'rgba(100,116,253,0.1)',
  general: 'rgba(250,173,20,0.1)',
  easy: 'rgba(82,196,26,0.1)',
  default: '#eee',
}
function getColor(rate: number) {
  if (!rate && rate != 0)
    return colorMap.default

  if (rate < 60)
    return colorMap.difficult

  if (rate < 70)
    return colorMap.medium

  if (rate < 85)
    return colorMap.general

  if (rate <= 100)
    return colorMap.easy
}
let currentSubIndex = $ref(0)
function selectQues(item: any, index: number) {
  currentSubIndex = index
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}
const currentSubItem = $computed(() => {
  return cardItem.subQuestionList[currentSubIndex]
})
const typeMap = {
  1: '难',
  2: '中',
  3: '易',
}
const questionType = $computed(() => {
  let arr = [] as any
  cardItem.variationalStudentList.forEach((v) => {
    if (v.studentList.length) {
      arr.push({
        label: `变式题 (${typeMap[v.type]})`,
        value: v.type,
      })
    }
  })
  return arr
})
let viewData = $ref<any>(false)
let currentType = $ref<any>(null)
/* 点击变式题和答案解析答题统计 */
function handleQuestionType(key: any) {
  if (currentType != key) {
    currentType = key
    viewData = true
  }
  else {
    viewData = false
    currentType = null
  }
}
const answer = $computed(() => {
  if ([1,
2,
3].includes(props.questionItem.scoreStatistics[0].type))
    return currentSubItem.subQuestionAnswer

  return ''
})
</script>

<template>
  <div class="mb-17px">
    <div class="bg-white px-17px br-[9px] overflow-hidden pb-17px">
      <div ref="contentRef"
           class="overflow-hidden pt-17px"
           :style="{ maxHeight: expand ? 'unset' : `${maxHeight}px` }"
      >
        <div class="flex items-center">
          <div
            class="px-5px h-24px rounded-[5px] text-center leading-[24px] text-[17px] text-[#393939] "
            :style="{ backgroundColor: getColor(cardItem.classRate) }"
          >
            {{ cardItem.questionNumber }}
          </div>
          <div class="text-[16px] text-[#333] mx-8px">
            {{ cardItem.sysQuestionTypeName ?? '-' }}
          </div>
          <div
            class="text-[15px] text-[#393939] leading-[21px] px-6px rounded-[5px]"
            :style="{ backgroundColor: getColor(cardItem.classRate) }"
          >
            大题得分率：
            <span class="text-[#FF4646]">{{ (cardItem.classRate || cardItem.classRate == 0) ? cardItem.classRate : '-' }}</span><span v-if="cardItem.classRate || cardItem.classRate == 0">%</span>
          </div>
        </div>
        <!-- 大题题干 -->
        <g-mathjax
          v-if="cardItem.questionTitle"
          :text="cardItem.questionTitle"
          class="flex-shrink-0 mb-12px text-16px"
          :font-width="16"
        />
      </div>
      <div>
      </div>

      <div v-if="hasExpand"
           class="text-[#6474FD] text-15px flex items-center mt-11px cursor-pointer pl-16px"
           @click="expand = !expand"
      >
        <span class="mr-6px">{{ expand ? '收起' : '展开查看更多' }}</span>
        <img :src="expand ? $g.tool.getFileUrl('taskCenter/blueTop.png') : $g.tool.getFileUrl('taskCenter/blueBottom.png')"
             alt=""
             class="w-15px h-9px"
        >
      </div>
      <!-- 题号选择 -->
      <div v-if="cardItem.subQuestionList?.length > 1" class="flex items-center my-14px">
        <div v-for="(item, index) in cardItem.subQuestionList" :key="index">
          <div
            class="w-[43px] mr-17px h-43px rounded-[50%] text-center leading-[43px] border border-solid border-[transparent] mb-5px text-15px  cursor-pointer active:opacity-80 select-none"
            :class="{
              'bg-[rgba(255,70,70,0.1)]  bg-1':
                (60 > item.statistics?.classRate),
              '!bg-[rgba(100,116,253,0.1)]  bg-3':
                (60 <= item.statistics?.classRate
                  && item.statistics?.classRate < 70),
              '!bg-[rgba(250,173,20,0.1)]  bg-2':
                (70 <= item.statistics?.classRate
                  && item.statistics?.classRate < 85),
              '!bg-[rgba(82,196,26,0.1)]  bg-4':
                (85 <= item.statistics?.classRate),
              '!bg-[#eee]  bg-5':
                (cardItem.scoreStatistics.length == 1),
              'item-active': currentSubIndex === index,
            }"
            @click.stop="selectQues(item, index)"
          >
            {{ item.questionIndex ?? index + 1 }}
          </div>
        </div>
      </div>
      <g-mathjax :text="currentSubItem.subQuestionTitle" class="text-16px" />
      <div
        v-for="(item, subIndex) in currentSubItem.optionArr"
        :key="item.name"
        class="flex items-center  text-15px"
        :class="[subIndex !== currentSubItem.optionArr.length - 1 && 'mb-12px',
                 questionItem.questionTitle && currentSubItem.subQuestionTitle ? 'pl-32px' : 'pl-16px']"
      >
        <div class="flex-shrink-0">
          {{ item.name }}.
        </div>
        <g-mathjax :text="item.title" class="text-15px"></g-mathjax>
      </div>

      <div class="myDashed mt-17px"></div>
      <div class="flex items-center text-[#666] text-15px my-14px">
        <div>满分：<span class="text-[#FF4646]">{{ cardItem.scoreStatistics.length == 1 ? cardItem.score : currentSubItem.score }}</span>分</div>
        <div class="w-1px h-11px bg-[#ccc] mx-13px"></div>
        <div>
          班级均分/得分率：
          <span class="text-[#FF4646]">{{ currentSubItem.statistics.classScore }}</span>分/ <span class="text-[#FF4646]">{{ currentSubItem.statistics.classRate }}</span>%
        </div>
        <div class="w-1px h-11px bg-[#ccc] mx-13px"></div>
        <div>
          年级均分/得分率：
          <span class="text-[#FF4646]">{{ currentSubItem.statistics.gradeScore }}</span>分/ <span class="text-[#FF4646]">{{ currentSubItem.statistics.gradeRate }}</span>%
        </div>
      </div>
      <div class="flex items-center my-13px">
        <div class="mr-13px flex-shrink-0">
          知识点
        </div>
        <div class="flex items-center flex-1 flex-wrap">
          <template v-if="currentSubItem?.knowledgePoints?.length">
            <div
              v-for="item in currentSubItem?.knowledgePoints"
              :key="item.sysKnowledgePointId"
              class="text-[#6C6C74] text-[13px] p-4px bg-[#F4F5FA] rounded-[5px] mr-4px mb-4px"
            >
              {{ item.sysKnowledgePointName }}
            </div>
          </template>
          <div v-else>
            -
          </div>
        </div>
      </div>
      <div class="flex items-center my-13px">
        <div class="mr-13px flex-shrink-0">
          能力点
        </div>
        <div class="flex items-center flex-1 flex-wrap">
          <template v-if="currentSubItem?.subjectAbilityPoint?.length">
            <div
              v-for="item in currentSubItem?.subjectAbilityPoint"
              :key="item"
              class="text-[#6C6C74] text-[13px] p-4px bg-[#F4F5FA] rounded-[5px] mr-4px mb-4px"
            >
              {{ item }}
            </div>
          </template>
          <div v-else>
            -
          </div>
        </div>
      </div>
      <!-- 按钮组 -->
      <div class="flex items-center justify-between mb-7px mt-10px">
        <div class="flex items-center">
          <div
            v-for="val in questionType"
            :key="val.value"
            class="mr-17px button-item"
            :class="{
              'button-item-active-question': currentType === val.value,
            }"
            @click="handleQuestionType(val.value)"
          >
            {{ val.label }}
          </div>
        </div>
        <div class="flex items-center">
          <div
            class="mr-17px button-item"
            :class="{
              'button-item-active': currentType === 'answer',
            }"
            @click="handleQuestionType('answer')"
          >
            答案及解析
          </div>
          <div
            class="button-item"
            :class="{
              'button-item-active': currentType === 'statistics',
            }"
            @click="handleQuestionType('statistics')"
          >
            答题统计
          </div>
        </div>
      </div>
      <!-- 变式题、数据统计相关 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            v-for="val in questionType"
            :key="val.value"
            class="mr-17px w-[115px]"
          >
            <svg-menu-icon-top v-if="currentType === val.value" class="mx-auto w-16px h-11px text-[#F3F4F9]" />
          </div>
        </div>
        <div class="flex items-center">
          <div class="w-115px mr-17px">
            <svg-menu-icon-top v-if=" currentType === 'answer'" class="mx-auto  w-16px h-11px text-[#F3F4F9]" />
          </div>
          <div class="w-115px">
            <svg-menu-icon-top v-if="currentType == 'statistics'" class="mx-auto  w-16px h-11px text-[#F3F4F9]" />
          </div>
        </div>
      </div>
      <div v-if="viewData" class="bg-[#F3F4F9] rounded-[5px] p-17px">
        <!-- 变式题 -->
        <VariantCard
          v-if="[1, 2, 3].includes(currentType)"
          :type="currentType"
          :book-id="bookId"
          :question-id="cardItem.questionId"
          :variational-student-list="cardItem.variationalStudentList"
          :structure-number="cardItem.structureNumber"
          :sys-course-id="jztSysCourseId"
          :knowledge-points="cardItem.knowledgePoints"
          :question-item="questionItem"
        />
        <!-- 答案解析 -->
        <AnswerParse v-if="currentType === 'answer'" :sub-question="currentSubItem" />
        <!-- 答题统计 -->
        <Statistics
          v-if="currentType === 'statistics'"
          :exam-id="examId"
          :sys-course-id="sysCourseId"
          :question-id="cardItem.questionId"
          :exam-paper-id="examPaperId"
          :score-statistics="cardItem.scoreStatistics"
          :structure-number="cardItem.structureNumber"
          :answer="answer"
          :current-sub-index="currentSubIndex"
        />
      </div>
      <slot name="footer" :question-item="questionItem"></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.button-item{
  width: 115px;
  height: 43px;
  line-height: 43px;
  text-align: center;
  border-radius: 5px;
  background-color: #F3F4F9;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid transparent;
}
.button-item-active{
  background-color: #6474FD;
  color: #fff;
}
.button-item-active-question{
  background-color: #ECEFFF;
  color: #6474FD;
  border-color: #646AB4 !important;
}
.myDashed{
  height: 1px;
  background: linear-gradient(
    to left,
    transparent 0%,
    transparent 50%,
    #ccc 50%,
    #ccc 100%
);
background-size: 10px 1px;
background-repeat: repeat-x;

}
.item-active {
  &.bg-1 {
    border: 1px solid #FF4646!important;
  }
  &.bg-2 {
    border: 1px solid #faad14!important;
  }
  &.bg-3 {
    border: 1px solid #6474FD!important;
  }
  &.bg-4 {
    border: 1px solid #52c41a!important;
  }
  &.bg-5 {
    border: 1px solid #80a0c5!important;
  }
}
:deep(){
  .g-mathjax {
    & > :first-child::before {
      color: #999999;
      }
  }
}
</style>
