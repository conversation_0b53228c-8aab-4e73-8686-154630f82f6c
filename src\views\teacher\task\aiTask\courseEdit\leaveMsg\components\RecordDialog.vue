<script lang="ts" setup>
import OSS from '@/plugins/OSS'
import { useRecorder } from './useRecorder'
import WaveAnimation from './WaveAnimation.vue'

const emits = defineEmits(['receiveMsg'])
const showDialog = defineModel<boolean>('show')
const oss = new OSS()
const {
  isRecording,
  startRecording,
  stopRecording,
  resetRecording,
} = useRecorder()
let recongizing = $ref(false)

async function record() {
  recongizing = !recongizing
  if (recongizing) {
    await startRecording()
  }
  else {
    const audioBlob: any = await stopRecording()
    if (audioBlob.size <= 0)
      $g.showToast('未检测到说话')

    const duration = await getAudioDuration(audioBlob)
    console.log(audioBlob)
    const fileName = `audio_${Date.now()}.wav`
    const result = await oss.uploadFile({
      file: audioBlob,
      id: fileName,
      name: fileName,
      size: audioBlob.size,
    })
    if (result.fullUrl || result.resource_url) {
      emits('receiveMsg', {
        url: result.fullUrl || result.resource_url,
        duration,
      })
    }
    reset()
  }
}

function getAudioDuration(file) {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file)
    const audio = document.createElement('audio')
    audio.src = url
    audio.addEventListener('loadedmetadata', () => {
      resolve(Number.parseInt(audio.duration as any))
      URL.revokeObjectURL(url)
    })
    audio.addEventListener('error', (e) => {
      reject('音频加载失败')
      URL.revokeObjectURL(url)
    })
  })
}

function reset() {
  showDialog.value = false
  resetRecording()
}
</script>

<template>
  <el-dialog v-model="showDialog">
    <div class="p-10px flex flex-col justify-center items-center">
      <WaveAnimation v-if="isRecording" />
      <div class="w-68px h-68px bg-[#6474FD] rounded-full flex items-center justify-center cursor-pointer mt-100px" @click="record">
        <svg-ri-mic-line v-if="!isRecording" class="text-white text-30px" />
        <svg-ri-pause-line v-else class="text-white text-30px" />
      </div>
    </div>
  </el-dialog>
</template>
