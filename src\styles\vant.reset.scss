:root{
  --van-overlay-background: rgba(0, 0, 0, 0.5);
}

/* button按钮 */
.van-button {
  &.van-button--primary {
    background: var(--van-button-primary-background);
  }
  &.van-button--success {
    background: var(--van-button-success-background);
  }
  &.van-button--warning {
    background: var(--van-button-warning-background);
  }
  &.van-button--danger {
    background: var(--van-button-danger-background);
  }
  &.van-button--plain {
    background: var(--van-white);
  }
}

/* 导航栏 */
.van-nav-bar {
  .van-nav-bar__left {
    .van-icon-arrow-left {
      color: #333333 !important;
      font-size: 20px;
    }
  }
  &.van-hairline--bottom:after {
    display: none !important;
  }
}

/* 选择器picker */
.van-picker {
  --van-border-color: #e8e8e8;
  .van-picker__title {
    font-size: 14px !important;
    font-weight: 400;
  }
  .van-picker__columns {
    // height: 200px !important;
  }
  .van-picker__toolbar {
    height: 55px;
    button {
      padding: 20px 30px;
      height: 55px;
      line-height: 15px;
    }
  }
  .van-picker-column__item {
    padding: 0 24px;
    font-size: 14px;
    line-height: 18px;
    color: #666666;
  }
}

/* 占位图 */
.van-empty__description {
  position: relative !important;
  top: -20px !important;
}

/* 无边距-popover气泡 */
.no-padding-popover {
  .van-popover__action {
    padding: 0 !important;
  }
  .van-popover__action-text {
    font-size: 12px !important;
  }
}

/* 调用$g.showConfirmDialog方法时，加此类名，可以覆盖默认样式
 $g.showConfirmDialog({
    title: '确认对答案?',
    className: 'global-custom-van-dialog'
    ...
  }
*/
.global-custom-van-dialog {
  min-width: 360px;
  padding-bottom: 26px;
  background: linear-gradient(#ffe6d6 0%, #ffffff 30%) !important;
  --van-dialog-font-size: 19px;
  // title上内边距
  --van-dialog-header-padding-top: 18px;
  --van-dialog-radius: 12px;
  // 消息体上内边距
  --van-dialog-has-title-message-padding-top: 18px;
  --van-dialog-has-title-message-text-color: #333333;
  // 按钮
  --van-button-radius: 8px;
  --van-dialog-button-height: 43px;
  --van-dialog-button-margin-right: 13px;

  --van-border-width: 0;

  --van-footer-padding: 12px 34px 0px;
  .van-dialog__footer {
    padding: var(--van-footer-padding);

    button {
      font-weight: 500;
      border-radius: var(--van-button-radius) !important;
    }
    .van-dialog__cancel {
      background: rgba(204, 204, 204, 0.6);
      color: #666666;
      margin-right: var(--van-dialog-button-margin-right);
    }
    .van-dialog__confirm {
      background: #ff7d29;
      color: white;
    }
  }
}

/* 提示框 */
.van-toast {
    z-index: 3000 !important;
}
