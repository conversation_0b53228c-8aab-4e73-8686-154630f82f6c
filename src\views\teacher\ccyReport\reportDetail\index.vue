<script setup lang="ts" name="ReportDetail">
import {
  getActivitySubjectList,
  getClassList,
  getGradeList,
  getSchoolList,
  getStatisticsModule,
} from '@/api/activity'
import ChapterReport from './chapterReport/index.vue'
import ExamTable from './components/ExamTable.vue'
import TabBar from './components/TabBar.vue'
import { shortcuts } from './constant'
import CorrectionReport from './correctionReport/index.vue'
import StageTest from './stageTest/index.vue'
import StudentReport from './studentReport/index.vue'

const route = useRoute()
let activeTab = $ref<any>('1')
let schoolId = $ref<any>('')
let schoolList = $ref<any>([])
let gradeList = $ref<any>([])
let gradeId = $ref<any>('')
let classList = $ref<any>([{
  label: '全部',
  value: 'all',
}])
let classId = $ref('')
let dateRange = $ref<any>(['', ''])
let subjectList = $ref<any>([])
let statisticsCount = $ref<any>({})
let activedReportTab = $ref<string>('chapter') // 报告tab
let chapterReportList = $ref<Array<any>>([])
let chapterReportListLoading = $ref<boolean>(true)
let excelName = $computed(() => {
  let schoolName = schoolList.find(v => v.value == schoolId)?.label ?? ''
  let gradeName = gradeList.find(v => v.value == gradeId)?.label ?? ''
  let className = classList.find(v => v.value == classId)?.label ?? ''
  return `${schoolName}-${gradeName}-${className}`
})
const tabMenu = [
  {
    label: '章节测验报告',
    value: 'chapter',
  },
  {
    label: '学生报告',
    value: 'student',
  },
  {
    label: '测试数据',
    value: 'testData',
  },

  // {
  //   label: '月考',
  //   value: 'monthly',
  // },
  // {
  //   label: '周考',
  //   value: 'weekly',
  // },
  // {
  //   label: '评估与诊断',
  //   value: 'assessment',
  // },
  {
    label: '阶段测试',
    value: 'stageTest',
  },
  {
    label: '订正报告',
    value: 'correctionReport',
  },

]

let searchData = $computed(() => {
  return {
    activeTab,
    schoolId,
    sysGradeId: gradeId,
    schoolClassId: classId == 'all' ? null : classId,
    beginDateTime: dateRange?.[0] || dateRange?.[0] != 'Invalid Date' ? dateRange?.[0] : '',
    endDateTime: dateRange?.[1] || dateRange?.[1] != 'Invalid Date' ? dateRange?.[1] : '',
  }
})

const selectStyle = $computed(() => {
  return $g.isPC ? 'w-256px' : 'w-135px'
})
const title = $computed<any>(() => {
  return route.query.activityName ?? ''
})
let activityId = $ref<any>(null)
onBeforeMount(async () => {
  activityId = route.query.activityId
  await fetchSubjectList()
  await fetchSchoolList()
})
/* 获取学科 */
async function fetchSubjectList() {
  try {
    let res = await getActivitySubjectList({
      activityId,
    })
    subjectList = res?.map((v) => {
      return {
        ...v,
        label: v.sysSubjectName,
        key: v.sysCourseId,
      }
    }) || []
    activeTab = res.length ? res[0].sysCourseId : ''
  }
  catch (err) {
    console.log('获取学科失败', err)
  }
}
/* 获取学校 */
async function fetchSchoolList() {
  try {
    if (!activityId || !activeTab) {
      chapterReportListLoading = false
      return
    }
    let res = await getSchoolList({
      activityId,
      sysCourseId: activeTab,
    })
    schoolList = res?.map((v) => {
      return {
        ...v,
        label: v.schoolName,
        value: v.schoolId,
      }
    }) || []
    if (!schoolId || !schoolList.find(v => v.value == schoolId))
      schoolId = schoolList.find(v => v.defaultSelected)?.value

    if (schoolId)
      await fetchGradeList()

    else
      chapterReportListLoading = false
  }
  catch (err) {
    chapterReportListLoading = false
    console.log('获取学校失败', err)
  }
}
/* 获取年级 */
async function fetchGradeList(isReset = false) {
  try {
    if (!schoolId || !activityId || !activeTab) {
      chapterReportListLoading = false
      return
    }
    let res = await getGradeList({
      schoolId,
      activityId,
      sysCourseId: activeTab,
    })
    gradeList = res?.map((v) => {
      return {
        ...v,
        label: v.sysGradeName,
        value: v.sysGradeId,
      }
    }) || []
    gradeId = isReset || !gradeId || !gradeList.find(v => v.value == gradeId)
      ? gradeList?.[0]?.value || ''
      : gradeId
    await fetchClassList(isReset)
  }
  catch (err) {
    chapterReportListLoading = false
    console.log('获取年级失败', err)
  }
}
/* 获取班级 */
async function fetchClassList(isReset = false) {
  try {
    if (!schoolId || !activityId || !activeTab || !gradeId) {
      chapterReportListLoading = false
      chapterReportList = []
      return
    }
    let res = [] as any
    res = await getClassList({
      activityId,
      schoolId,
      sysGradeId: gradeId,
      sysCourseId: activeTab,
    })
    classList = res.length
      ? res?.map((v) => {
        return {
          ...v,
          label: v.className,
          value: v.schoolClassId || 'all',
        }
      })
      : classList
    classId = isReset || !classId || !classList.find(v => v.value == classId) ? 'all' : classId
    getList()
  }
  catch (err) {
    chapterReportListLoading = false
    console.log('获取班级失败', err)
  }
}
/* 切换学科 */
async function handleChange() {
  try {
    // 切换科目时，所有筛选项不变化
    initData()
    await fetchSchoolList()
  }
  catch (err) {
    console.log(err)
  }
}
const showBack = $computed(() => {
  return !route.query['token-origin']
})
function initData() {
  chapterReportList = []
  chapterReportListLoading = false
}
// 切换年级
function changeGrade(id) {
  gradeId = id
  fetchClassList()
}
// 切换班级
async function changeClass(id) {
  classId = id
  await getList()
}
// 切换日期
const changDateRange = useDebounceFn(async (val) => {
  if (val?.[0] == 'Invalid Date' || val?.[1] == 'Invalid Date')
    dateRange = ['', '']

  else
    dateRange = val || ['', '']

  setDefaultSelected()
  await getList()
}, 100)
// 设置日期选择器选中样式
function setDefaultSelected() {
  // shortcuts 数据格式化
  const tempDateList = shortcuts.map(item => ({
    ...item,
    value: item.value(),
  }))
  // 全部时返回【'Invalid Date'，'Invalid Date'】
  const isInvalidDate = dateRange?.[0] === 'Invalid Date' || dateRange?.[1] === 'Invalid Date'
  const isEmptyDate = dateRange?.[0] == '' || dateRange?.[1] == ''
  // 对应的选中index
  const selectedItemIndex = isInvalidDate || isEmptyDate
    ? 0
    : tempDateList.findIndex(
        v => v.value[0] == $g.dayjs(dateRange?.[0]).format('YYYY-MM-DD HH:mm') && v.value[1] == $g.dayjs(dateRange?.[1]).format('YYYY-MM-DD HH:mm'),
      )
  const sidebarDom = document.querySelector('.el-picker-panel__sidebar')
  if (sidebarDom?.children) {
    Array.from(sidebarDom.children).forEach((child: any, index) => {
      const isSelected = index === selectedItemIndex
      child.style.backgroundColor = isSelected ? '#F5F7FF' : ''
      child.style.color = isSelected ? '#6474FD' : ''
    })
  }
}
// 日期选择器显示隐藏
function handleVisibleChange(visible) {
  if (visible)
    setDefaultSelected()
}
// 切换章节报告和学生报告tab
async function changeTab(val) {
  activedReportTab = val
  await getList()
}
// 请求章节报告数据或者学生报告数据
async function getList() {
  if (['weekly',
'monthly',
'assessment'].includes(activedReportTab))
    return

  if (!schoolId || !gradeId || !classId || !activeTab) {
    chapterReportListLoading = false
    return
  }
  if (activedReportTab == 'chapter')
    getStatisticsModuleApi()
}
// 章节测验报告
async function getStatisticsModuleApi() {
  try {
    if (!schoolId || !gradeId || !classId || !activeTab) {
      chapterReportListLoading = false
      chapterReportList = []
      return
    }
    chapterReportListLoading = true
    let params: any = {
      activityId,
      sysCourseId: activeTab,
      schoolId,
      sysGradeId: gradeId,
      schoolClassId: classId == 'all' ? null : classId,
    }
    if (dateRange?.[0] && dateRange?.[1]) {
      params = {
        ...params,
        beginDateTime: $g.dayjs(dateRange?.[0]).format('YYYY-MM-DD HH:mm:ss'),
        endDateTime: $g.dayjs(dateRange?.[1]).format('YYYY-MM-DD HH:mm:ss'),
      }
    }
    let res = await getStatisticsModule(params)
    chapterReportList = res || []
    chapterReportListLoading = false
  }
  catch (err) {
    chapterReportListLoading = false
    console.log(err)
  }
}
</script>

<template>
  <div class="px-26px pt-26px">
    <g-navbar v-if="showBack"
              :title="title"
              class="mb-17px"
    ></g-navbar>
    <div class="h-[calc(100vh-27px-17px-34px)]  rounded-[6px]  ">
      <div class="relative no-bar h-[calc(100%-23px)] " :class="$g.isPC ? '' : 'overflow-auto'">
        <!-- 筛选条件 -->
        <div class="bg-white p-[17px] rounded-[6px]">
          <TabBar
            v-model:active-tab="activeTab"
            class="mb-[17px]"
            :data="subjectList"
            @handle-change="handleChange"
          />
          <div class="flex items-center justify-between  mb-17px">
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-13px">
                学校
              </div>
              <el-select v-model="schoolId"
                         :class="selectStyle"
                         @change="fetchGradeList(true)"
              >
                <el-option
                  v-for="item in schoolList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>

            <div class="flex items-center">
              <div class="flex-shrink-0 mr-13px">
                时间范围
              </div>
              <el-date-picker
                v-model="dateRange"
                :class="$g.isPC ? 'w-400px' : 'w-360px'"
                type="datetimerange"
                :shortcuts="shortcuts"
                :teleported="false"
                clearable
                :editable="false"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                unlink-panels
                @change="changDateRange"
                @visible-change="handleVisibleChange"
              />
            </div>
          </div>
          <div>
            <div class="flex items-top mb-[17px] ">
              <div class="w-fit  font-[600] text-[15px] text-[#333]  mr-[10px] leading-[30px]">
                年级
              </div>
              <div class="flex flex-wrap flex-1">
                <div
                  v-for="item in gradeList"
                  :key="item"
                  class="min-w-[49px]  px-[12px] h-[30px]   rounded-[5px] cursor-pointer mr-[8px] text-[15px] text-[#6C6C74] leading-[30px] text-center"
                  :class="{ 'bg-[#ECEFFF] !text-[#6474FD]': gradeId === item.value }"
                  @click="changeGrade(item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <div class="flex items-top ">
              <div class="w-fit  font-[600] text-[15px] text-[#333]  mr-[10px] leading-[30px]">
                班级
              </div>
              <div class="flex flex-wrap flex-1">
                <div
                  v-for="item in classList"
                  :key="item"
                  class="min-w-[49px]  px-[11px]  h-[30px]  rounded-[5px] cursor-pointer mr-[8px] text-[15px] leading-[30px] text-[#6C6C74]  text-center"
                  :class="{ 'bg-[#ECEFFF] !text-[#6474FD]': classId === item.value }"
                  @click="changeClass(item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex gap-[26px] my-[17px] ">
          <div
            v-for="item in tabMenu"
            :key="item.value"
            class="text-[15px] font-[600] text-[#6C6C74] leading-[21px] cursor-pointer"
            :class="{ '!text-[#333] activedTab': activedReportTab == item.value }"
            @click="changeTab(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
        <!-- 章节测验报告 -->
        <template v-if="activedReportTab == 'chapter'">
          <g-loading v-if="chapterReportListLoading" class="h-200px"></g-loading>
          <ChapterReport
            v-else
            :chapter-report-list="chapterReportList"
            :search-data="searchData"
          />
        </template>

        <!-- 学生报告 -->
        <div v-else-if="activedReportTab == 'student'" class="bg-white p-[17px] rounded-[6px]">
          <StudentReport :search-data="searchData"
                         :subject-list="subjectList"
                         :excel-name="excelName"
          />
        </div>
        <!-- 阶段测试 -->
        <template v-else-if="activedReportTab == 'stageTest'">
          <StageTest
            :school-id="schoolId"
            :grade-id="gradeId"
            :class-id="classId"
            :date-range="dateRange"
            :sys-course-id="activeTab"
            :sys-subject-id="subjectList.find(v => v.sysCourseId == activeTab)?.sysSubjectId"
            :sys-subject-name="subjectList.find(v => v.sysCourseId == activeTab)?.label"
          />
        </template>
        <!-- 测试数据 -->
        <template v-else-if="activedReportTab == 'testData'">
          <ExamTable
            :statistics-count="statisticsCount"
            :current-tab="activedReportTab"
            :search-data="searchData"
            :activity-id="activityId"
            :sys-course-id="activeTab"
          />
        </template>
        <!-- 订正报告 -->
        <template v-else-if="activedReportTab == 'correctionReport'">
          <CorrectionReport
            :search-data="searchData"
            :subject-list="subjectList"
            :excel-name="excelName"
            :sys-course-id="activeTab"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  thead {
    height: 34px !important;

    th {
      background-color: #F5F5F5;
    }
  }
}
.activedTab{
  position: relative;
  padding-bottom: 9px;
  &::after{
    display: inline-block;
    content: '';
    position: absolute;
    bottom: 0px;
    left: 0;
    width:100%;
    height: 1px;
    border: 1px solid #6474FD;
    border-radius: 2px;
  }
}
</style>
