<script setup lang="ts">
import { getTeacherRemindApi } from '@/api/taskCenter'

const props = defineProps({
  reportAll: {
    type: Object,
    required: true,
  },
  params: {
    type: Object,
    required: true,
  },
})
const reportInfo: any = $computed(() => props.reportAll?.studentsReport || {})
const route = useRoute()
const router = useRouter()
const isActivity = $computed(() => (route.query.exerciseSourceType as any) == 1)

const columns = [
  {
    prop: 'studentName',
    label: '姓名',
  },
  {
    prop: 'schedule',
    label: '进度',
    sort: true,
    formatter(row) {
      if (row.schedule == null) return '-'
      return `${row.schedule}%`
    },
  },
  {
    prop: 'answerNum',
    label: '答题数',
    sort: true,
  },
  {
    prop: 'correctRate',
    label: '正确率',
    sort: true,
    slot: true,
  },
  {
    prop: 'answerTime',
    label: '学习用时',
    sort: true,
    formatter(row) {
      const {
        h,
        m,
        s,
      } = formatTime(row.answerTime)
      if (h) return `${h}时${m}分${s}秒`
      if (m) return `${m}分${s}秒`
      return `${s}秒`
    },
  },
  {
    prop: 'finishTime',
    label: '完成时间',
    sort: true,
    width: '158px',
  },
  {
    prop: 'errorList',
    label: route.query.exerciseSourceType == '1' ? '错题重做情况' : '错题订正情况',
    headerSlot: true,
    width: '167px',
    slot: true,
  },
  {
    prop: 'state',
    label: '自查状态',
    slot: true,
  },
  {
    prop: 'teacherState',
    label: '教师批改状态',
    slot: true,
  },
  {
    prop: 'cz',
    label: '答题结果',
    slot: true,
  },
]

const tableOptions = reactive<any>({
  ref: null as any,
  key: '',
  loading: false,
  column: [],
  data: [],
})
let showLoading = $ref(true)
watch(() => reportInfo.studentList, () => {
  try {
    showLoading = true
    setTimeout(() => {
      tableOptions.data = reportInfo.studentList
      tableOptions.data.sort((a, b) => b.answerNum - a.answerNum)
      tableOptions.column = reportInfo.configCorrect === 1 ? columns : columns.filter(v => v.prop !== 'teacherState')
      showLoading = false
    }, 500)
  }
  catch (err) {
    showLoading = false
  }
}, {
  immediate: true,
})

function formatTime(seconds) {
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  const s = seconds % 60
  return {
    h,
    m,
    s,
  }
}
// 单位秒，处理成时分秒
function dealTime(seconds) {
  const {
    h,
    m,
    s,
  } = formatTime(seconds)
  if (h)
    return `<span class="text-26px">${h}</span>时<span class="text-26px">${m}</span>分<span class="text-26px">${s}</span>秒`

  if (m)
    return `<span class="text-26px">${m}</span>分<span class="text-26px">${s}</span>秒`

  return `<span class="text-26px">${s}</span>秒`
}

async function remind() {
  await getTeacherRemindApi({
    taskId: route.query.exerciseSourceId,
    type: 1,
    schoolStudentIds: reportInfo.noLearnStudentList.map(v => v.schoolStudentId),
  })
  $g.showToast('提醒成功')
}

function goReport(row) {
  router.push({
    name: 'ReportQuestionList',
    query: {
      ...route.query,
      exerciseTaskId: row.exerciseTaskId,
      studentName: row.studentName,
      state: row.state,
      ...props.params,
    },
  })
}
function sortChange(val) {
  if (val.order == 'asc') {
    if (val.prop == 'finishTime') {
      tableOptions.data.sort((a, b) => Date.parse(a.finishTime) - Date.parse(b.finishTime))
      return
    }
    tableOptions.data.sort((a, b) => Number(a[val.prop]) - Number(b[val.prop]))
  }
  else if (val.order == 'desc') {
    if (val.prop == 'finishTime') {
      tableOptions.data.sort((a, b) => Date.parse(b.finishTime) - Date.parse(a.finishTime))
      return
    }
    tableOptions.data.sort((a, b) => Number(b[val.prop]) - Number(a[val.prop]))
  }
}
</script>

<template>
  <div class="flex flex-col h-full">
    <div class=" bg-[#FFFFFF] br-[9px] pt-17px pl-17px pb-15px">
      <div class="text-15px font-600 mb-12px">
        任务概况
      </div>
      <div class="flex pr-[5vw]">
        <div class="flex items-center  flex-1">
          <div>
            <div class="text-13px h-36px lh-[36px] mb-5px">
              <span class="text-26px text-[#6474FD] ">{{ reportInfo.completeNum ?? 0 }}</span>
              <span v-if="!isActivity">/</span>
              <span v-if="!isActivity">{{ reportInfo.totalNum ?? 0 }}</span>
            </div>
            <div class="text-13px text-[#929296] h-18px lh-[18px]">
              完成人数
            </div>
          </div>
          <div class="flex-1 flex justify-center">
            <div class="w-1px h-26px bg-[#CCCCCC] "></div>
          </div>
        </div>
        <div>
        </div>
        <div class="flex items-center  flex-1">
          <div>
            <div class="text-13px h-36px lh-[36px] mb-5px">
              <span class="text-26px ">{{ reportInfo.avgSchedule }}</span>
              <span>%</span>
            </div>
            <div class="text-13px text-[#929296] h-18px lh-[18px]">
              平均进度
            </div>
          </div>
          <div class="flex-1 flex justify-center">
            <div class="w-1px h-26px bg-[#CCCCCC]"></div>
          </div>
        </div>
        <div class="flex items-center  flex-1">
          <div>
            <div class="text-13px h-36px lh-[36px] mb-5px">
              <div v-html="dealTime(reportInfo.avgTime)"></div>
              <!-- <span class="text-26px">{{ reportInfo.avgTime }}</span>
              <span>%</span> -->
            </div>
            <div class="text-13px text-[#929296] h-18px lh-[18px]">
              平均学习用时
            </div>
          </div>
          <div class="flex-1 flex justify-center">
            <div class="w-1px h-26px bg-[#CCCCCC]"></div>
          </div>
        </div>
        <div class="flex items-center  flex-1">
          <div>
            <div class="text-13px h-36px lh-[36px] mb-5px">
              <span class="text-26px ">{{ reportInfo.avgAnswerNum }}</span>
              <span>题</span>
            </div>
            <div class="text-13px text-[#929296] h-18px lh-[18px]">
              平均答题数
            </div>
          </div>
          <div class="flex-1 flex justify-center">
            <div class="w-1px h-26px bg-[#CCCCCC]"></div>
          </div>
        </div>
        <div class="flex items-center" :class="!isActivity && 'flex-1'">
          <div>
            <div class="text-13px h-36px lh-[36px] mb-5px">
              <span class="text-26px ">{{ reportInfo.avgCorrectRate }}</span>
              <span>%</span>
            </div>
            <div class="text-13px text-[#929296] h-18px lh-[18px]">
              平均正确率
            </div>
          </div>
          <div v-if="!isActivity" class="flex-1 flex justify-center">
            <div class="w-1px h-26px bg-[#CCCCCC] "></div>
          </div>
        </div>
        <div v-if="!isActivity" class="flex items-center  ">
          <div>
            <div class="flex items-end">
              <div class="text-13px h-36px lh-[36px] mb-5px ">
                <span class="text-26px text-[#6474FD]">{{ reportInfo.noLearnNum }}</span>
                <span>人</span>
              </div>
              <el-popover
                :popper-style="{ padding: '15px 11px' }"
                placement="bottom"
                :width="350"
                :trigger="reportInfo.noLearnStudentList?.length ? 'hover' : 'contextmenu'"
                :teleported="false"
                :popper-options="{
                  modifiers: [
                    {
                      name: 'offset',
                      options: {
                        offset: [-64, 12],
                      },
                    },
                  ],
                }"
              >
                <template #reference>
                  <img
                    :src="$g.tool.getFileUrl('taskCenter/blue-info.png')"
                    alt=""
                    class="w-16px h-16px mb-10px ml-2px "
                    :class="reportInfo.noLearnStudentList?.length && 'cursor-pointer'"
                  >
                </template>
                <div>
                  <el-scrollbar max-height="180px" always>
                    <div class="flex flex-wrap gap-x-[11px] gap-y-[11px] ">
                      <div v-for="item in reportInfo.noLearnStudentList"
                           :key="item.schoolStudentId"
                           class="bg-[#F3F4F9] br-[4px]  h-26px lh-[26px] text-15px w-71px flex-cc px-2px"
                      >
                        <div class="truncate">
                          {{ item.studentName }}
                        </div>
                      </div>
                    </div>
                  </el-scrollbar>
                  <div class="flex justify-end mt-8px">
                    <el-button type="primary"
                               class="h-26px"
                               @click="remind"
                    >
                      一键提醒
                    </el-button>
                  </div>
                </div>
              </el-popover>
            </div>
            <div class="text-13px text-[#929296] h-18px lh-[18px]">
              未学习人数
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="p-17px br-[6px] bg-[#FFFFFF] mt-17px flex-1">
      <div class="text-15px font-600 mb-13px">
        学生列表
      </div>
      <g-loading v-if="showLoading" class="h-200px"></g-loading>

      <g-table
        v-else
        :table-options="tableOptions"
        :default-sort="{ prop: 'answerNum', order: 'descending' }"
        :border="false"
        :lazy="true"
        stripe
        @cell-click="() => {
          return
        }"
        @sort-change="sortChange"
      >
        <template #header-errorList="{ column }">
          <div class="flex-cc">
            <span>{{ column.label }}</span>
            <el-popover
              placement="top"
              trigger="hover"
              width="284"
              :popper-options="{
                modifiers: [
                  {
                    name: 'offset',
                    options: {
                      offset: [-100, 12],
                    },
                  },
                ],
              }"
            >
              <template #reference>
                <img :src="$g.tool.getFileUrl('taskCenter/blue-info.png')"
                     alt=""
                     class="w-16px h-16px  ml-2px cursor-pointer mb-2px z-10"
                >
              </template>
              <div style="font-size: 15px;">
                <span style="color: #52C41A;">绿色</span>代表此题已{{ isActivity ? '重做' : '订正' }}，<span style="color:#F5222D">红色</span>代表未{{ isActivity ? '重做' : '订正' }}
              </div>
            </el-popover>
          </div>
        </template>
        <template #correctRate="{ row }">
          <div v-if="row.correctRate != null" :class="row.correctRate < 50 ? 'text-[#F5222D]' : 'text-[#52C41A]'">
            {{ row.correctRate }}%
          </div>
          <div v-else>
            -
          </div>
        </template>
        <template #errorList="{ row }">
          <div v-if="row.errorList?.length">
            <span v-for="(errItem, index) in row.errorList"
                  :key="errItem.questionId"
                  :class="errItem.correctiveState == 1 ? 'text-[#F5222D]' : 'text-[#52C41A]'"
            >
              {{ errItem.qnum }} <span v-if="index !== row.errorList.length - 1">,</span>
            </span>
          </div>
          <div v-else>
            -
          </div>
        </template>
        <template #state="{ row }">
          <div v-if="[5].includes(row.state)" class="text-[#52C41A]">
            已自查
          </div>
          <div v-else-if="[3, 4].includes(row.state)" class="text-[#FAAD14]">
            自查中
          </div>
          <div v-else>
            --
          </div>
        </template>
        <template #teacherState="{ row }">
          <template v-if="[5].includes(row.state)">
            <div v-if="[1].includes(row.teacherState)" class="text-[#F2494A]">
              未批改
            </div>
            <div v-else-if="[2].includes(row.teacherState)" class="text-[#3398F7]">
              批改中
            </div>
            <div v-else-if="[3].includes(row.teacherState)" class="text-[#00CF6A]">
              已批改
            </div>
          </template>
          <div v-else>
            --
          </div>
        </template>
        <template #cz="{ row }">
          <el-button
            text
            class="text-13px"
            :class="![1, 2, null].includes(row.state) ? 'text-[#6474FD]' : 'text-[#929296]'"
            :disabled="[1, 2, null].includes(row.state)"
            @click="goReport(row)"
          >
            查看
          </el-button>
        </template>
      </g-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
// :deep(){
//   .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
//     background: #EFF1FE !important;
//   }
// }
:deep(){
  .el-table {

.el-table__body {

  // 取消悬浮高亮
  tr:hover>td.el-table__cell {
    background-color: #fff !important;
  }

  // 取消点击后的高亮
  tr.current-row>td.el-table__cell {
    background-color: #fff !important;
  }
}
}
}
</style>
