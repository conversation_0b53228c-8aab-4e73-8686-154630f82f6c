<script setup lang="ts">
import * as fabric from 'fabric'
import { onMounted, ref } from 'vue'

const canvasRef = useTemplateRef('canvas')

onMounted(() => {
  if (!canvasRef.value) return
  const canvas = new fabric.Canvas(canvasRef.value, {
    isDrawingMode: true,
  })

  canvas.freeDrawingBrush = new fabric.PencilBrush(canvas)
})
</script>

<template>
  <div class="canvas-container">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<style scoped>
.canvas-container {
  border: 1px solid #ccc;
  margin: 20px;
}

canvas {
  border: 1px solid #ddd;
}
</style>`
