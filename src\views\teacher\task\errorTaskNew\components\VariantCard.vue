<script setup lang="ts">
import { getBookQuestionRecommendStatusApi } from '@/api/taskCenter'
import QuestionChangeDialog from './QuestionChangeDialog/index.vue'
import VariantQuestion from './VariantQuestion.vue'

const props = defineProps({
  type: {
    type: Number || String as PropType<number | string>,
    required: true,
  },
  questionId: {
    type: String as PropType<any>,
    required: true,
  },
  bookId: {
    type: String as PropType<any>,
    required: true,
  },
  variationalStudentList: {
    type: Array as PropType<any[]>,
    required: true,
  },
  knowledgePoints: {
    type: Array as PropType<any[]>,
    required: true,
  },
  structureNumber: {
    type: [String, Number] as PropType<string | number>,
    required: true,
  },
  sysCourseId: {
    type: [String, Number] as PropType<string | number>,
    required: true,
  },
  questionItem: {
    type: Object as PropType<any>,
    required: true,
  },
})
// 难度
const difficulty = {
  1: {
    label: '难',
    range: '(70%-99%)',
    value: 'ONE',
  },
  2: {
    label: '中',
    range: '(30%-70%)',
    value: 'TWO',
  },
  3: {
    label: '易',
    range: '(0%-30%)',
    value: 'THREE',
  },
}
let questionData = $ref<any>({})
let showLoading = $ref(true)
/* 获取变式题 */
async function getVariantQuestion() {
  try {
    showLoading = true
    const res = await getBookQuestionRecommendStatusApi({
      bookId: props.bookId,
      questionId: props.questionId,
      parallelPaperType: difficulty[props.type].value,
    })
    setTimeout(() => {
      showLoading = false
      questionData = res.length ? res[0] : {}
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    }, 200)
  }
  catch (err) {
    showLoading = false
    questionData = {}
    console.log('获取变式题失败', err)
  }
}
watchDebounced(
  () => props.type,
  async () => {
    questionData = {}
    showLoading = true
    await getVariantQuestion()
  },
  {
    immediate: true,
    debounce: 100,
  },
)
const currentStudentList = $computed(() => {
  return props.variationalStudentList[props.type - 1]
})

let showDialog = $ref(false)

function changeQuestion() {
  showDialog = true
  console.log('换题')
}
</script>

<template>
  <div>
    <!-- 换题、难度 -->
    <div class="flex items-center justify-between">
      <div class="text-[15px] text-[#6474FD] flex items-center cursor-pointer" @click="changeQuestion">
        <svg-menu-change class="w-16px h-16px mr-6px" />
        <span>我要换题</span>
      </div>
      <div class="flex items-center text-[15px] text-[#666666]">
        <div>当前题目难度为 <span class="text-[#FF4646]">【{{ difficulty[type].label }}】</span></div>
        <div class="w-1px h-11px bg-[#ccc] mx-13px"></div>
        <div>适合考试原题题目得分率<span class="text-[#FF4646]">{{ difficulty[type].range }}</span>的学生</div>
      </div>
    </div>
    <!-- 学生信息 -->
    <div class="text-[15px] text-[#666666] mt-17px">
      将做此变式题的学生有:{{ currentStudentList.studentList.length ? currentStudentList.studentList.map(v => v.studentName).join('、') : '-' }}
      <div class="myDashed my-17px"></div>
    </div>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <template v-else>
      <!-- 变式题 -->
      <div v-if="!$g.tool.isTrue(questionData)" class="flex items-center justify-center">
        <div>
          <img src="@/assets/img/taskCenter/variantEmpty.png"
               class="w-128px h-95px  mx-auto"
               alt=""
          >
          <div class="text-[15px] text-[#666666] text-center mt-10px">
            暂未查询到变式题，您可以通过 <span class="text-[#6474FD] cursor-pointer" @click="changeQuestion">【我要换题】</span> 按钮手动选择变式题
          </div>
        </div>
      </div>
      <VariantQuestion v-else :question-item="questionData" />
    </template>

    <!-- 换题弹窗 -->
    <QuestionChangeDialog
      v-model:show="showDialog"
      :question-id="questionId"
      :book-id="bookId"
      :difficulty="difficulty[type].label"
      :knowledge-point="knowledgePoints"
      :structure-number="structureNumber"
      :parallel-paper-type="difficulty[type].value"
      :sys-course-id="sysCourseId"
      :question-item="questionItem"
      :variant-question-id="questionData.questionId"
      @get-variant-question="getVariantQuestion"
    />
  </div>
</template>

<style lang="scss" scoped>
.myDashed{
  height: 1px;
  background: linear-gradient(
    to left,
    transparent 0%,
    transparent 50%,
    #ccc 50%,
    #ccc 100%
);
background-size: 10px 1px;
background-repeat: repeat-x;
}
</style>
