<script setup lang="ts">
const emits = defineEmits(['confirm'])
const showDialog = defineModel<boolean>()
const text = defineModel<string>('text', { default: '' })

function confirm() {
  emits('confirm')
  showDialog.value = false
}
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    round
    duration="0.1"
    :close-on-click-overlay="false"
    class="p-16px max-h-[80vh] w-[400px]"
  >
    <div class="w-full flex items-center justify-between">
      <span class="text-17px text-[#333] font-600">
        编辑
      </span>
      <img
        src="@/assets/img/taskCenter/close.png"
        class="w-17px h-17px select-none van-haptics-feedback"
        @click="showDialog = false"
      >
    </div>
    <el-input
      v-model="text"
      type="textarea"
      :rows="5"
      class="my-20px"
    />
    <div class="w-full flex justify-end">
      <el-button @click="showDialog = false">
        取消
      </el-button>
      <el-button type="primary" @click="confirm">
        确定
      </el-button>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>

</style>
