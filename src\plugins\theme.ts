import { useCssVar } from '@vueuse/core'

export interface ThemeColors {
  primary?: string
  success?: string
  warning?: string
  error?: string
  info?: string
}

// 默认主题配置
const defaultTheme = {
  primary: '#6474FD',
  success: '#00be70',
  warning: '#e6a23c',
  error: '#FF4646',
  info: '#909399',
}

// 颜色处理工具函数
function generateTintColor(color: string, tint: number): string {
  let red = Number.parseInt(color.slice(1, 3), 16)
  let green = Number.parseInt(color.slice(3, 5), 16)
  let blue = Number.parseInt(color.slice(5, 7), 16)

  red = Math.min(255, Math.round(red + (255 - red) * tint))
  green = Math.min(255, Math.round(green + (255 - green) * tint))
  blue = Math.min(255, Math.round(blue + (255 - blue) * tint))

  return `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`
}

function generateDarkColor(color: string, shade: number): string {
  let red = Number.parseInt(color.slice(1, 3), 16)
  let green = Number.parseInt(color.slice(3, 5), 16)
  let blue = Number.parseInt(color.slice(5, 7), 16)

  red = Math.max(0, Math.round(red * (1 - shade)))
  green = Math.max(0, Math.round(green * (1 - shade)))
  blue = Math.max(0, Math.round(blue * (1 - shade)))

  return `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`
}

// 为每种颜色设置所有必要的 CSS 变量
function setColorVariables(colorName: string, color: string) {
  const el = document.documentElement

  useCssVar(`--el-color-${colorName}`, el).value = color

  const lightColors = [3,
5,
7,
8,
9]
  lightColors.forEach((level) => {
    const tint = level / 10
    useCssVar(`--el-color-${colorName}-light-${level}`, el).value =
      generateTintColor(color, tint)
  })

  useCssVar(`--el-color-${colorName}-dark-2`, el).value = generateDarkColor(
    color,
    0.2,
  )
}

// 主题初始化函数
export function initTheme(customTheme: ThemeColors = {}) {
  const theme = {
    ...defaultTheme,
    ...customTheme,
  }
  const el = document.documentElement

  // 设置所有主题色
  Object.entries(theme).forEach(([name, color]) => {
    setColorVariables(name, color)
  })
  // Vant 主题色映射
  useCssVar('--van-primary-color', el).value = theme.primary
  useCssVar('--van-success-color', el).value = theme.success
  useCssVar('--van-warning-color', el).value = theme.warning
  useCssVar('--van-danger-color', el).value = theme.error
  useCssVar('--van-info-color', el).value = theme.info

  // Vant 主色衍生色
  useCssVar('--van-primary-color-light', el).value = generateTintColor(
    theme.primary,
    0.7,
  )
  useCssVar('--van-primary-color-dark', el).value = generateDarkColor(
    theme.primary,
    0.2,
  )
}

export { defaultTheme }
