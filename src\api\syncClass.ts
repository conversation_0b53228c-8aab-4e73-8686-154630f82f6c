import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config

/** =========================同步课堂开始==================== */

/* 教师试卷学段年级 */
export function getSyncClassGrade(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/grade`, data)
}

/* 教师学科 select：教师所教授的所有科目（包括行政班、教学班） */
export function getSyncClassSubjectList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/subjectSelect`, data)
}

/* 班级列表 */
export function getSyncClassList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/schoolClass`, data)
}

/* 书籍列表 */
export function getSyncClassBookList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/bookPage`, data)
}

/* 章节列表 */
export function getSyncClassChapterList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/bookCatalogClassTree`, data)
}

/* 章节课堂内容 */
export function getSyncClassChapterContent(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/bookCatalog/content`, data)
}

/* 章节内容排序 */
export function setSyncClassResourceSort(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/classroom/bookCatalog/content/sort`, data)
}

/* 删除数据 */
export function delSyncClassResource(data?) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/classroom/resource/delete`, data)
}

/* 左侧章节目录排序保存 */
export function saveTreeDataSort(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/classroom/saveTreeSort`, data)
}

/* 数据同步 */
export function syncClassResourceToOther(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/classroom/sync/classResource`, data)
}

/* 知识精讲 */
export function getKnowledgeExplainList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/resource/plateOne`, data)
}

/* 实战演练 */
export function getKnowledgePracticeList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/resource/plateTwo`, data)
}

/** =========================同步课堂结束==================== */

/** =========================课程学习开始==================== */

/* 课程学习列表 */
export function getCourseLearningList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/bookVideoSchoolList`, data)
}

/* 预览视频 */
export function getVideoResource(data) {
  return request.get(`${VITE_JZT_API}/admin/videoResource/videoPlayUrl`, data)
}

/* 上传视频 */
export function uploadVideo(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/book/video/save`, data)
}

/** =========================课程学习结束==================== */

/** =========================知识卡片开始==================== */

/* 知识卡片列表 */
export function getKnowledgeList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/bookArticleSchoolList`, data)
}

/* 获取选中状态 */
export function getCheckedList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/bind/resourceIdList`, data)
}

/* 预览文件 */
export function getKnowledgeResource(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/article/details`, data)
}

/* 保存勾选卡片 */
export function saveCheckedItems(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/classroom/bindResource`, data)
}

/** =========================知识卡片结束==================== */

/** ==========================课后训练开始==================== */

// 获取原卷试题
export function getOringinList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/originQuestionList`, data)
}

// 获取换一批列表
export function getChangeList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/changeQuestion`, data)
}

// 保存试题篮
export function saveQuestionBasket(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/classroom/bindQuestion`, data)
}

// 获取分层训练题目列表
export function getLayerList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/classroom/layeredExerciseDetail`, data)
}
/** ==========================课后训练结束==================== */
