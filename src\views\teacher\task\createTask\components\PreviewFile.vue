<script setup lang="ts">
const props = defineProps({
  currentFile: {
    type: Object as PropType<any>,
    required: true,
  },
})
let showLoading = $ref(false)
let showDialog = defineModel<boolean>('show')

/* 计算文件类型 */
const calFileType = $computed(() => {
  const imgType = ['jpg',
'png',
'jpeg',
'heic',
'heif',
'gif',
'webp']
  const videoType = ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8']
  const audioType = ['mp3',
'ogg',
'wav',
'aac']
  const fileType = ['doc',
'docx',
'xls',
'xlsx',
'pdf',
'ppt',
'pptx']

  if (videoType.includes(props.currentFile?.fileExtension) || $g.tool.isTrue(props.currentFile.fileAbsoluteUrlM3u8))
    return 'video'

  if (imgType.includes(props.currentFile?.fileExtension))
    return 'img'

  if (audioType.includes(props.currentFile?.fileExtension))
    return 'audio'

  if (fileType.includes(props.currentFile?.fileExtension))
    return 'file'

  return 'default'
})
const calSrc = $computed(() => {
  if (calFileType !== 'file')
    return props.currentFile.fileAbsoluteUrl ?? props.currentFile.fileAbsoluteUrlM3u8

  return $g.tool.getWeb365Url(props.currentFile?.fileAbsoluteUrl)
})

watch(
  () => props.currentFile,
  () => {
    showLoading = true
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="w-[900px] h-[500px] py-30px px-[30px] overflow-hidden bg-[#FFFFFF] br-[12px]"
    teleport="#app"
    v-bind="$attrs"
  >
    <div class="w-full h-full relative">
      <div v-if="calFileType == 'file'" class="w-full h-full">
        <g-loading v-show="showLoading" class="h-200px"></g-loading>
        <iframe
          v-show="!showLoading"
          :key="props.currentFile?.fileAbsoluteUrl"
          :src="calSrc"
          frameborder="0"
          class="w-full h-full"
          @load="showLoading = false"
        ></iframe>
      </div>
      <template v-else-if="calFileType == 'video'">
        <g-video :url="calSrc" class="w-full h-full" />
      </template>
      <div v-else-if="calFileType == 'img'" class="h-full overflow-auto">
        <img
          :src="calSrc"
          alt=""
          srcset=""
          class="object-contain"
        >
      </div>
      <div v-else>
        暂不支持该文件类型预览！
      </div>
      <img src="@/assets/img/taskCenter/close.png"
           class="w-18px h-18px absolute top-[-20px] right-[-20px] cursor-pointer"
           @click="showDialog = false"
      >
    </div>
  </van-popup>
</template>

<style lang="scss" scoped></style>
