import { AnswerType } from './type'

/**
 * 获取当前题目的学生作答答案
 * 不同的题型和不同的作答方式，取的答案字段不一样
 */
export function getStudentAnswer(subQuestion) {
  const {
    isObjective,
    subQuestionExerciseInfo: {
      answer,
      answerType,
      whiteBoard = null,
      keybord = null,
      image = null,
    },
  } = subQuestion

  let studentAnswer = ''
  // 选择题取answer字段
  if (isObjective) {
    studentAnswer = answer
  }
  else {
    switch (answerType) {
      case AnswerType.SUBJECTIVE_CANVAS:
        studentAnswer = whiteBoard
        break
      case AnswerType.SUBJECTIVE_KEYBOARD:
        studentAnswer = keybord
        break
      case AnswerType.SUBJECTIVE_PHOTO:
        studentAnswer = image
        break
    }
  }
  return studentAnswer
}
