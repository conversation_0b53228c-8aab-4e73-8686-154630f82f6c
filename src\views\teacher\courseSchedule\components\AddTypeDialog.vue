<script setup lang="ts">
import AddMemoDialog from './AddMemoDialog.vue'

let props = defineProps({
  currentWeek: {
    type: Number,
    default: () => null,
  },
  currentApiParam: {
    type: Object,
    default: () => {},
  },
})
let emit = defineEmits(['confirm'])
const show = defineModel<boolean>('show')
let currentType = $ref<any>(null)
let showAdd = $ref<any>(false)
const router = useRouter()
const route = useRoute()
function confirm() {
  const {
    sysSubjectId,
    beginTime,
    endTime,
    sysSubjectName,
    timetableDate,
    timetableTimeIndex,
    timetableTimeType,
  } = props.currentApiParam
  if (!currentType) {
    $g.showToast('请选择类型')
    return
  }

  if (currentType == 1) {
    router.push({
      name: 'CreateAiTask',
      query: {
        isCourseSchedule: 'true',
        subjectId: sysSubjectId,
        dateStart: beginTime,
        dateEnd: endTime,
        subjectName: sysSubjectName,
        timetableDate,
        timetableTimeIndex,
        timetableTimeType,
      },
    })
  }
  else {
    showAdd = true
  }

  emit('confirm', currentType)
}
const refreshTable = inject('refreshTable') as any
watch(() => show.value, () => {
  if (show.value)
    currentType = null

  else
    refreshTable()
})
</script>

<template>
  <div>
    <el-dialog
      v-model="show"
      class="w-[400px] !px-0"
      align-center
      :show-close="false"
      center
    >
      <div
        class="flex relative w-full text-[17px] font-500 mb-[26px] justify-center items-center"
      >
        类型选择<img
          class="w-[15px] cursor-pointer absolute right-[26px] h-[15px]"
          src="@/assets/img/courseSchedule/close.png"
          @click="show = false"
        />
      </div>
      <div class="flex justify-center mb-[20px]">
        <div
          v-if="!currentApiParam.task?.taskName"
          class="w-[133px] br-[4px] border cursor-pointer h-[67px] mr-[26px]"
          :class="
            currentType == 1 ? 'border-theme-primary' : 'border-[transparent]'
          "
        >
          <img
            class="w-full h-full"
            src="@/assets/img/courseSchedule/addTask.png"
            @click="currentType = 1"
          />
        </div>
        <div
          v-if="!currentApiParam.existMemo"
          :class="
            currentType == 2 ? 'border-theme-primary' : 'border-[transparent]'
          "
          class="w-[133px] br-[4px] border cursor-pointer h-[67px]"
        >
          <img
            class="w-full h-full"
            src="@/assets/img/courseSchedule/addMemo.png"
            @click="currentType = 2"
          />
        </div>
      </div>
      <template #footer>
        <div>
          <el-button
            class="bg-[#999999]/[0.1048] mr-[64px] w-[75px] h-[30px]"
            @click="show = false"
          >
            取消
          </el-button>
          <el-button
            class="w-[75px] h-[30px]"
            type="primary"
            @click="confirm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <AddMemoDialog
      v-model:show="showAdd"
      :current-api-param="currentApiParam"
    ></AddMemoDialog>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dialog) {
  border-radius: 12px !important;
}
</style>
