import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config

/** ========================批改页面开始====================== */

// 获取可以批改的学生列表
export function getStudentList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/correction/studentList`, data, { delay: false })
}

// 获取题目详情列表
export function getQuestionList(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/exercise/report/questionList`, data)
}

// 获取学生作答+批改详情
export function getStudentAnswerDetail(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/correction/questionResult`, data, { replace: true })
}

// 教师批改结果提交
export function submitTeacherCorrect(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/correction/check`, data, {
    delay: false,
    replace: true,
  })
}
