<script setup lang="ts">
import { checkPad, getClassList, getGradeList, getStatus, getStudentList, kickOff, lockScreen, resetPassword, unLockScreen } from '@/api/teachingTools'

const filterFormData = reactive<any>({
  options: {
    stageList: [], // 学段列表
    gradeList: [], // 年级列表
    classList: [], // 班级列表
  },
  data: {
    periodLevel: null, // 学段id
    gradeId: null, // 年级id
    classId: null, // 班级id
  },
})
let pcList = $ref<any[]>([]) // pc学生列表
let padList = $ref<any[]>([]) // pad学生列表
let offLineList = $ref<any[]>([]) // 离线学生列表
let showLoading = $ref<boolean>(true) // 加载状态
let lockStatus = $ref<any>(null)
let showDialog = $ref<boolean>(false)
let showInfoDialog = $ref<boolean>(false)
let showTipDialog = $ref<boolean>(false)
let btnLoading = $ref(false)
let resetLoading = $ref(false)
let kickOffLoading = $ref(false)
let currentIdNum = $ref<any>(null)
let currentFlag = $ref<any>(false)
let lockOption = $ref([
  {
    label: '5分钟',
    value: 5,
  },
  {
    label: '10分钟',
    value: 10,
  },
  {
    label: '15分钟',
    value: 15,
  },
  {
    label: '20分钟',
    value: 20,
  },
  {
    label: '25分钟',
    value: 25,
  },
  {
    label: '30分钟',
    value: 30,
  },
])
let infoList = $ref<any[]>([])
let currentPassword = $ref<any>(null)
let timer = $ref<number | null>(null) // 定时器

const formRef = $ref<any>(null)
const formData = reactive<any>({
  lockTime: '',
})
const rules = reactive<any>({
  lockTime: [{ required: true, message: '请选择锁屏时间', trigger: 'change' }],
})

/* 获取年级信息 */
async function getGradeListApi() {
  try {
    showLoading = true
    const res = await getGradeList({
      periodLevel: filterFormData.data.periodLevel,
    })
    filterFormData.options.gradeList = res.map((item) => {
      return {
        ...item,
        name: item.sysGradeName,
        id: item.sysGradeId,
      }
    })
    filterFormData.data.gradeId = res?.[0]?.sysGradeId
    filterFormData.data.gradeId && await getClassListApi()
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}
/* 获取班级信息 */
async function getClassListApi() {
  try {
    showLoading = true
    const res = await getClassList({
      sysGradeId: filterFormData.data.gradeId,
    })
    filterFormData.options.classList = res.map((item) => {
      return {
        ...item,
        name: item.className,
        id: item.schoolClassId,
      }
    })
    filterFormData.data.classId = res?.[0]?.schoolClassId
    filterFormData.data.classId && await getStudentListApi()
    filterFormData.data.classId && await getStatusApi()
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}

/* 开始定时器 */
function startTimer() {
  // 清除已有定时器
  clearTimer()

  // 创建新的定时器，每5秒调用一次getStudentListApi，不显示loading
  timer = window.setInterval(() => {
    getStudentListApi(false)
    getStatusApi()
  }, 5000)
}

/* 清除定时器 */
function clearTimer() {
  if (timer !== null) {
    clearInterval(timer)
    timer = null
  }
}

/* 获取学生在线信息列表 */
async function getStudentListApi(showLoadingFlag = true) {
  try {
    if (showLoadingFlag) {
      showLoading = true
    }
    const res = await getStudentList({
      schoolClassId: filterFormData.data.classId,
    })
    pcList = res.filter(v => v.isPcOnLine == true)
    padList = res.filter(v => v.isPadOnLine == true)
    offLineList = res.filter(v => (v.isPcOnLine == false && v.isPadOnLine == false))
    showLoading = false

    // 第一次加载完成后启动定时器
    if (showLoadingFlag && timer === null) {
      startTimer()
    }
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}

/* 年级改变 */
async function handleGradeChange() {
  filterFormData.data.classId = null
  filterFormData.options.classList = []
  clearTimer() // 清除定时器
  await getClassListApi()
}

/* 班级改变 */
async function handleClassChange() {
  clearTimer() // 清除定时器
  await getStudentListApi()
  await getStatusApi()
  setTimeout(() => {
    startTimer()
  }, 1000)
}

/* 获取学生在线信息列表 */
async function getStatusApi() {
  try {
    const res = await getStatus({
      schoolClassId: filterFormData.data.classId,
    })
    lockStatus = res.lockStatus
  }
  catch (err) {
    console.log(err)
  }
}

/* 锁屏 */
async function onLock() {
  switch (lockStatus) {
    case 2:
      try {
        const res = await checkPad({
          schoolClassId: filterFormData.data.classId,
        })
        if (res) {
          showDialog = true
        }
      }
      catch (err) {
        console.log(err)
      }
      break
    default:
      await unLockScreen({
        schoolClassId: filterFormData.data.classId,
      }).then((res) => {
        $g.showToast('操作成功')
        lockStatus = 2
      })
      break
  }
}

async function confirm() {
  await formRef?.validate()
  await lockScreen({
    schoolClassId: filterFormData.data.classId,
    lockTime: formData.lockTime,
  }).then((res) => {
    $g.showToast('操作成功')
    lockStatus = 1
    showDialog = false
  })
}

/* 关闭弹窗 */
function closeDialog() {
  formData.lockTime = ''
  formRef?.resetFields()
}

/* 查看学生信息 */
function handleInfo(item: any, type: number) {
  currentIdNum = item.idNum
  currentFlag = type == 1
  infoList = [
    {
      label: '学生：',
      value: item.studentName,
    },
    {
      label: '启鸣号：',
      value: item.idNum,
    },
    {
      label: '年级：',
      value: item.sysGradeName,
    },
    {
      label: '班级：',
      value: item.className,
    },
    {
      label: '状态：',
      value: type == 1 ? '在线' : '离线',
      textColor: type == 1 ? 'text-[#00D3A9]' : 'text-[#F2494A]',
    },
    {
      label: '累计登录次数：',
      value: item.loginCount,
    },
    {
      label: '最近登录时间：',
      value: item.lastLoginTime,
    },
  ]
  showInfoDialog = true
}

/* 下线 */
async function offLine() {
  kickOffLoading = true
  await kickOff({
    idNum: currentIdNum,
  }).then((res) => {
    getStudentListApi()
    showInfoDialog = false
    $g.showToast('操作成功')
  }).catch((err) => {
    console.log(err)
  }).finally(() => {
    kickOffLoading = false
  })
}

/* 重置密码 */
async function onReset() {
  resetLoading = true
  await resetPassword({
    idNum: currentIdNum,
  }).then((res) => {
    currentPassword = res
    showInfoDialog = false
    showTipDialog = true
  }).catch((err) => {
    console.log(err)
  }).finally(() => {
    resetLoading = false
  })
}

onMounted(() => {
  getGradeListApi()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimer()
})
</script>

<template>
  <div class="flex flex-col h-full toolContainer">
    <g-navbar title="教学工具" class="px-26px pt-26px">
    </g-navbar>
    <div class="px-26px overflow-y-auto pb-75px">
      <!-- 筛选 -->
      <div class="flex justify-between mt-26px ">
        <div class="flex items-center">
          <el-select
            v-model="filterFormData.data.gradeId"
            placeholder="请选择年级"
            class="custom-select w-[156px]"
            @change="handleGradeChange"
          >
            <el-option
              v-for="item in filterFormData.options.gradeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="filterFormData.data.classId"
            placeholder="请选择班级"
            class="custom-select w-[156px] ml-27px"
            @change="handleClassChange"
          >
            <el-option
              v-for="item in filterFormData.options.classList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <div v-else>
        <div class="text-[#97989A] text-14px mt-18px">
          当前在线人数{{ pcList.length + padList.length }}，离线人数{{ offLineList.length }}，一键锁屏功能仅针对在线平板生效，点击学生姓名可修改密码和一键下线
        </div>
        <div class="font-600 text-18px my-25px">
          在线人数（{{ pcList.length + padList.length }}）
        </div>
        <div class="bg-[#fff] rounded-[9px] w-full p-18px">
          <div class="text-17px">
            平板在线（{{ padList.length }}）
          </div>
          <div class="mb-18px flex flex-wrap">
            <div
              v-for="item in padList"
              :key="item.idNum"
              class="w-88px h-54px border rounded-[5px] border-[#C4C4C4] flex justify-center items-center text-16px relative mt-20px mr-22px cursor-pointer"
              @click="handleInfo(item, 1)"
            >
              {{
                item?.studentName?.length > 4
                  ? `${item?.studentName.substring(0, 4)}...`
                  : item?.studentName
              }}
              <div class="w-11px h-11px rounded-[100%] bg-[#00D3A9] absolute right-[-5px] top-[-5px]"></div>
            </div>
          </div>
          <div class="text-17px">
            PC在线（{{ pcList.length }}）
          </div>
          <div class="flex flex-wrap">
            <div
              v-for="item in pcList"
              :key="item.idNum"
              class="w-88px h-54px border rounded-[5px] border-[#C4C4C4] flex justify-center items-center text-16px relative mt-20px mr-22px cursor-pointer"
              @click="handleInfo(item, 1)"
            >
              {{
                item?.studentName?.length > 4
                  ? `${item?.studentName.substring(0, 4)}...`
                  : item?.studentName
              }}
              <div class="w-11px h-11px rounded-[100%] bg-[#00D3A9] absolute right-[-5px] top-[-5px]"></div>
            </div>
          </div>
        </div>
        <div class="font-600 text-18px my-25px">
          离线人数（{{ offLineList.length }}）
        </div>
        <div class="bg-[#fff] rounded-[9px] flex flex-wrap w-full p-18px pb-0">
          <div
            v-for="item in offLineList"
            :key="item.idNum"
            class="w-88px h-54px border rounded-[5px] border-[#C4C4C4] flex justify-center items-center text-16px relative mb-20px mr-22px cursor-pointer"
            @click="handleInfo(item, 2)"
          >
            {{
              item?.studentName?.length > 4
                ? `${item?.studentName.substring(0, 4)}...`
                : item?.studentName
            }}
            <div class="w-11px h-11px rounded-[100%] bg-[#F2494A] absolute right-[-5px] top-[-5px]"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[#FFFFFF] w-full h-61px fixed bottom-0 flex justify-center items-center">
      <div
        v-if="padList.length"
        class="w-110px h-45px rounded-[23px] flex flex-col justify-center items-center cursor-pointer"
        :class="lockStatus == 2 ? 'bg-[#00CE9B] border-none text-[#fff]' : 'border border-[#DCDEE0] text-[#333333]'"
        @click="onLock"
      >
        <div class="text-16px font-500">
          {{ lockStatus == 2 ? '一键锁屏' : '取消锁屏' }}
        </div>
        <img
          class="w-[14px] h-[16px] "
          :src="
            lockStatus == 2
              ? $g.tool.getFileUrl('teachingTools/lock.png')
              : $g.tool.getFileUrl('teachingTools/unlock.png')
          "
        />
      </div>
      <div v-else class="w-110px h-45px rounded-[23px] flex flex-col justify-center items-center bg-[rgba(102,102,102,0.6)] text-[#fff] cursor-pointer">
        <div class="text-16px font-500">
          一键锁屏
        </div>
        <div class="text-11px">
          当前无人平板在线
        </div>
      </div>
    </div>
    <el-dialog
      v-model="showDialog"
      width="416px"
      :close-on-click-modal="false"
      class="custom-dialog"
      @close="closeDialog"
    >
      <div class="flex flex-col items-center formStyle my-20px">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="auto"
          label-position="top"
        >
          <el-form-item label="请选择锁屏时间" prop="lockTime">
            <el-select
              v-model="formData.lockTime"
              placeholder="请选择锁屏时间"
              class="dialog-select w-[253px] h-41px"
            >
              <el-option
                v-for="item in lockOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="text-[#999999] text-12px flex items-center">
          <img
            class="w-[13px] h-[13px] mr-3px"
            :src="
              $g.tool.getFileUrl('teachingTools/watchout.png')
            "
          />
          <span>锁屏后该班级学生不可使用平板，请谨慎操作</span>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-center">
          <div>
            <el-button
              @click="() => {
                showDialog = false
              }"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              :loading="btnLoading"
              @click="confirm"
            >
              确定
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showInfoDialog"
      width="416px"
    >
      <div class="flex flex-col">
        <div v-for="item in infoList" :key="item.label" class="flex text-16px mb-18px">
          <span class="text-[#666666]">{{ item.label }}</span>
          <span class="font-500 ml-18px" :class="$g.tool.isTrue(item?.textColor) ? item?.textColor : 'text-[#333333]' ">{{ item.value }}</span>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-center">
          <div>
            <el-button
              v-if="currentFlag"
              color="#F2494A"
              class="w-110px h-31px"
              :loading="kickOffLoading"
              @click="offLine"
            >
              下线
            </el-button>
            <el-button
              color="#6474FD"
              class="w-110px h-31px"
              :loading="resetLoading"
              @click="onReset"
            >
              重置密码
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showTipDialog"
      width="416px"
      :close-on-click-modal="false"
    >
      <p class="text-18px text-[#333333] text-center font-600 mt-[-10px]">
        密码已重置
      </p>
      <p class="text-16px text-[#333333] text-center mt-30px mb-15px">
        {{ currentPassword }}
      </p>
      <template #footer>
        <div class="flex justify-center pb-10px">
          <el-button
            color="#6474FD"
            class="w-79px h-31px"
            @click="() => {
              showTipDialog = false
            }"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss">
/* Global styles for all Element Plus dialogs */
.toolContainer{
  .el-dialog {
  border-radius: 7px !important;
}
}
</style>

<style lang="scss" scoped>
.custom-select {
  --el-border-radius-base: 16px;
  --el-fill-color-blank: rgba(255,255,255,1);
  --el-border-color: rgba(235, 235, 235, 1);
  --el-border-color-hover: #6365ff;
  --el-popover-border-radius: 9px; /* 弹窗圆角 */
  --el-text-color-primary: #3C9EF9; /* 选中文本颜色 */
  --el-input-text-color: #000;
  :deep() {
  .el-select__wrapper {
      height: 43px;
    }
  }
}
.dialog-select{
  --el-border-radius-base: 5px;
  --el-fill-color-blank: rgba(255,255,255,1);
  --el-border-color: rgba(235, 235, 235, 1);
  --el-border-color-hover: #6365ff;
  --el-popover-border-radius: 9px; /* 弹窗圆角 */
  --el-text-color-primary: #3C9EF9; /* 选中文本颜色 */
  --el-input-text-color: #000;
  :deep() {
  .el-select__wrapper {
      height: 43px;
      background-color: #F4F4F4;
    }

  }
}
.formStyle{
  :deep() {
    .el-form-item__label{
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }
}
</style>
