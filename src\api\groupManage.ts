import config from '@/config/index'
import request from '@/utils/request/index'

const {
  VITE_PAD_API,
  VITE_WORKBENCH_API,
  VITE_JZT_API,
  VITE_APP_BASE_API,
} = config

// 获取班级列表
export async function getClassList() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/classTypeSelect`)
}

// 教师年级班级列表
export async function getGradeClassList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/gradeClassList`, data)
}

// 教师班级科目 select
export async function getSubjectList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/class/subjectSelect`, data)
}

// 班级学生列表
export async function getStudentList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/class/studentList`, data)
}

// 获取小组列表
export async function getGroupList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/group/list`, data)
}

// 新增小组
export async function createNewGroup(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/group/add`, data)
}

// 编辑小组（仅小组名称）
export async function editGroupName(data) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/task/group/edit`, data)
}

// 删除小组
export async function delGroupById(data) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/group/delete`, data)
}

// 新增小组学生
export async function addStudentToGroup(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/group/addStudent`, data)
}

// 删除小组成员
export async function delMembersFromGroup(data) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/group/deleteStudent`, data)
}
