interface Tool {
  /** 获取文件后缀 */
  getExt: (filename: string) => string | undefined
  /** 将url请求参数转为json格式 */
  paramObj: (url: string, ...params: any) => object
  /** 获取随机id */
  uuid: (length: number) => string
  /** 判断是否为存在 */
  isTrue: (a: any) => boolean
  /** 获取静态资源地址 */
  getFileUrl: (url: string) => any
  /** 判断精准类型 */
  typeOf: (
    obj: any,
  ) =>
    | 'boolean'
    | 'NaN'
    | 'number'
    | 'string'
    | 'function'
    | 'array'
    | 'date'
    | 'regExp'
    | 'undefined'
    | 'null'
    | 'object'
  /** 判断是否安卓 */
  isAndroid: () => boolean
  /** 判断是否苹果 */
  isIOS: () => boolean
  /** 判断是否PC */
  isPC: () => boolean
  /** 判断是否是PC非正式环境 */
  isPCTest: () => boolean
  /** 判断是否是PC开发环境 */
  isPCDevelopmentL: () => boolean
  /** 计算到现在时间 */
  timeAgo: (time: any) => string
  /** 加载数学公式 */
  renderMathjax: (elClass?: string) => void
  /** url参数拼接 */
  paramsToUrl: (e: object) => void
  /** 动态加载js */
  loadJS: (url: string) => any
  /** 获取资源类型 */
  resourceType: (str: string) => any
  /** 下载文件 */
  downloadFile: (...params: any) => any
  /** mqtt解密 */
  decryptMqttPassword: (
    clientId: string | number,
    ciphertext: string | number,
  ) => string | number
  /** 通过文件后缀获取文件类型 */
  getFileTypeIcon: (suffix: string) => string
  /** 文字编码 */
  unicodeToZH: (str: string) => string
  /** 存储单位转换 */
  formatFileSize: (fileSize: number) => string
  /** 获取oss地址 */
  getOSSUrl: (url: string) => string
  /* 功能点击埋点 */
  buryingPoint: (
    title:
      | 'AI_QUESTION'
      | 'UNIT_PASS'
      | 'MICRO_LESSON'
      | 'FLAGSHIP_SYNCHRONIZATION',
  ) => any
  /** web365地址 */
  getWeb365Url: (url: string) => string
  // 添加索引签名支持任意函数
  [key: string]: ((...args: any[]) => any) | any
}

interface MathNumber {
  /** 加  */
  add: (num: number) => MathNumber
  /** 减  */
  sub: (num: number) => MathNumber
  /** 乘  */
  multiply: (num: number) => MathNumber
  /** 除  */
  divide: (num: number) => MathNumber
  /** 四舍五入  */
  toFixed: (precision?: number) => MathNumber
  /** 获取结果  */
  value: () => number
}

type FnName =
  | 'version'
  | 'token'
  | 'jztToken'
  | 'platform'
  | 'viewInset'
  | 'padding'
  | 'fullPage'
  | 'statusBarDark'
  | 'fullWithStatusBar'
  | 'keyboardResize'
  | 'showBottomBar'
  | 'loadError'
  | 'clearCache'
  | 'login'
  | 'back'
  | 'reload'
  | 'launchInBrowser'
  | 'launchInNewWebView'
  | 'launchInNewWebView2'
  | 'protocol'
  | 'nativeRoute'
  | 'previewImage'
  | 'previewVideo'
  | 'playVideo'
  | 'bindChild'
  | 'share'
  | 'download'
  | 'chooseMedia'
  | 'cropPic'
  | 'setStatusBarColor'
  | 'jump'
  | 'img'
  | 'video'
  | 'setFullWithStatusBar'
  | 'isBindChild'
  | 'loginOut'
  | 'showTabbar'
  | 'getToken'
  | 'enableWebPageBack'
  | 'userInfo'
  | 'openQiYuService'
  | 'setOrientations'
  | 'setClipboard'
  |'closeXueKeWang'

interface Flutter {
  (fnName: FnName, data?: any): any
}

interface Lodash {
  /** 深拷贝 */
  cloneDeep: <T>(value: T) => T
  /** 调用 iteratee 遍历 collection(集合) 中的每个元素。 如果迭代函数（iteratee）显式的返回 false ，迭代会提前退出。 */

  forEach: <T>(
    collection: Array<T> | Record<string, T>,
    iteratee?: (
      value: T,
      indexOrKey: number | string,
      collection: Array<T> | Record<string, T>,
    ) => any,
  ) => Array<T> | Record<string, T>

  /** 遍历 collection（集合）元素，返回 predicate（断言函数）返回真值 的所有元素的数组。 */
  filter: <T>(
    collection: T[] | Record<string, T>,
    predicate?: (
      value: T,
      indexOrKey: number | string,
      collection: T[] | Record<string, T>,
    ) => boolean,
  ) => T[]

  /** 移除数组中predicate（断言）返回为真值的所有元素，并返回移除元素组成的数组 */
  remove: <T>(
    array: T[],
    predicate?: (value: T, index: number, array: T[]) => boolean,
  ) => T[]

  /** 遍历 collection（集合）元素，返回 predicate（断言函数）第一个返回真值的第一个元素 */
  find: (...params: any) => any

  /** 该方法类似_.find，区别是该方法返回第一个通过 predicate 判断为真值的元素的索引值（index），而不是元素本身。 */
  findIndex: (...params: any) => any

  /** 将数组（array）拆分成多个 size 长度的区块，并将这些区块组成一个新数组 */
  chunk: <T>(array: T[], size?: number) => T[][]

  /** 创建一个从 object 中选中的属性的对象。 */
  pick: <T extends Record<string, any>, K extends keyof T>(
    object: T,
    ...paths: Array<K | K[]>
  ) => Pick<T, K>

  /** 反向版_.pick */
  omit: <T extends Record<string, any>, K extends keyof T>(
    object: T,
    ...paths: Array<K | K[]>
  ) => Omit<T, K>

  /** 检查 value(值) 是否在 collection(集合) 中。 */
  includes: (
    collection: Array<any> | Record<string, any> | string,
    value: any,
    fromIndex?: number,
  ) => boolean

  /** 通过 predicate（断言函数） 检查 collection（集合）中的 所有 元素是否都返回真值。 */
  every: <T>(
    collection: Array<T> | Record<string, T>,
    predicate?: (
      value: T,
      indexOrKey: number | string,
      collection: Array<T> | Record<string, T>,
    ) => boolean,
  ) => boolean

  /** 防抖:创建一个 debounced（防抖动）函数，该函数会从上一次被调用后，延迟 wait 毫秒后调用 func 方法。 */
  debounce: (...params: any) => any

  /** 节流:在 wait 秒内最多执行 func 一次的函数。 */
  throttle: (...params: any) => any

  /** 这个方法类似_.forEach，不同之处在于，_.forEachRight 是从右到左遍历集合中每一个元素的。 */
  forEachRight: (...params: any) => any

  /** 生成一个介于包容性 lower 和 upper 边界之间的随机数。如果 floating 是 true,则返回浮点数 */
  random: (lower?: number, upper?: number, floating?: boolean) => number

  /** 接受一个 iteratee （迭代函数），调用每一个数组（array）的每个元素以产生唯一性计算的标准 */
  uniqBy: <T>(array: T[], iteratee?: (value: T) => any) => T[]
  /** 生成唯一 ID。如果 prefix 给定，则将 ID 附加到其上 */
  uniqueId: (prefix?: string) => string
  /** 通过iteratee运行集合中的每个元素来创建一个值数组。 */
  map: (...params: any) => any
  /** 计算n的四舍五入精度。 */
  round: (n: number, precision?: number) => number
  /** 判断两个值是否相等  */
  isEqual: (o: any, n: any) => boolean
  [key: string]: (...args: any[]) => any
}

interface Global {
  /** flutter提供的各种方法  */
  flutter: Flutter
  /** 工具函数  */
  tool: Tool
  /** 事件总线  */
  bus: any
  /** dayjs */
  dayjs: any
  /**  */
  JSON: any
  /** 当前环境是否flutter环境  */
  isFlutter: boolean
  /** 当前环境是否PC环境  */
  isPC: boolean
  /** lodash按需引入的方法 */
  _: Lodash
  /** 数学方法 */
  math: (num: number) => MathNumber
  msg(
    msg: string,
    type?: 'success' | 'warning' | 'error' | 'info',
    options?: Record<string, any>,
  ): void
  [propName: string]: any
}

declare const _: any
declare const $dayjs: any
declare const $g: Global
declare const $_: any
declare const __APP_VERSION__: string
declare const WebOfficeSDK: any
declare const mqtt: any
declare const listenerApp: any
declare const fabric: any
declare const HlsPlayer: any
declare const eruda: any
declare const document: Document

interface Window {
  $g: Global
  $_: any
  flutter_inappwebview: any
  MathJax: any
  leaveCurrentPageState: (...arg) => void
  refreshPage: (...arg) => void
  WebOfficeSDK: any
  listenerApp: any
  _time: any
  onFileChoose: (...arg) => void
}
