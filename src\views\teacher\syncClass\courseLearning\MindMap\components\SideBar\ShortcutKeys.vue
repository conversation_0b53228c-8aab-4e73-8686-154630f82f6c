<script setup lang="ts">
import { shortcutKeyList } from '../../config/other.js'
import DrawerLayout from './DrawerLayout.vue'

const emit = defineEmits(['resetSidebar'])
let show = defineModel('modelValue')
</script>

<template>
  <DrawerLayout v-model="show"
                title="快捷键"
                @close="emit('resetSidebar')"
  >
    <el-scrollbar>
      <div class="box px-10px">
        <div v-for="item in shortcutKeyList" :key="item.type">
          <div class="title">
            {{ item.type }}
          </div>
          <div v-for="item2 in item.list"
               :key="item2.value"
               class="list"
          >
            <div class="item">
              <span
                v-if="item2.icon"
                class="icon iconfont"
                :class="[item2.icon]"
              ></span>
              <span class="name" :title="item2.name">{{ item2.name }}</span>
              <div class="value" :title="item2.value">
                {{ item2.value }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </DrawerLayout>
</template>

<style lang="scss" scoped>
:deep() {
  .el-drawer__header {
    margin-bottom: 0;
  }
}
.box {
  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 26px 0 20px;
  }

  .list {
    font-size: 14px;

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .icon {
        font-size: 16px;
        margin-right: 16px;
      }

      .name {
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .value {
        color: #909090;
        margin-left: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
../sideConfig
