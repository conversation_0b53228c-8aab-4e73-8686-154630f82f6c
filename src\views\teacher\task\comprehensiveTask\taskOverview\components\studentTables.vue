<script setup lang="ts">
import { studentComplete } from '@/api/comprehensiveTask'

const props = defineProps({
  // currentGroup.headerType: 1--默写 2--阅题 3--测试
  currentGroup: {
    type: Object,
    default: () => {},
  },
  tableOptions: {
    type: Object,
    default: () => {},
  },
  hideClearance: {
    type: Boolean,
    default: false,
  },
  hideOperation: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['getData', 'initPage'])
const router = useRouter()
let key = $ref('1')

const initialColumn = $ref([
  {
    label: '学生姓名',
    prop: 'studentName',
    minWidth: 123,
  },
  {
    label: '启鸣号',
    prop: 'idNum',
    minWidth: 123,
  },
  {
    label: '班级/组',
    prop: 'classGroupName',
    minWidth: 140,
  },
  {
    label: props.currentGroup?.headerType == 1 ? '默写科目' : props.currentGroup?.headerType == 2 ? '阅读科目' : '测试科目',
    prop: 'sysSubjectName',
    minWidth: 100,
  },
  {
    label: '任务状态',
    prop: 'taskState',
    sort: true,
    slot: true,
    minWidth: 100,
  },
])

const columnList = {
  1: [{
    label: '批改状态',
    prop: 'correctionState',
    slot: true,
    sort: true,
    minWidth: 100,
  },
{
    label: '默写得分',
    prop: 'core',
    slot: true,
    minWidth: 100,
  },
{
    label: '是否合格',
    prop: 'qualified',
    slot: true,
    minWidth: 100,
  },
{
    label: '任务用时',
    prop: 'taskDuration',
    slot: true,
    minWidth: 100,
  },
{
    label: '批改时间',
    prop: 'correctTime',
    slot: true,
    minWidth: 150,
  },
{
    label: '操作',
    prop: 'cz',
    slot: true,
    fixed: 'right',
    minWidth: 110,
  }],
  2: [{
    label: '任务用时',
    prop: 'taskDuration',
    slot: true,
  },
{
    label: '操作',
    prop: 'cz',
    slot: true,
  }],
  3: [{
    label: '批改状态',
    prop: 'correctionState',
    slot: true,
    sort: true,
    minWidth: 100,
  },
{
    label: '测试轮数',
    prop: 'round',
    slot: true,
    minWidth: 100,
  },
{
    label: '任务用时',
    prop: 'taskDuration',
    slot: true,
    minWidth: 100,
  },
{
    label: '操作',
    prop: 'cz',
    slot: true,
    fixed: 'right',
    minWidth: 110,
  }],
}

const TASK_STATUS = {
  1: {
    text: '待开始',
    className: '',
  },
  2: {
    text: '已完成',
    className: 'text-[#00B34A]',
  },
  3: {
    text: '进行中',
    className: 'text-[#FAAD14]',
  },
}

const CORRECT_STATUS = {
  1: {
    text: '待批改',
    className: 'text-[#F2494A]',
  },
  2: {
    text: '批改中',
    className: 'text-[#FAAD14]',
  },
  3: {
    text: '已批改',
    className: 'text-[#00B34A]',
  },
}

// 定义排序字段映射
const SORT_FIELD_MAP = {
  taskState: 'TASK_STATE',
  correctionState: 'CORRECTION_STATE',
}

// 秒钟转换
function secondsToMinutesSeconds(seconds) {
  if (!seconds) return '/'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  if (!mins) return `${secs}秒`
  return `${mins}分${secs}秒`
}

// 手动点击通关
async function handleClickClearance(row) {
  await studentComplete({
    taskScheduleGroupId: props.currentGroup.taskScheduleGroupId,
    schoolStudentId: row.schoolStudentId,
    taskId: row.taskId,
  })
  emit('getData')
}

// 进入学生详情
function toStudentTaskDetail(row) {
  const query = {
    schoolStudentId: row.schoolStudentId,
    taskScheduleGroupId: props.currentGroup.taskScheduleGroupId,
    taskId: row?.taskId,
  }
  router.push({
    name: 'CorrectPage',
    query,
  })
}

// 批改
function toCorrect(row) {
  const routerName = 'CorrectionPage'
  router.push({
    name: routerName,
    query: {
      studentId: row.schoolStudentId,
      taskId: row.taskId,
      exerciseTaskId: row.exercise?.exerciseTaskId,
    },
  })
}

// 前往报告页
function toReport(row) {
  let url = `${
    import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
  }/#/student/studentTest/testReport?exerciseTaskId=${row?.exercise?.exerciseTaskId}&showBack=false&source=teacher`
  if ($g.isPC) {
    window.open(url, '_blank')
  }
  else {
    let option = {
      url,
      refreshCallJs: false,
      inSafeArea: {
        top: false,
        left: true,
        bottom: false,
        right: false,
      },
      beforeEnter: {
        fullPage: true,
      },
      afterEnter: {
        fullPage: true,
      },
    }
    $g.flutter('launchInNewWebView2', option)
  }
}

// 排序
function sortChange({
  order,
  prop,
}) {
  emit('initPage', order
    ? {
        order: order.toUpperCase(),
        orderFiled: SORT_FIELD_MAP[prop],
      }
    : { cancelSort: true })
}

function changePage() {
  emit('getData')
}

watch(() => props.currentGroup, (val) => {
  if (val) {
    key = $g.tool.uuid(4)
    let newColumn: any = []
    if (val?.headerType == 2) {
      newColumn = initialColumn.map((item) => {
        return {
          ...item,
          minWidth: '',
        }
      })
    }
    else {
      newColumn = $g._.cloneDeep(initialColumn)
    }
    // 根据hideOperation属性决定是否添加操作列
    if (props.hideOperation)
      props.tableOptions.column = [...newColumn, ...columnList[val?.headerType].filter(col => col.prop !== 'cz')]

    else
      props.tableOptions.column = [...newColumn, ...columnList[val?.headerType]]
  }
}, {
  immediate: true,
})
</script>

<template>
  <g-table
    :key="key"
    stripe
    :border="false"
    :header-cell-style="{
      background: '#6474FD1A',
      color: '#6C6C74',
      fontWeight: '400',
      fontSize: '13px',
    }"
    :cell-style="{
      color: '#333333',
      fontWeight: '400',
      fontSize: '13px',
    }"
    :table-options="tableOptions"
    :highlight-current-row="false"
    @sort-change="sortChange"
    @change-page="changePage"
  >
    <!-- 任务状态 -->
    <template #taskState="{ row }">
      <span :class="TASK_STATUS[row.taskState].className">{{ TASK_STATUS[row.taskState].text }}</span>
    </template>
    <!-- 批改状态 -->
    <template #correctionState="{ row }">
      <span v-if="[1, 2, 3].includes(row.correctionState)" :class="CORRECT_STATUS[row.correctionState].className">{{ CORRECT_STATUS[row.correctionState].text }}</span>
      <span v-else>/</span>
    </template>
    <!-- 默写得分 -->
    <template #core="{ row }">
      <span>{{ row.dictation?.finallyScore || '/' }}</span>
    </template>
    <!-- 是否合格 -->
    <template #qualified="{ row }">
      <span v-if="row.dictation?.isPass">{{ row.dictation?.isPass == 1 ? '不合格' : '合格' }}</span>
      <span v-else>/</span>
    </template>
    <!-- 测试轮数 -->
    <template #round="{ row }">
      <span v-if="row.exercise?.retryIndex && currentGroup.taskPatternType == 4">{{ row.exercise?.retryIndex }}轮</span>
      <span v-else>/</span>
    </template>
    <!-- 任务用时 -->
    <template #taskDuration="{ row }">
      <span>{{ secondsToMinutesSeconds(row.taskDuration) }}</span>
    </template>
    <!-- 批改时间 -->
    <template #correctTime="{ row }">
      <span v-if="row?.correctionTime">{{ $g.dayjs(row?.correctionTime).format('YYYY/MM/DD HH:mm:ss') }}</span>
      <span v-else>/</span>
    </template>
    <template #cz="{ row }">
      <template v-if="!props.hideOperation">
        <template v-if="[1, 2].includes(props.currentGroup?.headerType)">
          <el-button
            v-if="props.currentGroup?.headerType == 1"
            type="primary"
            class="px-0"
            text
            :disabled="!row.correctionState"
            @click="toStudentTaskDetail(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="!props.hideClearance"
            type="primary"
            class="px-0"
            text
            :disabled="[2].includes(row.taskState)"
            @click="handleClickClearance(row)"
          >
            {{ row.taskState == 2 ? "已通关" : "通关" }}
          </el-button>
        </template>
        <template v-else>
          <el-button
            v-if="row.correctionState != 3"
            type="primary"
            class="px-0"
            text
            :disabled="![1, 2].includes(row.correctionState) || !row.exercise?.exerciseTaskId"
            @click="toCorrect(row)"
          >
            {{ row.correctionState == 3 ? "已批改" : "批改" }}
          </el-button>
          <!-- 跳转报告页 -->
          <el-button
            v-else
            type="primary"
            class="px-0"
            text
            @click="toReport(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="!props.hideClearance"
            type="primary"
            class="px-0"
            text
            :disabled="[2].includes(row.taskState)"
            @click="handleClickClearance(row)"
          >
            {{ row.taskState == 2 ? "已通关" : "通关" }}
          </el-button>
        </template>
      </template>
    </template>
  </g-table>
</template>

<style lang="scss" scoped>
:deep(){
.el-table .el-table__body tr:hover > td.el-table__cell{
    background-color: inherit !important;
}
.el-button.is-text{
  background-color: transparent !important;
}
}
</style>
