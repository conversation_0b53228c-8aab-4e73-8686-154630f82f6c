<script setup lang="ts">
import { syncData } from '@/api/teachingTools'

let showDialog = defineModel<boolean>('showDialog', { required: true })
const remainingClassList = inject<Ref<any[]>>('remainingClassList', ref([])) // 剩余班级列表
const moduleList = inject<Ref<any[]>>('moduleList', ref([])) // 模块列表
let selectedClassList = $ref<any[]>([]) // 选中的班级列表
let buttonLoading = $ref<boolean>(false) // 按钮loading
function handleClick(item: any) {
  if (selectedClassList.includes(item)) {
    selectedClassList = selectedClassList.filter((val: any) => val.schoolClassId !== item.schoolClassId)
  }
  else {
    selectedClassList.push(item)
  }
}
async function syncSave() {
  try {
    if (!selectedClassList.length) { return $g.showToast('请选择班级') }
    buttonLoading = true
    await syncData({
      syncSchoolClassIds: selectedClassList.map(v => v.schoolClassId),
      modules: moduleList.value.map((v) => {
        return {
          available: v.available ? 2 : 1,
          module: v.key,
          limitSeconds: v.limitSeconds,
        }
      }),
    })
    $g.showToast('同步成功')
    buttonLoading = false
    showDialog.value = false
    selectedClassList = []
  }
  catch (err) {
    buttonLoading = false
    console.log(err)
  }
}
</script>

<template>
  <div>
    <el-dialog
      v-model="showDialog"
      width="600px"
      @close="() => {
        showDialog = false
      }"
    >
      <template #header>
        <div class="flex items-center">
          <svg-common-tip color="#3C9EF9"></svg-common-tip>
          <span class="mt-2px ml-4px font-600 text-17px">是否将数据同步给以下班级</span>
        </div>
      </template>
      <div class="flex flex-wrap gap-15px">
        <div
          v-for="item in remainingClassList"
          :key="item.schoolClassId"
          class="border-[1px] border-solid border-[#f0f0f0] rounded-[11px] px-15px py-5px cursor-pointer"
          :class="{ 'bg-[#3C9EF9] text-[#fff]': selectedClassList.includes(item) }"
          @click="handleClick(item)"
        >
          {{ item.className }}
        </div>
      </div>
      <template #footer>
        <div>
          <el-button
            @click="() => {
              showDialog = false
            }"
          >
            取消
          </el-button>
          <el-button
            color="#3C9EF9"
            class="text-[#fff]"
            :loading="buttonLoading"
            @click="syncSave"
          >
            同步
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>
