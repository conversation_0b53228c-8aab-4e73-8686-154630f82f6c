<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import { saveSchoolModuleClass } from '@/api/teachingTools'
import SyncSaveDialog from './SyncSaveDialog.vue'

const props = defineProps({
  schoolClassId: {
    type: String,
    required: true,
  },
  modules: {
    type: Array as PropType<any[]>,
    required: true,
  },
})

let showDialog = defineModel<boolean>('showDialog', { required: true })
const remainingClassList = inject<ComputedRef<any[]>>('remainingClassList') // 剩余班级列表
let showSyncSaveDialog = $ref<boolean>(false) // 同步保存弹窗
let buttonLoading = $ref<boolean>(false) // 按钮loading
async function quit() {
  try {
    buttonLoading = true
    await saveSchoolModuleClass({
      schoolClassId: props.schoolClassId,
      modules: props.modules.map((v) => {
        return {
          available: v.available ? 2 : 1,
          module: v.key,
          limitSeconds: v.limitSeconds,
        }
      }),
    })
    buttonLoading = false
    showDialog.value = false
    if (remainingClassList && remainingClassList.value.length > 0) {
      showSyncSaveDialog = true
    }
  }
  catch (err) {
    buttonLoading = false
    console.log(err)
  }
}
</script>

<template>
  <div>
    <el-dialog
      v-model="showDialog"
      width="500px"
      @close="() => {
        showDialog = false
      }"
    >
      <template #header>
        <div class="flex items-center">
          <svg-common-tip color="#3C9EF9"></svg-common-tip>
          <span class="mt-2px ml-4px font-600 text-17px">确定保存？</span>
        </div>
      </template>
      <div>
        保存后将对选中班级全部学生生效
      </div>
      <template #footer>
        <div>
          <el-button
            @click="() => {
              showDialog = false

            }"
          >
            取消
          </el-button>
          <el-button
            color="#3C9EF9"
            class="text-[#fff]"
            :loading="buttonLoading"
            @click="quit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <SyncSaveDialog v-model:show-dialog="showSyncSaveDialog" />
  </div>
</template>

<style lang="scss" scoped>

</style>
