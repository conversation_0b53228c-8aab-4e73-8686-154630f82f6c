<script setup lang="ts" name="TeacherHome">
import { getTaskModuleConfig } from '@/api/common'
import CreateTask from './components/CreateTask.vue'
import TaskList from './components/TaskList.vue'

let isCreate = $ref(true) // 是否是创建任务模块
let currentSubject = $ref('')
let currentType: any = $ref(-1)
const taskRef: any = $ref(null)
let scrollTop = $ref(0)

// 刷新
function manuallyRefresh() {
  location.reload()
}

function ruleChange(option) {
  currentSubject = option.currentSubject
  currentType = option.currentType
}

onDeactivated(() => {
  if (taskRef) scrollTop = taskRef.scrollTop
})

onActivated(() => {
  if (taskRef) taskRef.scrollTop = scrollTop
})

let moduleItem = $ref({})
// 获取任务模块配置 任务中心内页面请求
async function getTaskModuleConfigApi() {
  const res = await getTaskModuleConfig()
  moduleItem = res.moduleConfigList.find(item => item.type == 1)
}

onMounted(() => {
  getTaskModuleConfigApi()
})
</script>

<template>
  <div class="pl-21px pt-21px h-[100vh]">
    <div class="w-full flex justify-end items-center pr-21px mb-5px">
      <img
        :src="$g.tool.getFileUrl('taskCenter/refresh.png')"
        alt="refresh"
        class="w-15px h-15px cursor-pointer mr-26px"
        @click="manuallyRefresh"
      />
    </div>
    <div ref="taskRef" class="h-[calc(100%_-_32px)] overflow-auto pb-21px pr-21px no-bar">
      <CreateTask
        v-if="isCreate"
        v-model:is-create="isCreate"
        :subject="currentSubject"
        :type="currentType"
        :module-item="moduleItem"
        @rule-change="ruleChange"
      />
      <TaskList
        v-else
        v-model:is-create="isCreate"
        :subject="currentSubject"
        :type="currentType"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-select__wrapper {
    background-color: transparent;
    border: none;
    box-shadow: none !important;
    padding-left: 0;
    padding-right: 0;
    .el-select__input-wrapper{
      display: none;
    }
  }
  .el-select__suffix {
    display: none;
  }
  .el-select__placeholder {
    width: fit-content;
  }
  .is-focused {
    .down {
      transform: rotate(180deg);
    }
  }
}
</style>
