<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})
const emit = defineEmits(['handleChange'])
let activeTab = defineModel('activeTab')
function handleChangeTab(key) {
  activeTab.value = key
  nextTick(() => {
    emit('handleChange')
  })
}
</script>

<template>
  <div class="flex bg-[#F3F4F5] w-fit py-2px mx-auto h-30px">
    <div
      v-for="item in data"
      :key="item.key"
      style="box-shadow: 0px 0 2px 0px #D9D9D9"
      class=" px-23px leading-[26px] mx-2px transition-all duration-75 cursor-pointer bg-[rgba(255,255,255,0)] text-13px text-[#666666]"
      :class="{ '!bg-white !text-[#333]': item.key == activeTab }"
      @click="handleChangeTab(item.key)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
