import type { RoutesModuleType } from '/#/router'
import { routes } from '@/router/index'

import { useUserStore } from '@/stores/modules/user'

export const useRouterStore: any = defineStore('router', {
  state: (): RoutesModuleType => ({
    routerArr: routes,
  }),
  getters: {
    keepAliveArr: (state) => {
      const newArr: string[] = []
      deepFn(state.routerArr)
      function deepFn(routerArr) {
        routerArr.forEach((e) => {
          if (e?.meta?.keepAlive)
            newArr.push(e.name)

          if (e?.children?.length)
            deepFn(e.children)
        })
      }
      return newArr
    },
    getRoutes: (state) => {
      const userStore = useUserStore()
      function filterRoutesByRole(routes: any[], role: number): any[] {
        return routes
          .filter((route) => {
            // 1. 过滤Root路由
            if (route.name === 'Root') return false

            // 2. 检查权限
            if (route.meta?.roles)
              return route.meta.roles.includes(role)

            // 3. 无roles定义的路由默认保留
            return true
          })
          .map((route) => {
            // 处理子路由
            const children = route.children
              ? filterRoutesByRole(route.children, role)
              : undefined

            const shouldKeep = (
              route.meta?.showMenu ||
              route.redirect ||
              children?.length ||
              (route.meta?.hidden && route.meta?.keepAlive)
            )

            return shouldKeep
              ? {
                  ...route,
                  children,
                }
              : null
          })
          .filter(Boolean)
      }

      return filterRoutesByRole($g._.cloneDeep(state.routerArr), userStore.currentRole)
    },
  },
  actions: {
    changeMenuMeta(options) {
      function handleRoutes(routes) {
        return routes.map((route) => {
          if (route.name === options.name)
            Object.assign(route.meta, options.meta)

          if (route.children && route.children.length)
            route.children = handleRoutes(route.children)

          return route
        })
      }
      this.routerArr = handleRoutes(this.routerArr)
    },
  },
})
