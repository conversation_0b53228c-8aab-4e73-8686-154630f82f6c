<script setup lang="ts">
interface IDrawerProps {
  /** 抽屉宽度，支持 px 或 vw */
  width?: string | number
  /** 是否显示遮罩层 */
  showMask?: boolean
  bodyClass?: object | Array<string>
}

const props = withDefaults(defineProps<IDrawerProps>(), {
  width: '75vw',
  showMask: true,
})

const show = defineModel<boolean>('show')

/** 显示抽屉 */
function showDrawer() {
  show.value = true
}

/** 隐藏抽屉 */
function hideDrawer() {
  show.value = false
}

// 暴露方法供父组件使用
defineExpose({
  showDrawer,
  hideDrawer,
})
</script>

<template>
  <div class="my-drawer">
    <Teleport to="#app">
      <Transition name="fade">
        <div v-show="show" class="drawer-container">
          <div v-if="showMask"
               class="drawer-mask"
               @click="hideDrawer"
          />
        </div>
      </Transition>

      <Transition name="slide">
        <div v-show="show" class="drawer-wrapper">
          <div
            class="drawer-content"
            :style="{ width: typeof width === 'number' ? `${width}px` : width }"
          >
            <slot name="close-button" />
            <div class="drawer-body" :class="bodyClass">
              <slot />
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<style scoped>
.drawer-container {
  position: fixed;
  inset: 0;
  z-index: 4000;
}

.drawer-mask {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.35);
}

.drawer-wrapper {
  position: fixed;
  inset: 0;
  z-index: 4000;
  pointer-events: none;
  overflow: visible;
}

.drawer-content {
  position: absolute;
  top: 17px;
  right: 17px;
  bottom: 17px;
  background: white;
  box-shadow: 0px 1px 32px 0px rgba(0, 0, 0, 0.03);
  pointer-events: auto;
  border-radius: 13px;
  overflow: visible;
}

.drawer-body {
  height: 100%;
  padding: 17px;
  padding-left: 24px;
  overflow-y: auto;
}

.folding {
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  white-space: nowrap;
  z-index: 1;
}
/* 遮罩层动画 */
.fade-enter-active {
  transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-leave-active {
  transition: opacity 0.2s cubic-bezier(0.4, 0, 1, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 抽屉滑动动画 */
.slide-enter-active {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: transform 0.2s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
}
</style>
