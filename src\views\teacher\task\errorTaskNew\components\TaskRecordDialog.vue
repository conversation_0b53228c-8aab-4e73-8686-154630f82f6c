<script setup lang="ts">
import { getErrorExamPublishRecordApi } from '@/api/taskCenter'

const props = defineProps({
  bookId: {
    type: Number || String,
    required: true,
  },
})

let showDialog = defineModel<boolean>('visible', { required: true })
let showLoading = $ref(true)
let data = $ref<any>([])
// 获取发布记录
async function getTaskRecord() {
  if (!props.bookId) {
    showLoading = false
    return
  }
  showLoading = true
  let res = await getErrorExamPublishRecordApi({
    bookId: props.bookId,
    page: 1,
    pageSize: 9999,
  })
  data = res.list
  showLoading = false
}
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    position="right"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="h-screen flex flex-col p-21px overflow-hidden bg-[#fff] rounded-[8px]"
    teleport="#app"
    v-bind="$attrs"
    @open="getTaskRecord()"
  >
    <div class="h-full w-[491px] ">
      <div class="text-[17px] text-[#333] font-600 text-center relative">
        发布记录
        <svg-menu-dialog-close class="absolute top-0 bottom-0 my-auto right-0 w-15px h-15px cursor-pointer" @click="showDialog = false" />
      </div>
      <div class="h-[calc(100%-27px)] overflow-y-auto no-bar mt-20px">
        <g-loading v-if="showLoading" class="h-200px"></g-loading>
        <template v-else>
          <g-empty v-if="!data.length"></g-empty>
          <template v-else>
            <div
              v-for="item in data"
              :key="item.taskExamRecordId"
              class="px-13px py-11px text-[14px] bg-[#F3F4F9] rounded-[6px] mb-13px"
            >
              <span>{{ item.createTime }}</span>  {{ item.bookName }}({{ item.sysSubjectName }})
              <span :class="item.publishMessage.includes('成功') ? 'text-[#52C41A]' : 'text-[#FF4646]'">
                {{ item.publishMessage }}
              </span>
            </div>
          </template>
        </template>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>

</style>
