<script lang="ts" setup>
import Player, { Events } from 'xgplayer'
import FlvJsPlugin from 'xgplayer-flv.js'
import HlsPlugin from 'xgplayer-hls'
import 'xgplayer/dist/index.min.css'

const props = defineProps({
  url: {
    type: String,
    default: '',
    required: true,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  currentTime: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits<{
  (e: 'complete', val: any): void
  (e: 'play', val: any): void
  (e: 'pause'): void
  (e: 'ended'): void
  (e: 'timeupdate', val: any): void
}>()

watch(
  () => props.url,
  (newVal) => {
    if (player) {
      reloadCount = 0
      destroyPlayer()
      initVideo()
    }
  },
)

let player: any = $ref()
let reloadCount = $ref(0)

const xgplayerConfig = $computed(() => {
  return {
    id: 'videoPreview',
    mode: 'bottom',
    url: props.url,
    playsinline: true,
    width: '100%',
    height: '100%',
    lang: 'zh-cn',
    videoInit: true, // 在没有设置poster的情况下显示视频首帧,当autoplay为true时，该配置为false无效
    plugins: [] as any,
    poster: getOssPoster(),
    fullscreen: {
      useCssFullscreen: !$g.isPC,
    },
    commonStyle: {
      // 进度条底色
      progressColor: '#4d4d4d',
      // 播放完成部分进度条底色
      playedColor: '#FF8700',
      // 缓存部分进度条底色
      cachedColor: '#a6a6a6',
      // 进度条滑块样式
      sliderBtnStyle: {},
      // 音量颜色
      volumeColor: '#FF8700',
    },
    ignores: ['cssfullscreen'],
  }
})

function getOssPoster() {
  if (props.url.includes('.oss') || props.url.includes('qm-resource')) {
    return (
      `${props.url
      }?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_500,m_fast`
    )
  }
  return ''
}

function initVideo() {
  let ext = $g.tool.getExt(props.url)
  // 非app端加载hls插件
  // if (!$g.isFlutter) {
  if (ext == 'm3u8' || ext == 'hls')
    xgplayerConfig.plugins.push(HlsPlugin)

  // }
  if (ext == 'flv')
    xgplayerConfig.plugins.push(FlvJsPlugin)

  if (['m3u8'].includes(ext as string) && !props.config.poster)
    props.config.poster = $g.tool.getOSSUrl('microClass/newVideoBig.jpg')

  player = new Player({
    ...xgplayerConfig,
    ...props.config,
  })
  // 播放器创建video完成，可以开始播放
  player.once(Events.COMPLETE, () => {
    emit('complete', player)
  })
  // 播放
  player.on(Events.PLAY, () => {
    emit('play', player)
  })
  // 暂停
  player.on(Events.PAUSE, () => {
    emit('pause')
  })
  // 结束
  player.on(Events.ENDED, () => {
    emit('ended')
  })
  // 播放时间改变
  player.on(Events.TIME_UPDATE, () => {
    emit('timeupdate', player)
  })
  // 视频缓冲足够数据，可以播放
  player.once(Events.CANPLAY, () => {
    // 设置视频起始播放位置，如果视频已播放完毕则定位时间-2
    if (!props.currentTime)
      return

    if (props.currentTime == Math.floor(player.duration))
      player.currentTime = props.currentTime - 2

    else
      player.currentTime = props.currentTime
  })
  // 报错处理
  player.on(Events.ERROR, (error) => {
    // 失败3次不再重置
    if (reloadCount > 2)
      return

    console.log('⚡[ error ] >', error)
    // 重新加载视频
    setTimeout(() => {
      // 重置组件
      player?.reset()
      // 开始播放
      player?.play()
      reloadCount++
    }, 500)
  })
}

onMounted(async () => {
  initVideo()
})

onBeforeUnmount(() => {
  destroyPlayer()
})

function destroyPlayer() {
  if (player) {
    player.destroy() // 销毁播放器
    player = null // 将实例引用置空
  }
}

// 获取视频对象
function getVideo() {
  return player
}

defineExpose({ getVideo })
</script>

<template>
  <div>
    <div id="videoPreview" />
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .xgplayer .xgplayer-start {
    background: rgba(0, 0, 0, 0.38);
    border-radius: 50%;
  }
  .xgplayer .xg-options-list li:hover,
  .xgplayer .xg-options-list li.selected {
    color: #ff7d29 !important;
  }
}
</style>
