<?xml version="1.0" encoding="UTF-8"?>
<svg width="440px" height="265px" viewBox="0 0 440 265" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 4</title>
    <defs>
        <radialGradient cx="51.9779503%" cy="50%" fx="51.9779503%" fy="50%" r="325.814193%" gradientTransform="translate(0.519780,0.500000),scale(0.132916,1.000000),rotate(178.469713),scale(1.000000,0.247638),translate(-0.519780,-0.500000)" id="radialGradient-1">
            <stop stop-color="#B2D7FA" offset="0%"></stop>
            <stop stop-color="#F1F1F1" stop-opacity="0" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="45.3833818%" y1="42.8545859%" x2="50%" y2="55.2520984%" id="linearGradient-2">
            <stop stop-color="#C7E4FF" offset="0%"></stop>
            <stop stop-color="#E7E7E7" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="45.3833818%" y1="33.3671522%" x2="50%" y2="62.2256529%" id="linearGradient-3">
            <stop stop-color="#C7E4FF" offset="0%"></stop>
            <stop stop-color="#E7E7E7" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-3.0%" y="-30.4%" width="105.9%" height="160.7%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="2.2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="33.4217729%" x2="35.8403385%" y2="63.9725692%" id="linearGradient-5">
            <stop stop-color="#82B4CE" offset="0%"></stop>
            <stop stop-color="#C4E0F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="56.1854063%" y1="26.0654311%" x2="19.0443217%" y2="88.448998%" id="linearGradient-6">
            <stop stop-color="#82B4CE" offset="0%"></stop>
            <stop stop-color="#C4E0F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="55.0304774%" y1="27.7860477%" x2="-10.130778%" y2="30.7457281%" id="linearGradient-7">
            <stop stop-color="#69B6FF" offset="0%"></stop>
            <stop stop-color="#EAEDFB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="58.4716929%" y1="-2.22556644%" x2="35.2292032%" y2="117.783036%" id="linearGradient-8">
            <stop stop-color="#69B6FF" offset="0%"></stop>
            <stop stop-color="#EAF5FB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="65.5409026%" y1="114.910298%" x2="50%" y2="37.0498478%" id="linearGradient-9">
            <stop stop-color="#81BAE7" offset="0%"></stop>
            <stop stop-color="#3D85CC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="26.3005674%" y1="26.6967556%" x2="60.4300916%" y2="72.1658725%" id="linearGradient-10">
            <stop stop-color="#E5F2F9" offset="0%"></stop>
            <stop stop-color="#CAE9F7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="2.54785156%" x2="9.21375242%" y2="60.6831857%" id="linearGradient-11">
            <stop stop-color="#E5F2F9" offset="0%"></stop>
            <stop stop-color="#CAE9F7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#FFDE0C" offset="0%"></stop>
            <stop stop-color="#FFBA05" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-13">
            <stop stop-color="#FFDE0C" offset="0%"></stop>
            <stop stop-color="#FFBA05" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#6DD3FF" offset="0%"></stop>
            <stop stop-color="#3BA8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="40.2163489%" y1="84.2016935%" x2="75.5157396%" y2="88.979429%" id="linearGradient-15">
            <stop stop-color="#B1FFBE" offset="0%"></stop>
            <stop stop-color="#5CDB8E" offset="44.4207221%"></stop>
            <stop stop-color="#149654" offset="100%"></stop>
        </linearGradient>
        <path d="M190.941805,207.117167 C190.512805,206.813567 189.965005,206.993967 189.716405,207.524167 L176.122605,236.577367 C176.043405,236.744567 176.001605,236.935967 176.001605,237.129567 C176.001605,237.741167 176.404205,238.236167 176.899205,238.236167 C181.438538,239.475501 185.970538,240.095167 190.495205,240.095167 C195.018405,240.095167 199.549671,239.475501 204.089005,238.236167 C204.247405,238.236167 204.401405,238.185567 204.537805,238.088767 C204.966805,237.782967 205.114205,237.105367 204.865605,236.577367 L191.271805,207.524167 C191.192605,207.354767 191.078205,207.216167 190.941805,207.117167 Z" id="path-16"></path>
        <filter x="-8.6%" y="-7.5%" width="117.3%" height="115.1%" filterUnits="objectBoundingBox" id="filter-17">
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.219607843   0 0 0 0 0.780392157   0 0 0 0 0.474509804  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="17.846401%" y1="12.256193%" x2="94.9024379%" y2="107.807578%" id="linearGradient-18">
            <stop stop-color="#FFA99B" offset="0%"></stop>
            <stop stop-color="#FF6B5E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="86.6279368%" x2="64.977926%" y2="94.5827631%" id="linearGradient-19">
            <stop stop-color="#FFFFFF" stop-opacity="0.2235" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2431" offset="46.3410884%"></stop>
            <stop stop-color="#FFB3B3" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="45.3833818%" y1="43.6289409%" x2="50%" y2="54.6829237%" id="linearGradient-20">
            <stop stop-color="#C7E4FF" offset="0%"></stop>
            <stop stop-color="#E7E7E7" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="113.461538%" gradientTransform="translate(0.500000,0.500000),scale(0.440678,1.000000),translate(-0.500000,-0.500000)" id="radialGradient-21">
            <stop stop-color="#81A8E7" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </radialGradient>
        <filter x="-15.7%" y="-35.5%" width="131.3%" height="171.1%" filterUnits="objectBoundingBox" id="filter-22">
            <feGaussianBlur stdDeviation="3.08" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="2.83744335%" y1="49.0687712%" x2="46.1835176%" y2="55.5277528%" id="linearGradient-23">
            <stop stop-color="#A3A3A3" offset="0%"></stop>
            <stop stop-color="#757575" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="2.83744335%" y1="49.7482616%" x2="46.1835176%" y2="51.4943135%" id="linearGradient-24">
            <stop stop-color="#A3A3A3" offset="0%"></stop>
            <stop stop-color="#757575" offset="100%"></stop>
        </linearGradient>
        <path d="M18.1423678,3.53569299 C12.9402478,3.53569299 8.72372778,4.83237299 8.72372778,6.43397299 C8.72372778,8.03557299 12.9402478,9.33225299 18.1423678,9.33225299 C23.3475678,9.33225299 27.5640878,8.03557299 27.5640878,6.43397299 C27.5640878,4.83237299 23.3475678,3.53569299 18.1423678,3.53569299 Z" id="path-25"></path>
        <mask id="mask-26" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="18.84036" height="5.79656" fill="white">
            <use xlink:href="#path-25"></use>
        </mask>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="暂无内容" transform="translate(-973.000000, -490.000000)">
            <g id="编组-5" transform="translate(973.000000, 490.000000)">
                <g id="编组-4" transform="translate(0.000000, 0.000000)">
                    <rect id="路径" x="0" y="1.35936461e-05" width="440" height="264.999718"></rect>
                    <path d="M381.778139,224.6806 C419.08377,210.1364 59.5415374,210.0396 59.5415374,217.359 C59.5415374,224.6806 92.7304574,221.5082 82.8402296,228.1852 C72.9500019,234.8644 0.312384843,218.338 22.9311927,241.669 C45.5522008,265 196.358922,265 239.154234,265 C281.947346,265 432.556042,264.8658 410.843747,248.9972 C389.133652,233.1308 344.474708,239.2248 381.778139,224.6806 Z" id="路径" fill="url(#radialGradient-1)"></path>
                    <path d="M271.241367,59.5084681 C251.9679,59.0963347 229.859367,76.7220014 204.915767,112.385468 L439.998967,108.568468 C420.9411,51.8671347 399.663433,23.5164681 376.165967,23.5164681 C340.919767,23.5164681 352.526967,63.0768681 332.341967,67.9498681 C312.154767,72.8250681 300.151567,60.1288681 271.241367,59.5084681 Z" id="路径" fill="url(#linearGradient-2)" opacity="0.678222656"></path>
                    <path d="M194.854,112.384814 C180.3736,59.5496136 166.651467,31.9506136 153.6876,29.5878136 C134.2418,26.0436136 124.5816,45.7776136 115.775,45.7776136 C106.9662,45.7776136 91.894,0.0418135932 65.5886,0 C48.0516667,-0.0278576045 26.1888,32.0833469 0,96.3336136 L194.854,112.384814 Z" id="路径" fill="url(#linearGradient-3)"></path>
                    <path d="M222.589098,220.858087 C161.129898,220.858087 111.306498,225.724487 111.306498,231.728287 C111.306498,237.729887 161.129898,242.596287 222.589098,242.596287 C284.048298,242.596287 333.869498,237.729887 333.869498,231.728287 C333.869498,225.724487 284.048298,220.858087 222.589098,220.858087 Z" id="路径" fill-opacity="0.340116914" fill="#3398F7" filter="url(#filter-4)"></path>
                    <g id="编组-2" transform="translate(137.592237, 63.622285)">
                        <rect id="路径" x="0" y="0" width="202.254365" height="179.999244"></rect>
                        <path d="M84.8418758,166.211652 L153.184876,179.999052 L82.2370758,93.5918524 L11.2914758,93.5918524 L84.8418758,166.211652 Z" id="路径" fill="url(#linearGradient-5)"></path>
                        <path d="M84.8415552,166.211319 L153.184555,179.998719 L107.343155,124.167119 L84.8415552,166.211319 Z" id="路径" fill="url(#linearGradient-6)"></path>
                        <g id="路径-125" transform="translate(11.392352, 0.041190)">
                            <rect id="路径" x="0" y="0" width="190.559091" height="179.958048"></rect>
                            <path d="M70.8466,93.5506 L141.7922,179.9578 L190.2428,105.0346 C190.6718,104.3724 190.6652,103.5166 190.2252,102.861 L121.6204,0.1958 C121.5236,0.0506 121.352,-0.022 121.1804,0.0066 L34.8348,14.5596 C34.6148,14.597 34.4278,14.7422 34.3398,14.9468 L0,93.5506 L70.8466,93.5506 Z" id="路径" fill="url(#linearGradient-7)"></path>
                            <path d="M190.2428,105.0346 C190.6718,104.3724 190.6652,103.5166 190.2252,102.861 L121.6204,0.1958 C121.5236,0.0506 121.352,-0.022 121.1804,0.0066 L34.8348,14.5596 C34.6148,14.597 34.4278,14.7422 34.3398,14.9468 L0,93.5506 L70.8466,93.5506 L141.7922,179.9578 L190.2428,105.0346 Z" id="路径"></path>
                        </g>
                        <path d="M153.183726,179.9996 L202.184326,104.2272 C202.278926,104.0798 202.276726,103.8884 202.179926,103.7432 L132.853526,0 L82.2381256,93.5924 L153.183726,179.9996 Z" id="路径" fill="url(#linearGradient-8)"></path>
                        <path d="M126.111315,35.6496716 C126.256515,35.3900716 126.612915,35.3438716 126.817515,35.5616716 L126.817515,35.5616716 L148.102515,58.2018716 C150.408115,60.6526716 151.197915,64.5906716 149.866915,66.9996716 C148.535915,69.4064716 145.587915,69.3712716 143.284515,66.9204716 L143.284515,66.9204716 L121.803715,44.0756716 C121.673915,43.9348716 121.647515,43.7280716 121.739915,43.5608716 L121.739915,43.5608716 Z M133.339139,22.570303 C133.484339,22.310703 133.840739,22.264503 134.045339,22.482303 L134.045339,22.482303 L146.149739,35.356703 C148.455339,37.809703 149.245139,41.747703 147.914139,44.156703 C146.583139,46.563503 143.635139,46.528303 141.331739,44.077503 L141.331739,44.077503 L129.031539,30.996303 C128.901739,30.855503 128.875339,30.648703 128.967739,30.481503 L128.967739,30.481503 Z" id="形状结合" fill="url(#linearGradient-9)" opacity="0.18"></path>
                        <path d="M82.2382,93.5919392 L84.777,32.5551392 C84.81,31.7433392 84.1082,31.0921392 83.3008,31.1889392 L1.1638,41.0559392 C0.3872,41.1505392 -0.1386,41.8919392 0.033,42.6553392 L11.3916,93.5919392 L82.2382,93.5919392 Z" id="路径" fill="url(#linearGradient-10)"></path>
                        <path d="M153.183726,180.000294 L201.183326,167.367894 C201.757526,167.216094 202.019326,166.551694 201.704726,166.047894 L134.252726,58.6878937 C133.713726,57.8276937 132.563126,57.6010937 131.738126,58.1906937 L82.2381256,93.5908937 L153.183726,180.000294 Z" id="路径" fill="url(#linearGradient-11)"></path>
                    </g>
                    <g id="编组-3" transform="translate(197.741981, 190.837902)">
                        <rect id="路径" x="0" y="0" width="14.4925052" height="38.7285367"></rect>
                        <path d="M14.4936,36.9072 L14.4936,2.2374 C14.4936,2.1384 14.4276,2.0526 14.333,2.024 L7.2468,0 L0.1606,2.024 C0.066,2.0526 0,2.1384 0,2.2374 L0,36.9072 C0,37.1162 0.1474,37.2966 0.352,37.3384 L7.2468,38.7288 L14.1394,37.3384 C14.344,37.2966 14.4936,37.1162 14.4936,36.9072 Z" id="路径" fill="url(#linearGradient-12)"></path>
                        <path d="M7.2468,0 C3.245,0 0,0.9262 0,2.0702 C0,3.2142 3.245,4.1404 7.2468,4.1404 C11.2486,4.1404 14.4936,3.2142 14.4936,2.0702 C14.4936,0.9262 11.2486,0 7.2468,0 Z" id="路径" fill="url(#linearGradient-13)"></path>
                    </g>
                    <path d="M162.542708,227.438837 C162.542708,230.325237 167.263908,232.245837 176.288308,232.245837 C185.312708,232.245837 190.036108,230.323037 190.036108,227.438837 C190.036108,219.802637 183.880508,213.611837 176.288308,213.611837 C168.696108,213.611837 162.542708,219.802637 162.542708,227.438837 Z" id="路径" fill="url(#linearGradient-14)"></path>
                    <g id="路径">
                        <use fill="url(#linearGradient-15)" fill-rule="evenodd" xlink:href="#path-16"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                    </g>
                    <g id="多边形" transform="translate(218.447631, 223.532623)">
                        <rect id="路径" x="0" y="0" width="10.3041338" height="9.7306813"></rect>
                        <path d="M10.175,7.733 L5.9884,0.484 C5.9048,0.3366 5.7816,0.2134 5.6342,0.1298 C5.1722,-0.1364 4.5826,0.022 4.3142,0.484 L0.1298,7.733 C0.044,7.8804 0,8.0454 0,8.217 C0,8.7494 0.4334,9.1828 0.9658,9.1828 C2.4398,9.548 3.83533333,9.7306 5.1524,9.7306 C6.468,9.7306 7.8628,9.548 9.3368,9.1828 C9.5062,9.1828 9.6734,9.1388 9.8208,9.053 C10.2828,8.7868 10.4412,8.195 10.175,7.733 Z" id="路径" fill="url(#linearGradient-18)"></path>
                        <path d="M5.9884,0.484 C5.9048,0.3366 5.7816,0.2134 5.6342,0.1298 C5.1722,-0.1364 4.5826,0.022 4.3142,0.484 L0.1298,7.733 C0.044,7.8804 0,8.0454 0,8.217 C0,8.7494 0.4334,9.1828 0.9658,9.1828 C2.4398,9.548 3.83533333,9.7306 5.1524,9.7306 C6.468,9.7306 7.8628,9.548 9.3368,9.1828 C9.5062,9.1828 9.6734,9.1388 9.8208,9.053 C10.2828,8.7868 10.4412,8.195 10.175,7.733 L5.9884,0.484 Z" id="路径" fill="url(#linearGradient-19)"></path>
                    </g>
                    <path d="M98.6897103,184.809271 C85.7295103,164.813471 64.7151103,195.426471 61.1841103,195.426471 C57.6531103,195.426471 47.3857103,180.976871 40.2687103,190.450071 C33.1517103,199.923271 11.3981103,208.067671 18.6955103,211.915471 C25.9951103,215.763271 123.27251,217.851071 121.78311,210.135671 C120.14631,201.645871 111.64771,204.802871 98.6897103,184.809271 Z" id="路径" fill="url(#linearGradient-20)" opacity="0.321056548"></path>
                    <g id="编组-13备份" transform="translate(108.437098, 223.000000)">
                        <rect id="路径" x="0" y="0" width="58.9999108" height="25.9989124"></rect>
                        <path d="M29.5,0 C13.2086357,0 0,5.82007084 0,13 C0,20.1799292 13.2086357,26 29.5,26 C45.7913643,26 59,20.1799292 59,13 C59,5.82007084 45.7913643,0 29.5,0 Z" id="路径" fill="url(#radialGradient-21)" filter="url(#filter-22)"></path>
                        <path d="M27.5640878,6.40041701 L8.72372778,6.40041701 L8.72372778,10.749377 C8.72372778,12.347897 12.9402478,13.647657 18.1423678,13.647657 C23.3475678,13.647657 27.5640878,12.347897 27.5640878,10.749377 L27.5640878,6.40041701 Z" id="路径" fill="url(#linearGradient-23)"></path>
                        <path d="M24.7482647,10.7408726 L38.6247689,10.7408726 C38.7948726,10.7408726 38.9327689,10.8787689 38.9327689,11.0488726 L38.9327689,13.3313735 C38.9327689,13.5014772 38.7948726,13.6393735 38.6247689,13.6393735 L24.7482647,13.6393735 C24.578161,13.6393735 24.4402647,13.5014772 24.4402647,13.3313735 L24.4402647,11.0488726 C24.4402647,10.8787689 24.578161,10.7408726 24.7482647,10.7408726 Z" id="矩形" fill="url(#linearGradient-24)" transform="translate(31.686517, 12.190123) rotate(14.000000) translate(-31.686517, -12.190123) "></path>
                        <use id="路径" stroke="#707070" mask="url(#mask-26)" stroke-width="1.54" fill="#EEFAFF" stroke-dasharray="0,0" xlink:href="#path-25"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>