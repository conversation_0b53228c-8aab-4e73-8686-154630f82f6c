<script setup lang="ts">
import { getErrorExamQuestionListApi, getErrorExamReportApi } from '@/api/taskCenter'
import QuestionStemItem from '@/views/teacher/task/questionReport/components/QuestionStemItem.vue'
import ReportCard from './ReportCard.vue'

const props = defineProps({
  examId: {
    type: String,
    default: '',
  },
  examPaperId: {
    type: String,
    default: '',
  },
  sysGradeId: {
    type: String,
    default: '',
  },
  sysCourseId: {
    type: String,
    default: '',
  },

})
const emit = defineEmits(['handleScroll'])
let questionList = $ref<any>([])
const route = useRoute()
const router = useRouter()
let showLoading = $ref(true)
watchDebounced(() => [props.examId,
props.examPaperId,
props.sysGradeId,
props.sysCourseId], () => {
  getList()
  getStudentReport()
}, {
  debounce: 150,
  immediate: true,
})
let reportList = $ref<any>([])
/* 获取报告列表 */
async function getStudentReport() {
  try {
    showLoading = true
    if (!props.examId || !props.examPaperId)
      return

    const { paperReport } = await getErrorExamReportApi({
      examId: props.examId,
      examPaperId: props.examPaperId,
      sysSubjectId: route.query.subjectId,
      sysGradeId: props.sysGradeId,
      sysCourseId: props.sysCourseId,
    })
    reportList = paperReport.questionList
  }
  catch (e) {
    showLoading = false
    console.error(e)
  }
}
/* 获取对应题目的报告 */
function getQuestionReport(questionId) {
  return reportList.find(item => item.questionId == questionId)
}
/* 获取列表 */
async function getList() {
  try {
    showLoading = true
    if (!props.examId || !props.examPaperId) {
      showLoading = false
      questionList = []
      return
    }
    let res = await getErrorExamQuestionListApi({
      examId: props.examId,
      examPaperId: props.examPaperId,
      sysGradeId: props.sysGradeId,
      sysCourseId: props.sysCourseId,
      sysSubjectId: route.query.subjectId,
    })
    showLoading = false
    questionList = res || []
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (err) {
    showLoading = false
    questionList = []
    console.log('获取列表失败', err)
  }
}
/* 查看详情 */
function goDetail(item, index) {
  emit('handleScroll')
  router.push({
    name: 'ErrorQuestionDetail',
    query: {
      questionId: item.questionId,
      examId: props.examId,
      examPaperId: props.examPaperId,
      sysGradeId: props.sysGradeId,
      sysCourseId: props.sysCourseId,
      sysSubjectId: route.query.subjectId,
      quesIndex: index,
    },
  })
}

function view(item, type) {
  if (type == 'statistics') {
    item.showAnswer = false
    item.showStatistics = !item.showStatistics
  }
  else {
    item.showStatistics = false
    item.showAnswer = !item.showAnswer
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}
</script>

<template>
  <div>
    <g-loading v-if="showLoading" class="h-200px"></g-loading>

    <template v-else>
      <template v-if="questionList.length">
        <QuestionStemItem
          v-for="(item, index) in questionList"
          :id="`ques-item-${item.questionIndex}`"
          :key="item.questionId"
          :question-item="item"
          :index="index + 1"
        >
          <template #footer>
            <div>
              <!-- 统计、按钮 -->
              <div class="mt-12px flex justify-between ml-16px">
                <div class="flex items-center text-15px text-[#666666] font-400">
                  <div>
                    正确率：<span
                      v-if="typeof item.correctRate === 'number'"
                      :class="{
                        'text-[#F5222D]': 60 > item.correctRate,
                        'text-[#FAAD14]':
                          60 <= item.correctRate
                          && item.correctRate < 70,
                        'text-[#1EA0F0]':
                          70 <= item.correctRate
                          && item.correctRate < 85,
                        'text-[#52C41A]': 85 <= item.correctRate,
                      }"
                    >
                      {{ item.correctRate }}%
                    </span>
                    <span v-else>--</span>
                  </div>
                  <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                  <el-popover
                    trigger="hover"
                    :teleported="false"
                    :width="200"
                    :disabled="!item.errorNum"
                  >
                    <template #reference>
                      <div :class="item.errorNum && 'cursor-pointer'">
                        {{ item.errorNum }}人错误
                      </div>
                    </template>
                    <div class="pb-20px max-h-200px overflow-y-auto">
                      <div class="w-full flex items-center mb-8px">
                        <div class="flex items-center w-246px">
                          <img
                            src="@/assets/img/taskCenter/cross.png"
                            alt="cross icon"
                            class="w-16px h-16px mr-6px"
                          />
                          <div class="text-14px text-[#333]">
                            错误
                          </div>
                        </div>
                      </div>
                      <div
                        class="flex flex-wrap gap-10px"
                      >
                        <template
                          v-if="item.errorStudentList?.length"
                        >
                          <div
                            v-for="(errorItem, errorIdx) in item
                              .errorStudentList"
                            :key="errorIdx"
                            class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                          >
                            {{ errorItem.studentName }}
                          </div>
                        </template>
                        <span v-else class="text-[#999]">无学生</span>
                      </div>
                    </div>
                  </el-popover>
                  <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                  <el-popover
                    trigger="hover"
                    :teleported="false"
                    :width="200"
                    :disabled="!item.answerNum"
                  >
                    <template #reference>
                      <div :class="item.answerNum && 'cursor-pointer'">
                        {{ item.answerNum }}人作答
                      </div>
                    </template>
                    <div class="pb-15px max-h-200px overflow-y-auto">
                      <div class="flex items-center mb-8px">
                        <img
                          src="@/assets/img/taskCenter/pen.png"
                          alt="pen icon"
                          class="w-16px h-16px mr-6px"
                        />
                        <div class="text-14px text-[#333]">
                          作答
                        </div>
                      </div>
                      <div
                        class="flex flex-wrap gap-10px"
                      >
                        <template
                          v-if="item.answerStudentList?.length"
                        >
                          <div
                            v-for="(finishItem, finishIdx) in item.answerStudentList"
                            :key="finishIdx"
                            class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                          >
                            {{ finishItem.studentName }}
                          </div>
                        </template>
                        <span v-else class="text-[#999]">无学生</span>
                      </div>
                    </div>
                  </el-popover>
                  <div class="w-1px h-10px bg-[#CCCCCC] mx-12px"></div>
                  <el-popover

                    trigger="hover"
                    :teleported="false"
                    :width="200"
                    :disabled="!item.noAnswerNum"
                  >
                    <template #reference>
                      <div :class="item.noAnswerNum && 'cursor-pointer'">
                        {{
                          item.noAnswerNum ?? 0
                        }}人未作答
                      </div>
                    </template>
                    <div class="pb-15px max-h-200px overflow-y-auto">
                      <div class="flex items-center mb-8px">
                        <img
                          src="@/assets/img/taskCenter/warn.png"
                          alt="warn icon"
                          class="w-16px h-16px mr-6px"
                        />
                        <div class="text-14px text-[#333]">
                          未作答
                        </div>
                      </div>
                      <div
                        class="flex flex-wrap gap-10px"
                      >
                        <template
                          v-if="item.noAnswerStudentList?.length"
                        >
                          <div
                            v-for="(unfinishItem, unfinishIdx) in item
                              .noAnswerStudentList"
                            :key="unfinishIdx"
                            class="w-68px h-24px text-center leading-[24px] bg-[#F8F8F8] rounded-[4px] text-14px text-[#666] truncate"
                          >
                            {{ unfinishItem.studentName }}
                          </div>
                        </template>
                        <span v-else class="text-[#999]">无学生</span>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <div class="flex items-center">
                  <div
                    class="mr-17px cursor-pointer px-11px py-3px text-[15px] border-[1px] border-solid border-[#6474FD] text-[#6474FD] rounded-[4px]"
                    :class="{ 'bg-[#ECEFFF] border-[#ECEFFF]': item.showAnswer }"
                    @click="view(item, 'answer')"
                  >
                    答案解析
                  </div>
                  <div
                    class="cursor-pointer px-11px py-3px text-[15px] border-[1px] border-solid border-[#6474FD] text-[#6474FD] rounded-[4px]"
                    :class="{ 'bg-[#ECEFFF] border-[#ECEFFF]': item.showStatistics }"
                    @click="view(item, 'statistics')"
                  >
                    作答统计
                  </div>
                </div>
              </div>
              <!-- 学生答题情况 -->
              <div v-if="item.showStatistics">
                <ReportCard
                  :current-ques="getQuestionReport(item.questionId)"
                  :exam-id="examId"
                  :exam-paper-id="examPaperId"
                  :sys-course-id="sysCourseId"
                />
              </div>
              <!-- 答案解析 -->
              <div v-if="item.showAnswer" class="p-17px">
                <div class="text-17px text-[#333] font-600 mb-13px">
                  答案解析
                </div>
                <div class="flex items-start">
                  <div class="text-[15px] text-[#6474FD] mr-13px flex-shrink-0">
                    【详情】
                  </div>
                  <div class="text-16px text-[#333] pb-18px border-b border-dashed border-[#CCCCCC] ">
                    <div
                      v-for="(v, i) in item?.subQuestions"
                      :key="i"
                      class="flex items-start"
                      :class="{
                        'mt-10px': i !== 0,
                      }"
                    >
                      <div v-if="item?.subQuestions.length > 1" class="mr-5px">
                        ({{ i + 1 }})
                      </div>
                      <g-mathjax :text="v.subQuestionParse" class="text-16px" />
                    </div>
                  </div>
                </div>
                <div class="flex items-start mt-13px">
                  <div class="text-[15px] text-[#6474FD] mr-13px flex-shrink-0">
                    【答案】
                  </div>
                  <div class="text-16px text-[#333] pb-18px   mb-17px">
                    <div
                      v-for="(v, i) in item?.subQuestions"
                      :key="i"
                      class="flex items-start"
                      :class="{
                        'mt-10px': i !== 0,
                      }"
                    >
                      <div v-if="item?.subQuestions.length > 1" class="mr-5px">
                        ({{ i + 1 }})
                      </div>
                      <g-mathjax :text="v.subQuestionAnswer" class="text-16px" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </QuestionStemItem>
      </template>
      <g-empty v-else :size="200"></g-empty>
    </template>
  </div>
</template>

<style lang="scss" scoped>

</style>
