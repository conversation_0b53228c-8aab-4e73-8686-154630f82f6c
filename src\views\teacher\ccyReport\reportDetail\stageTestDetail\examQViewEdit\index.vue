<script setup lang="ts" name="ExamQViewEdit">
import { getPeriodTestStudentQuestionList } from '@/api/activity'
import QuestionItem from '@/views/teacher/task/createTask/components/QuestionItem.vue'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const route = useRoute()
let paperQuestionList = $ref<any>([])
let rightLoading = $ref(true)

async function getQuestionListApi() {
  if (!route.query?.studentId) {
    paperQuestionList = []
    rightLoading = false
    return
  }
  try {
    rightLoading = true
    const list = await getPeriodTestStudentQuestionList({
      studentTrainPeriodTestSchoolStudentId: route.query?.studentId,
    })
    paperQuestionList = list || []
    rightLoading = false
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    paperQuestionList = []
    rightLoading = false
    console.error(e)
  }
}

onBeforeMount(() => {
  getQuestionListApi()
})
</script>

<template>
  <div class="px-26px pt-26px">
    <g-navbar title="返回" class="mb-17px" />
    <div class="h-[calc(100vh-26px-34px-17px)]">
      <div class="h-full w-301px hidden">
        <el-scrollbar class="pr-10px">
          <div class="h-300px mb-13px border border-[#DADDE8] rounded-[6px] bg-white px-19px py-13px">
            <div class="text-15px text-[#333] font-600 h-21px leading-[21px] mb-15px">
              学生信息
            </div>
            <div class="text-13px">
              <div>启鸣号：23232323432</div>
              <div class="py-12px">
                学生姓名：张三
              </div>
              <div>学生班级：高三五班</div>
              <div class="py-12px">
                测试学科：数学
              </div>
              <div>
                测试时间：
                <div class="py-8px">
                  <span class="text-[#fff] bg-[#8CE48C] rounded-[4px] p-2px">始</span>
                  2025-04-01 10:00:00
                </div>
                <div>
                  <span class="text-[#fff] bg-[#6474FD] rounded-[4px] p-2px">结</span>
                  2025-04-01 10:00:00
                </div>
              </div>
              <div class="pt-12px">
                测试时长：<span class="text-[#FC5B68]">01分22秒</span>
              </div>
            </div>
          </div>
          <div class="border border-[#DADDE8] rounded-[6px] bg-white px-19px py-13px">
            <div class="text-15px text-[#333] font-600 h-21px leading-[21px] mb-15px">
              同期学生
            </div>
            <div class="h-[calc(100vh-26px-34px-17px-400px)] overflow-auto no-bar text-13px">
              <div v-for="i in 20"
                   :key="i"
                   class="flex justify-between items-center mb-13px cursor-pointer"
              >
                <div class="line-1">
                  张三
                </div>
                <span class="px-20px flex-shrink-0">3/30题</span>
                <span class="text-[#6474FD] flex-shrink-0">查看</span>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <g-loading v-if="rightLoading" class="h-200px"></g-loading>
      <g-empty v-else-if="!rightLoading && !paperQuestionList?.length"></g-empty>
      <template v-else>
        <DynamicScroller
          ref="scrollerRef"
          :items="paperQuestionList"
          :min-item-size="300"
          :buffer="500"
          key-field="questionId"
          class="w-full h-full"
        >
          <template #default="{ item, index, active }">
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :size-dependencies="['questionTitle', 'subQuestions', 'showParse']"
              :data-index="index"
              class="pb-1px"
            >
              <div
                class="border border-solid border-[#DADDE8] rounded-[6px] bg-white mb-9px"
              >
                <QuestionItem
                  :question-item="item"
                  :question-index="index"
                  :need-line="false"
                  render-if-change
                >
                  <template #tagList>
                    <div></div>
                  </template>
                  <template #footerAction>
                    <div></div>
                  </template>
                </QuestionItem>
              </div>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </template>
    </div>
  </div>
</template>
