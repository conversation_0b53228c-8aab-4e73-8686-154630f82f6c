{"extends": "@tsconfig/node22/tsconfig.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "jsx": "preserve", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["./src/*"], "/#/*": ["./types/*"]}, "types": ["node"], "noImplicitAny": false, "noEmit": true}, "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "vite/*", "types/**/*", "src/**/*", "src/**/*.vue"]}