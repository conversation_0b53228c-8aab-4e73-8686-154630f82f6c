<script setup lang="ts">
import AnswerParse from './AnswerParse.vue'

const props = defineProps({
  questionItem: {
    type: Object,
    required: true,
  },
  order: {
    type: String,
    default: '',
  },
})
const cardItem: any = $computed(() => {
  return {
    ...(props.questionItem ?? {}),
    subQuestionList: props.questionItem.subQuestions?.map((h) => {
      return {
        ...h,
        optionArr: Object.keys(h)
          .filter(
            key => key.includes('option') && h[key] && key !== 'optionNumbers',
          )
          .map((realKey) => {
            return {
              name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
              title: h[realKey].toString(),
            }
          }),
      }
    }),
  }
})
let currentSubIndex = $ref(0)
function selectQues(item: any, index: number) {
  currentSubIndex = index
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}

const currentSubItem = $computed(() => {
  if (cardItem.subQuestionList) return cardItem.subQuestionList[currentSubIndex]
})
let showAnswer = $ref(false)
function openAnswer() {
  props.questionItem.showAnswer = !props.questionItem.showAnswer
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}

watch(() => props.questionItem, () => {
  nextTick($g.tool.renderMathjax)
}, { deep: false })
</script>

<template>
  <div>
    <!-- 大题题干 -->
    <g-mathjax
      v-if="cardItem.questionTitle"
      :text="cardItem.questionTitle"
      class="flex-shrink-0 mb-12px text-16px"
      :font-width="16"
      :order="order"
    />
    <!-- 题号选择 -->
    <div v-if="cardItem.subQuestionList?.length > 1" class="flex items-center my-14px">
      <div v-for="(item, index) in cardItem.subQuestionList" :key="index">
        <div
          class="w-[43px] bg-[rgba(100,116,253,0.1)] mr-17px h-43px rounded-[50%] text-center leading-[43px] border border-solid border-[transparent] mb-5px text-15px  cursor-pointer active:opacity-80 select-none"
          :class="{
            'item-active': currentSubIndex === index,
          }"
          @click.stop="selectQues(item, index)"
        >
          {{ item.questionIndex ?? index + 1 }}
        </div>
      </div>
    </div>
    <g-mathjax :text="currentSubItem.subQuestionTitle"
               :order="cardItem.questionTitle ? '' : order"
               class="text-16px"
    />
    <div
      v-for="(item, subIndex) in currentSubItem.optionArr"
      :key="item.name"
      class="flex items-center  text-15px"
      :class="[subIndex !== currentSubItem.optionArr.length - 1 && 'mb-12px',
               questionItem.questionTitle && currentSubItem.subQuestionTitle ? 'pl-32px' : 'pl-16px']"
    >
      <div class="flex-shrink-0">
        {{ item.name }}.
      </div>
      <g-mathjax :text="item.title" class="text-15px"></g-mathjax>
    </div>
    <div class="myDashed my-17px"></div>
    <AnswerParse v-if="questionItem.showAnswer" :sub-question="currentSubItem" />
    <div class="flex items-center justify-between mt-15px">
      <div
        class="flex items-center w-100px cursor-pointer"
        @click="openAnswer()"
      >
        <svg-menu-eye-close v-if="!questionItem.showAnswer" class="w-17px h-17px"></svg-menu-eye-close>
        <svg-menu-eye-open v-else class="w-17px h-17px"></svg-menu-eye-open>
        <div class="text-15px text-[#6474FD] ml-5px ">
          答案及解析
        </div>
      </div>
      <slot name="footerRight"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.item-active {
    border: 1px solid #6474FD!important;
}
.myDashed{
  height: 1px;
  background: linear-gradient(
    to left,
    transparent 0%,
    transparent 50%,
    #ccc 50%,
    #ccc 100%
);
background-size: 10px 1px;
background-repeat: repeat-x;
}
</style>
