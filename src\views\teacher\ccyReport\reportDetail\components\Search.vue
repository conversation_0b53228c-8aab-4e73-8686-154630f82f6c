<script setup lang="ts">
import {
  getClassList,
  getGradeList,
  getSchoolList,
} from '@/api/activity'
import { shortcuts } from '../constant'

let emit = defineEmits(['getList'])

let route = useRoute()

let activeTab = $ref<any>('')
let activityId = $ref<any>('')
let schoolId = $ref<any>('')
let schoolList = $ref<any>([])
let gradeList = $ref<any>([])
let gradeId = $ref<any>('')
let classList = $ref<any>([{
  label: '全部',
  value: 'all',
}])
let classId = $ref<any>('')
let dateRange = $ref<any>(
  ['', ''],
)
let isFirst = $ref<boolean>(true)
const selectStyle = $computed(() => {
  return $g.isPC ? 'w-256px' : 'w-135px'
})

/* 获取学校 */
async function fetchSchoolList() {
  try {
    let res = await getSchoolList({
      activityId,
      sysCourseId: activeTab,
    })
    schoolList = res?.map((v) => {
      return {
        ...v,
        label: v.schoolName,
        value: v.schoolId,
      }
    }) || []
    schoolId = route.query?.schoolId ? Number(route.query?.schoolId) : schoolList.find(v => v.defaultSelected)?.value
    schoolId && await fetchGradeList()
  }
  catch (err) {
    console.log('获取学校失败', err)
  }
}
/* 获取年级 */
async function fetchGradeList() {
  try {
    let res = await getGradeList({
      schoolId,
      activityId,
      sysCourseId: activeTab,
    })
    gradeList = res?.map((v) => {
      return {
        ...v,
        label: v.sysGradeName,
        value: v.sysGradeId || 'all',
      }
    }) || []
    if (isFirst) {
      gradeId = route.query?.sysGradeId ? Number(route.query?.sysGradeId) : 'all'
      isFirst = false
    }
    else {
      gradeId = gradeList?.[0]?.value
    }
    await fetchClassList()
  }
  catch (err) {
    console.log('获取年级失败', err)
  }
}

/* 获取班级 */
async function fetchClassList() {
  try {
    let res = [] as any
    res = await getClassList({
      activityId,
      schoolId,
      sysGradeId: gradeId == 'all' ? '0' : gradeId,
      sysCourseId: activeTab,
    })
    classList = res.length
      ? res?.map((v) => {
        return {
          ...v,
          label: v.className,
          value: v.schoolClassId || 'all',
        }
      })
      : classList
    if (isFirst) {
      classId = route.query?.schoolClassId ? Number(route.query.schoolClassId) : 'all'
      isFirst = false
    }
    else {
      classId = 'all'
    }
  }
  catch (err) {
    console.log('获取班级失败', err)
  }
}
// 切换年级
async function changeGrade(id) {
  gradeId = id
  await fetchClassList()
  emit('getList', {
    schoolId,
    gradeId,
    classId,
    dateRange,
  })
}
// 切班级
function changeClass(item) {
  classId = item.value
  emit('getList', {
    schoolId,
    gradeId,
    classId,
    dateRange,
  })
}
// 切换日期
const changDateRange = useDebounceFn(async (val) => {
  if (val?.[0] == 'Invalid Date' || val?.[1] == 'Invalid Date')
    dateRange = ['', '']

  else
    dateRange = val ?? ['', '']

  setDefaultSelected()
  emit('getList', {
    schoolId,
    gradeId,
    classId,
    dateRange,
  })
}, 100)
// 日期选择器显示隐藏
function handleVisibleChange(visible) {
  if (visible)
    setDefaultSelected()
}
// 设置日期选择器选中样式
function setDefaultSelected() {
  // shortcuts 数据格式化
  const tempDateList = shortcuts.map(item => ({
    ...item,
    value: item.value(),
  }))
  // 全部时返回【'Invalid Date'，'Invalid Date'】
  const isInvalidDate = dateRange?.[0] === 'Invalid Date' || dateRange?.[1] === 'Invalid Date'
  const isEmptyDate = dateRange?.[0] == '' || dateRange?.[1] == ''
  // 对应的选中index
  const selectedItemIndex = isInvalidDate || isEmptyDate
    ? 0
    : tempDateList.findIndex(
        v => v.value[0] == $g.dayjs(dateRange?.[0]).format('YYYY-MM-DD HH:mm') && v.value[1] == $g.dayjs(dateRange?.[1]).format('YYYY-MM-DD HH:mm'),
      )
  const sidebarDom = document.querySelector('.el-picker-panel__sidebar')
  if (sidebarDom?.children) {
    Array.from(sidebarDom.children).forEach((child: any, index) => {
      const isSelected = index === selectedItemIndex
      child.style.backgroundColor = isSelected ? '#F5F7FF' : ''
      child.style.color = isSelected ? '#6474FD' : ''
    })
  }
}
onMounted(async () => {
  activityId = route.query?.activityId
  activeTab = route.query?.activeTab
  dateRange = [route.query?.beginDateTime ?? '', route.query?.endDateTime ?? '']
  await fetchSchoolList()
  emit('getList', {
    schoolId,
    gradeId,
    classId,
    dateRange,
  })
})
</script>

<template>
  <div class="bg-white p-17px rounded-[6px]">
    <div
      class="flex items-center justify-between mb-17px"
    >
      <div
        class="flex items-center"
      >
        <div class="flex-shrink-0 mr-13px">
          学校
        </div>
        <el-select v-model="schoolId"
                   :class="selectStyle"
                   @change="fetchGradeList"
        >
          <el-option
            v-for="item in schoolList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="flex items-center">
        <div class="flex-shrink-0 mr-13px">
          时间范围
        </div>
        <el-date-picker
          v-model="dateRange"
          :class="$g.isPC ? 'w-400px' : 'w-360px'"
          type="datetimerange"
          :shortcuts="shortcuts"
          :teleported="false"
          clearable
          :editable="false"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          unlink-panels
          @change="changDateRange"
          @visible-change="handleVisibleChange"
        />
      </div>
    </div>
    <div>
      <div class="flex items-top mb-[17px]">
        <div class="w-fit  font-[600] text-[15px] text-[#333] leading-[30px]  mr-[10px]">
          年级
        </div>
        <div class="flex flex-wrap flex-1">
          <div
            v-for="item in gradeList"
            :key="item"
            class="min-w-[49px] px-[11px]  h-[30px]  rounded-[5px] cursor-pointer mr-[8px] text-[15px] text-[#6C6C74] leading-[30px] text-center"
            :class="{ 'bg-[#ECEFFF] !text-[#6474FD]': gradeId === item.value }"
            @click="changeGrade(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="flex items-top">
        <div class="w-fit  font-[600] text-[15px] text-[#333]  mr-[10px] leading-[30px]">
          班级
        </div>
        <div class="flex flex-wrap flex-1">
          <div
            v-for="item in classList"
            :key="item"
            class="min-w-[49px] px-[11px]  h-[30px]   rounded-[5px] cursor-pointer mr-[8px] text-[15px] text-[#6C6C74] leading-[30px] text-center"
            :class="{ 'bg-[#ECEFFF] !text-[#6474FD]': classId === item.value }"
            @click="changeClass(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
