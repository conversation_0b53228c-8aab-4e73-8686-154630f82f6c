<script setup lang="ts" name="AiCorrectionMain">
import { getAutoCorrectResultPage } from '@/api/ccyReport'

const props = defineProps<{
  currentSubject: number | string
}>()

const route = useRoute()
const router = useRouter()

let dataList: any = $ref([])
const pageOption = reactive({
  page: 1,
  page_size: 5,
  total: 0,
})
async function pulldown() {
  pageOption.page = 1
  await getDataListApi()
}
async function pullup() {
  pageOption.page += 1
  await getDataListApi(true)
}
/* 获取列表 */
async function getDataListApi(up?) {
  try {
    const res = await getAutoCorrectResultPage({
      page: pageOption.page,
      pageSize: pageOption.page_size,
      sysCourseId: props.currentSubject,
      accountId: route.query.accountId,
    })
    if (res?.list?.length) {
      dataList = up ? dataList.concat(res.list) : res.list
      pageOption.total = res.total
    }
    else {
      dataList = []
    }
    await nextTick()
    $g.tool.renderMathjax()
  }
  catch (e) {
    console.error(e)
  }
}

function getClassName(type: number) {
  switch (type) {
    case 9:
    case 16:
    case 17:
      return 'bg-[#19D7A817] border-[#19D7A826] text-[#15C69A]'
    case 11:
      return 'bg-[#FBB30A08] border-[#FBB30A33] text-[#FBB30A]'
    case 10:
      return 'bg-[#52C45117] border-[#52C45126] text-[#52C451]'
    case 12:
      return 'bg-[#1EA0F008] border-[#1EA0F024] text-[#3496FA]'
    case 13:
      return 'bg-[#FC5B6817] border-[#FC5B6826] text-[#FC5B68]'
    case 4:
    case 14:
      return 'bg-[#F3F4F9] border-[#6474FD24] text-[#6474FD]'
    case 15:
      return 'bg-[#8034FA08] border-[#8034FA24] text-[#8034FA]'
    case 8:
      return 'bg-[#FFF2E3] border-[#FDA86424] text-[#FF7400]'
    default:
      return 'bg-[#FFF9F4] border-[#FF9A4624] text-[#FF7400]'
  }
}

async function handleClick(item) {
  router.push({
    name: 'AiCorrectionDetail',
    query: {
      title: item.name,
      exerciseTaskId: item.exerciseTaskId,
    },
  })
}

watch(() => props.currentSubject, () => {
  pulldown()
}, {
  immediate: true,
})
</script>

<template>
  <div class="box-h rounded-[6px]">
    <el-scrollbar>
      <g-list
        ref="scrollRef"
        v-model:data="dataList"
        :page-option="pageOption"
        url="/tutoring/student/questionAutoCorrect/ai/result/page"
        @pulldown="pulldown"
        @pullup="pullup"
      >
        <div v-for="(item, index) in dataList"
             :key="index"
             class="mb-17px"
        >
          <div class="leading-[21px] text-15px text-[#333] mb-17px font-600">
            {{ item.dateStr }}
          </div>
          <div
            v-for="(each, idx) in item.list"
            :key="idx"
            class="bg-[#fff] rounded-[4px] p-17px border border-solid border-[#DCDFE6] text-13px cursor-pointer van-haptics-feedback select-none relative"
            :class="{
              'mt-17px': idx !== 0,
            }"
            @click="handleClick(each)"
          >
            <div class="w-full flex items-center mb-17px">
              <div
                class="flex-shrink-0 px-11px h-29px mr-13px leading-[29px] border border-solid rounded-[4px]"
                :class="getClassName(each.exerciseTaskType)"
              >
                {{ each.exerciseTaskTypeName }}
              </div>
              <div
                class="flex-grow font-600 truncate leading-[21px] text-15px text-[#333]"
              >
                <g-mathjax
                  :text="each.name"
                  class="title-row-math truncate"
                />
              </div>
            </div>
            <div class="w-full leading-[18px] text-right text-[#999999]">
              {{ each.dateTime }}
            </div>
          </div>
        </div>
      </g-list>
    </el-scrollbar>
  </div>
</template>

<style scoped lang="scss">
.box-h{
  height: calc(100vh - 26px - 26px - 34px - 17px - 83px - 15px - 30px - 17px - 34px);
}
</style>
