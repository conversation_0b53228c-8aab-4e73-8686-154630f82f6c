# 三端模版

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## 框架改动

1. svg支持按需引入，移除g-icon，支持remixicon图标库，支持本地图标库，本地图标颜色改不动，修改fill="currentColor"，大小修改为width="1.2em" height="1.2em"

   ```html
   <svg-ri-account-box-line class="text-40px"></svg-ri-account-box-line> //
   remixicon图标库 <svg-common-tip class="text-20px"></svg-common-tip> //
   本地common文件夹内图标
   ```

2. 依然沿用学习机开发尺寸，设计图改为1024

3. .env-cmdrc.cjs改为js模式，支持注释
