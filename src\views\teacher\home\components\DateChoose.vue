<script setup lang="ts">
const props = defineProps({
  // 是否禁止日期与单选功能
  isDisabled: {
    type: Boolean,
    default: false,
  },
  // 禁用时间
  recommendations: {
    type: String,
    default: '',
  },
  // 不限时
  unlimited: {
    type: Boolean,
    default: false,
  },
})
const radio = defineModel<string>('radioValue')
const time = defineModel<any>('time')

// 禁止选择的日期
const disabledDay = $computed(() => {
  return props.recommendations.split(' ')[0]
})

// 禁止选择的时间
const disabledTime = $computed(() => {
  return props.recommendations.split(' ')[1]
})

// 日期限制函数
function dateDisabled(time) {
  if (props.isDisabled)
    return

  // 设置禁用的截止时间
  const cutoffTime = new Date(disabledDay)
  cutoffTime.setHours(0, 0, 0, 0) // 设置为当天的开始时间
  // 禁用指定时间以及之前的所有时间
  return time.getTime() < cutoffTime.getTime()
}

function makeRange(start: number, end: number) {
  const result: number[] = []
  for (let i = start; i <= end; i++)
    result.push(i)

  return result
}

// 小时限制
function disabledHours(): any {
  // 禁用日期是否与当前日期相同
  const equalDay = String(time.value[0]) == String(disabledDay)
  if (props.isDisabled || !equalDay)
    return

  // 禁用日期等于当前选择日期时开启小时禁用
  return makeRange(0, Number(disabledTime.split(':')[0]) - 1)
}

// 分钟限制
function disabledMinutes(): any {
  if (!disabledTime)
    return

  const hour = time.value[1]?.split(':')[0]
  const disabledHour = disabledTime?.split(':')[0]
  // 禁用日期是否与当前日期相同
  const equalDay = String(time.value[0]) == String(disabledDay)
  // 禁用小时是否等于当前小时
  const equalHour = disabledHour == hour
  // 禁用日期与当前选择日期相同并且禁用小时等于当前选择小时开启分钟禁用
  if (!props.isDisabled && equalDay && equalHour)
    return makeRange(0, Number(disabledTime.split(':')[1]))

  return ''
}
</script>

<template>
  <div class="relative">
    <el-radio-group v-model="radio" :disabled="isDisabled">
      <div v-if="isDisabled" class="mb-10px flex items-center h-25px">
        <el-radio value="0" size="large">
          立即发布
        </el-radio>
      </div>
      <el-radio v-if="unlimited"
                value="0"
                size="large"
      >
        不限时
      </el-radio>
      <div>
        <el-radio
          value="1"
          size="large"
          class="h-25px"
        >
          {{ !isDisabled ? '自定义时间' : "定时发布" }}
        </el-radio>
      </div>
    </el-radio-group>
    <div class="absolute bottom-[-3px] flex items-center" :class="!isDisabled ? 'left-[104px]' : 'left-[90px]'">
      <el-date-picker
        v-model="time[0]"
        class="w-140px"
        type="date"
        :clearable="false"
        placeholder="选择日期"
        :disabled="isDisabled"
        :disabled-date="dateDisabled"
        value-format="YYYY-MM-DD"
      />
      <el-time-picker
        v-model="time[1]"
        class="w-100px ml-10px"
        placeholder="选择时间"
        :clearable="false"
        :disabled="isDisabled"
        :disabled-hours="disabledHours"
        :disabled-minutes="disabledMinutes"
        format="HH:mm"
        value-format="HH:mm"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
  .el-radio-group{
    display:block;
  }
}
</style>
