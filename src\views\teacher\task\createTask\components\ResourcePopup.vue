<script setup lang="ts">
import type { PropType } from 'vue'

const props = defineProps({
  checkedQiMingResource: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  checkedXiaoBenResource: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  fileList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  count: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(['selected'])

const videoType = ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8']
let showDialog = defineModel<boolean>('show')
const tabMenu = [
  {
    label: '启鸣资源',
    key: 'QiMingResource',
  },
  {
    label: '校本资源',
    key: 'XiaoBenResource',
  },
  {
    label: '本地资源',
    key: 'UploadResource',
  },
]
let activeMenu = $ref<any>('QiMingResource')
const resourceData = $computed(() => {
  if (activeMenu == 'QiMingResource')
    return transformData(props.checkedQiMingResource)

  if (activeMenu == 'XiaoBenResource')
    return transformData(props.checkedXiaoBenResource)

  return props.fileList
})
/* 切换Tab */
function handleChangeTab(key) {
  activeMenu = key
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}
/* 取消选中 */
function handleChecked(row) {
  emit('selected', row, activeMenu)
}
function deleteFile(index) {
  props.fileList.splice(index, 1)
}

function transformData(data) {
  // 遍历原始数据，对每个资源列表进行聚合
  return data.map((book) => {
    // 创建一个空数组用于存储聚合后的章节
    const chapters = [] as any

    // 创建一个映射表，用于快速查找章节是否已存在
    const chapterMap = {}

    // 遍历当前书的 resourceList
    for (const resource of book.resourceList) {
      const {
        sysTextbooksCatalogId,
        sysTextbooksCatalogName,
      } = resource

      // 如果当前章节尚未创建，则初始化一个新章节
      if (!chapterMap[sysTextbooksCatalogId]) {
        chapterMap[sysTextbooksCatalogId] = {
          sysTextbooksCatalogId,
          sysTextbooksCatalogName,
          resourceList: [],
        }
        chapters.push(chapterMap[sysTextbooksCatalogId]) // 将新章节加入列表
      }

      // 将当前资源添加到对应的章节中
      chapterMap[sysTextbooksCatalogId].resourceList.push(resource)
    }
    // 返回一个新的 book 对象，包含聚合后的章节
    return {
      bookId: book.bookId,
      bookName: book.bookName,
      chapter: chapters, // 包含聚合后的章节
    }
  })
}
/* 格式化时间 */
function formatTime(seconds) {
  // 只保留分钟
  const minutes = Math.ceil(seconds / 60)
  return `(${Number.isNaN(minutes) ? '0' : minutes}分钟)`
}
function getCount(type) {
  let typeCount = 0
  if (type == 'QiMingResource') {
    props.checkedQiMingResource.forEach((v) => {
      typeCount += v.resourceList.length
    })
  }
  else if (type == 'XiaoBenResource') {
    props.checkedXiaoBenResource.forEach((v) => {
      typeCount += v.resourceList.length
    })
  }
  else {
    typeCount = props.fileList.length
  }
  return typeCount
}
/* 根据文件类型返回对应图片地址 */
function getFileTypeUrl(ext) {
  let type = ext ? ext.toLowerCase() : null
  if (['doc', 'docx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/docx.png')

  if (['xls', 'xlsx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/excel.png')

  if (['pdf',
'ppt',
'pptx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/pdf.png')

  if (['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8'].includes(type))
    return $g.tool.getFileUrl('taskCenter/video.png')

  if (['jpg',
'jpeg',
'png',
'gif',
'bmp',
'svg'].includes(type))
    return $g.tool.getFileUrl('taskCenter/img.png')

  return $g.tool.getFileUrl('taskCenter/default-file.png')
}
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    position="right"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="w-476px h-screen flex flex-col py-17px pl-21px pr-18px overflow-hidden bg-[#F3F4F9]"
    teleport="#app"
    v-bind="$attrs"
    @open="() => {
      $g.tool.renderMathjax()
    }"
  >
    <div class="flex items-center justify-between font-600 text-17px mb-21px">
      <span>已加入({{ count }})</span>
      <img
        class="cursor-pointer w-15px"
        src="@/assets/img/taskCenter/close.png"
        @click="showDialog = false"
      />
    </div>
    <!-- TAB -->
    <div class="flex">
      <div
        v-for="(item, index) in tabMenu"
        :key="item.key"
        class="menu_item flex items-center"
        :class="{
          'mx-10px': index != 0 && index != tabMenu.length - 1,
          'menu-item-active': activeMenu == item.key,
        }"
        @click="handleChangeTab(item.key)"
      >
        <!-- <svg-common-upload
          v-if="item.key == 'UploadResource'"
          class="w-17px h-17px mr-2px"
        /> -->
        <div class="flex items-center">
          <div>{{ item.label }}</div>
          <div class="ml-2px">
            ({{ getCount(item.key) }})
          </div>
        </div>
      </div>
    </div>
    <!-- CONTENT -->
    <div class="mt-15px h-[calc(100vh-34px-47px-36px)] overflow-y-auto">
      <g-empty v-if="!resourceData.length"></g-empty>
      <template v-else>
        <template v-if="activeMenu != 'UploadResource'">
          <div v-for="item in resourceData" :key="item.bookId">
            <!-- 书名 -->
            <div class="flex items-center mb-10px">
              <img
                :src="$g.tool.getFileUrl('taskCenter/book.png')"
                alt=""
                class="w-17px h-17px flex-shrink-0 mr-6px"
              />
              <div class="text-[15px] font-600">
                {{ item.bookName }}
              </div>
            </div>
            <!-- 章节资源 -->
            <div>
              <div
                v-for="chapter in item.chapter"
                :key="chapter.sysTextbooksCatalogId"
                class="my-17px bg-white px-17px pt-17px rounded-[6px]"
              >
                <!-- 章节名称 -->
                <g-mathjax :text="chapter.sysTextbooksCatalogName" class="font-600 text-[15px] truncate" />

                <!-- 资源 -->
                <div
                  v-for="(value, index) in chapter.resourceList"
                  :key="value.resourceId"
                >
                  <div class="flex items-center justify-between flex-1">
                    <div class="flex items-center my-17px">
                      <img
                        v-if="getFileTypeUrl(value.fileExtension)"
                        :src="getFileTypeUrl(value.fileExtension)"
                        alt=""
                        class="w-17px h-17px"
                      />
                      <div class="mx-2px text-[15px] flex items-center">
                        <g-mathjax :text="value.fileName " class="max-w-[220px] truncate flex-1" />
                        <div
                          v-if="
                            [
                              'mp4',
                              'mov',
                              'avi',
                              'rmvb',
                              'flv',
                              'wmv',
                              'mkv',
                              'm3u8',
                            ].includes(value.fileExtension)
                          "
                          class="fle-shrink-0 ml-2px w-[80px]"
                        >
                          {{ formatTime(value.fileDuration) }}
                        </div>
                      </div>
                    </div>
                    <div
                      class=" text-[#FF4646] border-[1px] border-solid border-[#FF4646] px-9px h-30px leading-[30px] rounded-[4px] flex-shrink-0 cursor-pointer"
                      @click="handleChecked(value)"
                    >
                      取消加入
                    </div>
                  </div>
                  <div
                    v-if="index != chapter.resourceList.length - 1"
                    class="w-auto h-1px bg-[#E8E8E8]"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-else class="bg-white px-17px rounded-[6px]">
          <div v-for="(value, index) in resourceData" :key="value.uid">
            <div class="flex justify-between items-center">
              <div class="flex items-center my-17px flex-1">
                <img
                  v-if="getFileTypeUrl(value.fileExtension)"
                  :src="getFileTypeUrl(value.fileExtension)"
                  alt=""
                  class="w-17px h-17px"
                />
                <div class="mx-2px text-[15px] flex items-center">
                  <div class="max-w-[235px] truncate">
                    {{ value.name || value.fileName }}
                  </div>
                  <div class="fle-shrink-0 ml-2px">
                    {{ videoType.includes(value.fileExtension) ? formatTime(value.fileDuration) : '' }}
                  </div>
                </div>
              </div>
              <div
                class="text-[#FF4646] border-[1px] border-solid border-[#FF4646] px-9px h-30px leading-[30px] rounded-[4px] flex-shrink-0 cursor-pointer"
                @click="deleteFile(index)"
              >
                取消加入
              </div>
            </div>
            <div
              v-if="index != resourceData.length - 1"
              class="w-auto h-1px bg-[#E8E8E8]"
            ></div>
          </div>
        </div>
      </template>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.menu_item {
  border: 1px solid #dadde8;
  padding: 0 17px;
  line-height: 34px;
  border-radius: 6px;
  color: #6c6c74;
  background-color: #fbfbfb;
  font-size: 15px;
  cursor: pointer;
}
.menu-item-active {
  border-color: #646ab4;
  color: #6474fd;
  background-color: #ecefff;
}
</style>
