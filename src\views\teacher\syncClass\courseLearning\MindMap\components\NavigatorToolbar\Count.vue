<script setup lang="ts">
const props = defineProps({
  mindMap: {
    type: Object,
  },
})
let textStr = $ref('')
let words = $ref(0)
let num = $ref(0)
let countEl: any = document.createElement('div')

// 监听数据变化
function onDataChange(data) {
  textStr = ''
  words = 0
  num = 0
  walk(data)
  countEl.innerHTML = textStr
  words = countEl.textContent.length
}

// 遍历
function walk(data) {
  num++
  textStr += String(data.data.text) || ''
  if (data.children && data.children.length > 0) {
    data.children.forEach((item) => {
      walk(item)
    })
  }
}

onBeforeMount(() => {
  $g.bus.on('mind_map_data_change', onDataChange)
  if (props.mindMap)
    onDataChange(props.mindMap.getData())
})

onBeforeUnmount(() => {
  $g.bus.off('mind_map_data_change', onDataChange)
})
</script>

<template>
  <div class="countContainer flex h-full px-10px items-center">
    <div class="item mr-10px">
      <span class="name">字数</span>
      <span class="value">{{ words }}</span>
    </div>
    <div class="item">
      <span class="name">节点</span>
      <span class="value">{{ num }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.countContainer {
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.6);
}
</style>
