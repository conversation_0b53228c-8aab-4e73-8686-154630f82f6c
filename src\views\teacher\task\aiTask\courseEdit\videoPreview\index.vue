<script lang="ts" setup>
import { getVideoAnalyse } from '@/api/aiTask'
import { getVideoResource } from '@/api/syncClass'

const route = useRoute()
let videoUrl = $ref('')
let content = $ref<any>()
let showLoading = $ref(true)

async function fetchVideoUrl() {
  try {
    const res = await getVideoResource({ videoResourceId: route.query?.videoResourceId })
    videoUrl = res || ''
  }
  finally {
    showLoading = false
  }
}
async function fetchVideoInfo() {
  try {
    const res = await getVideoAnalyse({ videoResourceId: route.query?.videoResourceId })
    content = res.analyseContent || ''
  }
  finally {
    showLoading = false
  }
}

onMounted(() => {
  fetchVideoInfo()
  fetchVideoUrl()
})
</script>

<template>
  <div class="min-h-screen p-26px" style="width: 100vw;">
    <g-navbar title="视频预览" class="mb-26px">
    </g-navbar>

    <div class="p-13px pt-17px bg-white rounded-[13px]">
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <template v-else>
        <div v-if="videoUrl" class="text-16px font-600 mb-16px">
          视频课
        </div>
        <g-video
          v-if="videoUrl"
          :url="videoUrl"
          class="w-full h-[450px]"
        />

        <div v-if="content" class="w-full min-h-300px">
          <div class="text-16px font-600 mt-22px mb-16px">
            视频大纲
          </div>
          <div class=" bg-[#F3F3FB] rounded-[4px]">
            <g-markdown-new :content="content"></g-markdown-new>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
