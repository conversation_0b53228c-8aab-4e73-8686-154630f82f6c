<script lang="ts" setup>
import { useRouterStore } from '@/stores/modules/router'
import { useUserStore } from '@/stores/modules/user'

const { routerArr } = $(storeToRefs(useRouterStore())) as any
const userStore = useUserStore()
const router = useRouter()
let isZXS = $ref(0)

const zxsList = [
  {
    name: '吴文俊(笔)',
    userName: '3530018328',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '张沁铃(笔)',
    userName: '3530015740',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '易子墨',
    userName: '3530015657',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '刘佳明',
    userName: '3530016202',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '周凌毅',
    userName: '3520013412',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '徐永杰',
    userName: '3530021419',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '常雨雯',
    userName: '3530021411',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '泸州天立随机账号',
    userName: '',
    password: '123456a!=&',
    schoolId: '1442',
    color: '',
  },
]

const jztList = [
  {
    name: '陈俊宏',
    userName: 'T01492154',
    password: '666666',
    color: '',
  },
  {
    name: '戢旭秋',
    userName: 'T01492155',
    password: '666666',
    color: '',
  },
  {
    name: '唐艺菡',
    userName: 'T01492156',
    password: '666666',
    color: '',
  },
  {
    name: '曾浩',
    userName: 'T01492157',
    password: '666666',
    color: '',
  },
  {
    name: '刘学根',
    userName: 'T01492158',
    password: '666666',
    color: '',
  },
  {
    name: '汪文杨',
    userName: 'T01492159',
    password: '666666',
    color: '',
  },
  {
    name: '肖茂',
    userName: 'T01492160',
    password: '666666',
    color: '',
  },
  {
    name: '朱思霖',
    userName: 'T01492161',
    password: '666666',
    color: '',
  },
  {
    name: '周荣',
    userName: 'T01492162',
    password: '666666',
    color: '',
  },
  {
    name: '苏航',
    userName: 'T01492163',
    password: '666666',
    color: '',
  },
  {
    name: '黄玲',
    userName: '*********',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '章红',
    userName: '*********',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '戴芮绅',
    userName: '*********',
    password: '666666',
    color: '',
  },
  {
    name: '谭潘',
    userName: '*********',
    password: '123456a!=&',
    color: '',
  },
  {
    name: '*********',
    userName: '*********',
    password: '123456a!=&',
    color: '',
  },
]

let studentList: any = $ref([])

function handleNodeClick(node) {
  router.push({
    name: node.name,
    query: node.meta?.debugQuery,
  })
}

const formInline = $ref({
  account: '*********',
  password: '123456a!=&',
})

function onSubmit() {
  userStore.getTokenTest(formInline)
}

function logInfo() {
  $g.showToast('打印成功，请前往控制台查看')
  console.log({
    ...userStore.userInfo,
    token: userStore.token,
  })
}

function generateRandomNumber(min, max) {
  const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min // 生成指定范围内的随机整数
  return randomNumber.toString().padStart(3, '0') // 使用padStart函数填充0，确保数字有3位
}
function changeStudent(item) {
  let idNum = item.userName
  if (item.schoolId) {
    switch (item.schoolId) {
      case '1442':
        idNum = `3530018${generateRandomNumber(300, 400)}`
        break
    }
  }
  formInline.account = idNum
  formInline.password = item.password || '123456a!=&'
  onSubmit()
}

function getStudentList(): any {
  const colors = ['primary',
'success',
'info',
'warning',
'danger']
  studentList.forEach((e, i) => {
    e.color = colors[i % colors.length]
  })
  return studentList
}

function loginChange() {
  userStore.resetAll()
  if (isZXS) {
    userStore.platform = 'STUDENT_PC'
    studentList = zxsList
  }
  else {
    userStore.platform = 'JZT_USER_APP'
    studentList = jztList
  }
  formInline.account = studentList[0].userName
  formInline.password = studentList[0].password
  onSubmit()
}

onMounted(() => {
  studentList = jztList
  // 如果没有登录学生，随机登录一个泸州天立账号
  // if (!userStore.userInfo?.userName && isZXS) {
  //   changeStudent({ schoolId: '1442' })
  // }
})
</script>

<template>
  <div class="bg-[white] h-[100vh] flex flex-col items-center relative">
    <div class="bg-[#f2f3f5] w-full h-160px">
      <div class="text-20px font-600 my-10px relative">
        <!-- <van-tabs
          v-model:active="isZXS"
          type="card"
          class="w-300px m-auto mb-10px"
          :color="isZXS ? '#3398f7' : '#fe8231'"
          @change="loginChange"
        >
          <van-tab title="金字塔" />
          <van-tab title="智习室" />
        </van-tabs>
        <div class="text-center">
          横屏3端项目-调试页面
        </div> -->
        <div
          class="text-theme-primary text-12px absolute right-10px top-10px"
          @click="logInfo"
        >
          <div class="text-10px text-theme-error mb-4px">
            点击此处打印完整信息
          </div>
          <div>账号-{{ userStore.userInfo?.accountName || '无' }}</div>
          <div>登陆老师-{{ userStore.userInfo?.userName || '无' }}</div>
          <div>学校-{{ userStore.userInfo?.schoolName || '无' }}</div>
          <div class="flex justify-between">
            <span>
              年级-{{
                $g.JSON.GRADE[userStore.userInfo?.sysGradeId] || '无'
              }}
            </span><span>班级-{{ userStore.userInfo?.className || '无' }}</span>
          </div>
        </div>
      </div>
      <div class="flex gap-2 mb-10px flex-cc w-500px mx-auto flex-wrap">
        <el-tag
          v-for="(item, i) in getStudentList()"
          :key="i"
          :type="item.color"
          size="small"
          @click="changeStudent(item)"
        >
          {{ item.name }}
        </el-tag>
      </div>
      <el-form
        :inline="true"
        :model="formInline"
        class="demo-form-inline flex-cc"
      >
        <el-form-item label="账号">
          <el-input
            v-model="formInline.account"
            class="w-[200px]"
            placeholder="请输入学生账号"
            clearable
          />
        </el-form-item>
        <el-form-item label="密码">
          <el-input
            v-model="formInline.password"
            class="w-[200px]"
            placeholder="请输入学生账号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="onSubmit"
          >
            登陆
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-divider style="margin-top: 0px" />
    <div class="flex">
      <el-tree
        class="!bg-[#cafff5] h-[calc(100vh_-_200px)]"
        :expand-on-click-node="false"
        node-key="name"
        style="min-width: 70vw; overflow: auto"
        :data="routerArr"
        :default-expanded-keys="['Root', 'Teacher', 'TaskCenter']"
        default-expand-all
        @node-click="handleNodeClick"
      >
        <template #default="{ data }">
          <span class="custom-tree-node">
            <span>{{ data.meta.title }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>
