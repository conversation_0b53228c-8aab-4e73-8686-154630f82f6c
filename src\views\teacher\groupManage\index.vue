<script setup lang="ts">
import {
  addStudentToGroup,
  createNewGroup,
  delGroupById,
  delMembersFromGroup,
  editGroupName,
  getClassList,
  getGradeClassList,
  getGroupList,
  getStudentList,
  getSubjectList,
} from '@/api/groupManage'
import { getInfo } from '@/api/taskCenter'
import Namedavatar from 'namedavatar'
import Draggable from 'vuedraggable'

let userInfo: any = $ref({})

const avatarUrl = $computed(() => {
  const svg = Namedavatar.getSVGString(userInfo?.userName, {
    nameType: 'lastName',
  })
  const url = Namedavatar.getDataURI(svg)
  return url
})

let boxItems: any = []
const leftBox = $ref<any>()

let classType = $ref(1)
let classList = $ref<any>([])

let schoolClassId = $ref<any>(null)
let gradeList = $ref<any>([])

let currentSubject = $ref<any>(null)
let subjectList = $ref<any>([])

let studentName = $ref('')

let studentList = $ref<any>([])
let leftSelected = $ref<any>([])

let groupList = $ref<any>([])
let groupLoading = $ref(true)

let tempName = $ref('')

let showMenu = $ref(false)

let moveEls: any = null // 移动中的元素，格式：[[], [], []]
let dragBoxIndex = -1
let inSelected = false

const rightSelected = $computed(() => {
  return groupList.reduce((prev, next) => {
    next.selected.forEach((v) => {
      if (!prev.includes(v))
        prev.push(v)
    })
    return prev
  }, [])
})

// 获取老师信息
async function getUserInfo() {
  const info = await getInfo()
  userInfo = info
}

function onItemSelect(list, id, isLeft) {
  if ((isLeft && getRightSelectedLength() > 0) || (!isLeft && leftSelected.length > 0))
    return

  const idx = list.findIndex(v => v === id)
  if (idx !== -1)
    list.splice(idx, 1)

  else
    list.push(id)
}

function dragStart(evt, index) {
  dragBoxIndex = index
  inSelected = evt.item.classList.contains('item-selected')
  if (index === -1) { // 从左边的盒子拖动元素
    if (leftSelected.length > 1 && inSelected) {
      moveEls = [studentList.filter(v => leftSelected.includes(v.id))]
      getMultipleClass()
    }
    else {
      // leftSelected = []
      moveEls = [[studentList[evt.oldIndex]]]
    }
  }
  else { // 从右边的盒子拖动元素
    if (getRightSelectedLength() > 1 && inSelected) {
      moveEls = groupList.filter(v => v.selected.length).map((v) => {
        return v.list.filter(vv => v.selected.includes(vv.id))
      })
      getMultipleClass()
    }
    else {
      // groupList.forEach((v) => {
      //   v.selected = []
      // })
      moveEls = [[groupList[index].list[evt.oldIndex]]]
    }
  }
}

function getMultipleClass() {
  const draggingBox: any = document.querySelector('.dragging-box')
  draggingBox?.classList.add('multi-drag')
}

function getRightSelectedLength() {
  return groupList.reduce((prev, next) => {
    return prev + next.selected.length
  }, 0)
}

async function dragEnd(e) {
  if (!moveEls?.length)
    return

  const clientX = e.originalEvent.clientX || e.originalEvent.changedTouches[0].clientX
  const clientY = e.originalEvent.clientY || e.originalEvent.changedTouches[0].clientY
  const idx = getPutBoxIndex(clientX, clientY)
  if (idx === dragBoxIndex) { // 同个盒子中拖动不作操作
    return
  }

  if (idx !== -1) { // 放置位置在右边的盒子中
    let schoolStudentIdList: any = []
    const list = groupList[idx].list
    moveEls.forEach((elList) => {
      elList.forEach((v) => {
        if (list.findIndex(vv => vv.id === v.id) === -1) {
          const item = $g._.cloneDeep(v)
          list.push(item)
          schoolStudentIdList.push(item)
        }
      })
    })
    if (schoolStudentIdList.length) {
      const schoolClassStudentIdList = schoolStudentIdList.map(v => v.schoolClassStudentId)
      const data = await addStudentToGroup({
        schoolClassId: groupList[idx].id,
        schoolStudentIdList: schoolStudentIdList.map(v => v.id),
      })
      data.forEach((v) => {
        const item = schoolStudentIdList.find(vv => vv.id === v.schoolStudentId)
        if (item)
          item.schoolClassStudentId = v.schoolClassStudentId
      })
      $g.showNotify({
        type: 'success',
        message: '设置成功',
        className: 'group-message-pop',
        background: '#EAF7EE',
      })
      if (dragBoxIndex !== -1) {
        groupList.forEach((v) => {
          v.list = v.list.filter(vv => !schoolClassStudentIdList.includes(vv.schoolClassStudentId))
        })
        await delMembersFromGroup({
          schoolClassStudentIdList,
        })
      }
      clearSelect()
    }
  }
  else if (isInLeftBox(clientX, clientY)) { // 放置位置在左边盒子
    await delStudentFromGroup()
  }
  getStudentListApi()
  moveEls = null
}

async function delStudentFromGroup(delByBtn = false) {
  await $g.showConfirmDialog({
    title: '确定要从组内删除这些同学吗?',
    message: '将已选中的学生从各自组中删除并保存',
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    confirmButtonColor: '#FF4646',
    zIndex: 100,
  })
  let schoolClassStudentIdList: any = []
  if (inSelected || delByBtn) {
    groupList.forEach((v) => {
      v.list = v.list.filter((vv) => {
        if (v.selected.includes(vv.id)) { // 在这里获取即将删除的学生id
          schoolClassStudentIdList.push(vv.schoolClassStudentId)
        }
        else {
          return true
        }
      })
    })
  }
  else {
    groupList[dragBoxIndex].list = groupList[dragBoxIndex].list.filter(v => v.id !== moveEls[0][0].id)
    schoolClassStudentIdList.push(moveEls[0][0].schoolClassStudentId)
  }
  await delMembersFromGroup({
    schoolClassStudentIdList,
  })
  getStudentListApi()
  clearSelect()
  $g.showNotify({
    type: 'success',
    message: '设置成功',
    className: 'group-message-pop',
    background: '#EAF7EE',
  })
}

function isInLeftBox(mouseX, mouseY) {
  if (!leftBox)
    return

  const rect = leftBox.getBoundingClientRect()
  return (
    mouseX >= rect.left &&
    mouseX <= rect.right &&
    mouseY >= rect.top &&
    mouseY <= rect.bottom
  )
}

function clearSelect() {
  leftSelected = []
  groupList.forEach((v) => {
    v.selected = []
  })
}

function getPutBoxIndex(mouseX, mouseY) {
  if (!boxItems)
    return -1

  return boxItems.findIndex((v) => {
    if (!v) return false
    const rect = v.getBoundingClientRect()
    return (
      mouseX >= rect.left &&
      mouseX <= rect.right &&
      mouseY >= rect.top &&
      mouseY <= rect.bottom
    )
  })
}

function addGroup() {
  if (!schoolClassId) {
    $g.showToast('请选择一个班级')
    return
  }
  if (!currentSubject) {
    $g.showToast('请选择一个学科')
    return
  }
  tempName = ''
  showMenu = true
}

async function onAddConfirm() {
  const name = tempName.trim()
  if (!name) {
    $g.showToast('小组名称不能为空')
    return
  }
  const id = await createNewGroup({
    className: name,
    sysSubjectId: currentSubject,
    belongsSchoolClassId: schoolClassId,
  })
  $g.showToast('新增小组成功')
  groupList.unshift({
    id,
    name,
    edit: false,
    list: [],
    selected: [],
  })
  showMenu = false
}

function onEditGroup(item) {
  tempName = item.name
  item.edit = true
}

async function confirmEdit(item) {
  if (!tempName) {
    $g.showToast('小组名称不能为空')
    return
  }
  await editGroupName({
    schoolClassId: item.id,
    className: tempName,
  })
  $g.showToast('修改小组名字成功')
  item.name = tempName
  item.edit = false
}

function delGroup(id, index) {
  $g.showConfirmDialog({
    title: '确定删除该小组吗?',
    message: '删除小组后，该小组的成员将从组内释放',
    cancelButtonText: '取消',
    confirmButtonText: '删除',
    confirmButtonColor: '#FF4646',
    zIndex: 100,
  }).then(async () => {
    await delGroupById({
      schoolClassId: id,
    })
    $g.showToast('删除小组成功')
    groupList.splice(index, 1)
    getStudentListApi()
  }).catch((err) => {
      console.log(err)
    })
}

function setItemRef(el, index) {
  if (el)
    boxItems[index] = el

  else
    boxItems[index] = null
}

async function getClassListApi() {
  const data = await getClassList()
  classList = data
  classType = data[0]?.classType || 1
}

async function getGradeClassListApi() {
  const data = await getGradeClassList({
    classType,
  })
  if (data.length) {
    schoolClassId = data[0].schoolClassId
    gradeList = data.map(v => ({
      label: v.sysGradeName + v.className,
      value: v.schoolClassId,
    }))
    getLeftData()
  }
  else {
    schoolClassId = null
    gradeList = []
    currentSubject = null
    subjectList = null
    studentName = ''
    studentList = []
    leftSelected = []
    groupList = []
    groupLoading = false
  }
}

async function getGroupListApi() {
  try {
    groupLoading = true
    const data = await getGroupList({
      sysSubjectId: currentSubject,
      classType,
    })
    groupList = data.map(v => ({
      id: v.schoolClassId,
      name: v.className,
      edit: false,
      list: v.list.map(vv => ({
        id: vv.schoolStudentId,
        name: vv.userName,
        schoolClassStudentId: vv.schoolClassStudentId,
      })),
      selected: [],
    }))
    clearSelect()
    groupLoading = false
  }
  catch (e) {
    groupLoading = false
    console.error(e)
  }
}

async function getLeftData() {
  getStudentListApi()
  getSubjectListApi()
}

async function getSubjectListApi() {
  if (!schoolClassId)
    return

  const data = await getSubjectList({
    schoolClassId,
  })
  currentSubject = data[0].sysSubjectId
  subjectList = data.map(v => ({
    label: v.sysSubjectName,
    value: v.sysSubjectId,
  }))
  getRightData()
}

function getRightData() {
  getGroupListApi()
  getStudentListApi()
}

async function getStudentListApi() {
  if (!schoolClassId)
    return

  const data = await getStudentList({
    schoolClassId,
    keyword: studentName,
    sysSubjectId: currentSubject,
  })
  studentList = data.map(v => ({
    name: v.userName,
    id: v.schoolStudentId,
    schoolClassStudentId: v.schoolClassStudentId,
    isJoinGroup: v.isJoinGroup,
  }))
}

// 刷新
function manuallyRefresh() {
  location.reload()
}
const router = useRouter()
function back() {
  if ($g.isPC)
    router.back()

  else
    $g.flutter('back', true)
}
onBeforeMount(async () => {
  getUserInfo()
  await getClassListApi()
  getGradeClassListApi()
})
</script>

<template>
  <div class="h-screen p-26px select-none" style="width: 100vw">
    <g-navbar class="mb-18px" @back="back">
      <template #title>
        群组管理
        <span class="text-[#666] font-400 text-16px">(支持跨班建组)</span>
      </template>
      <template #right>
        <img
          :src="$g.tool.getFileUrl('taskCenter/refresh.png')"
          alt="refresh"
          class="w-15px h-15px cursor-pointer mr-26px"
          @click="manuallyRefresh"
        />
        <div class="br-[50%] w-27px h-27px overflow-hidden mr-9px">
          <img :src="avatarUrl"
               alt="refresh"
               class="w-full h-full"
          />
        </div>
        <div class="text-[#929296] text-12px flex items-center">
          <span>{{ `${userInfo?.schoolName || ''}·` }}</span>
          <span class="max-w-[60px] line-clamp-1">{{ `${userInfo?.userName || ''}` }}</span>
        </div>
      </template>
    </g-navbar>
    <div class="flex items-center h-22px leading-[22px] mb-17px">
      <div class="text-16px font-600 mr-34px">
        自定义小组
      </div>
      <el-radio-group
        v-if="classList.length > 1"
        v-model="classType"
        class="group-radios"
        @change="getGradeClassListApi"
      >
        <el-radio v-for="item in classList"
                  :key="item.classType"
                  :value="item.classType"
        >
          {{ item.classTypeName }}
        </el-radio>
      </el-radio-group>
    </div>
    <div class="box-h w-full flex">
      <div class="w-291px bg-white border border-solid border-[#DADDE8] rounded-[6px] p-17px mr-15px">
        <div class="flex items-center mb-13px h-32px">
          <el-select
            v-model="schoolClassId"
            placeholder="选择班级"
            class="w-110px mr-10px group-select"
            @change="getLeftData"
          >
            <el-option
              v-for="item in gradeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-model="currentSubject"
            placeholder="选择科目"
            class="w-75px mr-5px group-select"
            @change="getRightData"
          >
            <el-option
              v-for="item in subjectList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="flex-grow flex-shrink-0 text-right">
            共{{ groupList.length }}组
          </div>
        </div>
        <el-input
          v-model="studentName"
          placeholder="搜索学生姓名"
          class="mb-21px"
          clearable
          @keydown.enter="getStudentListApi"
          @blur="getStudentListApi"
          @clear="getStudentListApi"
        >
          <template #prefix>
            <img src="@/assets/img/taskCenter/search.png"
                 alt="search"
                 class="w-17px h-17px"
            >
          </template>
        </el-input>
        <div ref="leftBox" class="left-h overflow-y-auto no-bar">
          <Draggable
            v-model="studentList"
            :group="{
              name: 'student',
              pull: 'clone',
              put: false,
            }"
            :sort="false"
            drag-class="dragging-box"
            class="flex flex-wrap items-start"
            handle=".handle"
            item-key="id"
            @start="dragStart($event, -1)"
            @end="dragEnd"
          >
            <template #item="{ element }">
              <div
                :data-id="element.id"
                class="relative px-10px h-40px leading-[40px] border border-[#E8E8E8] rounded-[5px] mr-13px mb-11px flex items-center"
                :class="{
                  '!border-[#646AB4] item-multiple': element.isJoinGroup === 2 && !leftSelected.includes(element.id),
                  '!border-[#646AB4] item-selected': leftSelected.includes(element.id),
                }"
                @click="onItemSelect(leftSelected, element.id, true)"
              >
                <div class="text-15px mr-6px text-center min-w-40px max-w-80px truncate">
                  {{ element.name }}
                </div>
                <div class="handle cursor-move" @click.stop>
                  <img src="@/assets/img/taskCenter/dot.png"
                       alt="dot"
                       class="w-9px h-15px"
                  >
                </div>
              </div>
            </template>
          </Draggable>
        </div>
      </div>
      <div class="flex-1">
        <div
          class="w-full h-53px flex-cc bg-white rounded-[6px] border border-solid border-[#DADDE8] van-haptics-feedback mb-15px"
          @click="addGroup"
        >
          <img src="@/assets/img/taskCenter/add.png"
               alt="add"
               class="w-11px h-11px"
          >
          <div class="text-15px ml-4px pt-1px text-[#6C6C74]">
            新增小组
          </div>
        </div>
        <div class="right-h overflow-y-auto no-bar">
          <g-loading v-if="groupLoading" class="h-200px"></g-loading>
          <template v-else>
            <div v-if="groupList.length === 0" class="flex justify-center pt-66px">
              <img src="@/assets/img/taskCenter/none.png"
                   alt="none"
                   class="w-128px h-107px"
              >
            </div>
            <div
              v-for="(item, index) in groupList"
              :key="item.id"
              class="border border-solid border-[#DADDE8] bg-white rounded-[6px] mb-13px p-2px"
            >
              <div class="h-43px w-full flex rounded-[4px_4px_0_0] items-center px-17px justify-between bg-[#F3F3FB]">
                <div class="flex items-center">
                  <template v-if="item.edit">
                    <el-input
                      v-model="tempName"
                      placeholder="请输入小组名称"
                      size="small"
                      :maxlength="20"
                      show-word-limit
                      @keydown.enter="confirmEdit(item)"
                    ></el-input>
                    <svg-ri-check-line
                      class="text-18px mx-10px text-[#929296]"
                      @click="confirmEdit(item)"
                    ></svg-ri-check-line>
                    <svg-ri-close-line class="text-18px text-[#929296]" @click="item.edit = false"></svg-ri-close-line>
                  </template>
                  <div v-else
                       class="flex items-center van-haptics-feedback"
                       @click="onEditGroup(item)"
                  >
                    <div class="text-15px text-[#333] mr-7px max-w-[300px] truncate">
                      {{ item.name }}
                    </div>
                    <img src="@/assets/img/taskCenter/edit.png"
                         alt="edit"
                         class="w-13px h-13px"
                    >
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="mr-5px text-14px text-[#929296]">
                    {{ item.list.length }}人
                  </div>
                  <div class="w-1px h-7px mx-13px bg-[#929296]"></div>
                  <img
                    src="@/assets/img/taskCenter/bin.png"
                    alt="bin"
                    class="w-15px h-15px van-haptics-feedback -translate-y-1px"
                    @click="delGroup(item.id, index)"
                  >
                </div>
              </div>
              <div :ref="el => setItemRef(el, index)" class="p-11px min-h-125px relative">
                <Draggable
                  v-model="item.list"
                  :group="{
                    name: 'student',
                    pull: 'clone',
                    put: false,
                  }"
                  :sort="false"
                  drag-class="dragging-box"
                  class="relative z-[2] min-h-70px flex flex-wrap items-start"
                  item-key="id"
                  handle=".handle"
                  @start="dragStart($event, index)"
                  @end="dragEnd"
                >
                  <template #item="{ element }">
                    <div
                      class="relative px-10px h-40px leading-[40px] border border-[#E8E8E8] rounded-[5px] mr-13px mb-11px flex items-center"
                      :class="{
                        '!border-[#646AB4] item-selected': item.selected.includes(element.id),
                      }"
                      @click="onItemSelect(item.selected, element.id, false)"
                    >
                      <div class="text-15px mr-6px text-center min-w-40px max-w-80px truncate">
                        {{ element.name }}
                      </div>
                      <div class="handle cursor-move" @click.stop>
                        <img src="@/assets/img/taskCenter/dot.png"
                             alt="dot"
                             class="w-9px h-15px"
                        >
                      </div>
                    </div>
                  </template>
                </Draggable>
                <div
                  v-if="item.list.length === 0"
                  class="absolute z-[1] w-full h-70px flex-cc text-[#bdbdbd] left-0 top-15px"
                >
                  可拖动学生名字加入该小组
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div
      class="fixed w-full left-0 bottom-0 flex justify-end items-center text-[#666] font-600 bg-white h-58px px-13px panel-btm"
    >
      <div class="text-15px">
        <template v-if="leftSelected.length">
          共{{ studentList.length
          }}人，
        </template>已选择<span class="mx-4px text-[#6474FD]">
          {{
            leftSelected.length || rightSelected.length || 0 }}
        </span>人
      </div>
      <el-button class="ml-13px" @click="clearSelect">
        取消
      </el-button>
      <el-button
        v-if="rightSelected.length"
        type="danger"
        plain
        class="!bg-[transparent] !border-[#FF4646] active:!text-[#ff4646] hover:!text-[#ff4646] van-haptics-feedback"
        @click="delStudentFromGroup(true)"
      >
        <img src="@/assets/img/taskCenter/bin-red.png"
             alt="bin"
             class="w-17px h-17px -translate-y-1px mr-4px"
        >
        删除组员
      </el-button>
    </div>
    <van-popup v-model:show="showMenu" class="p-21px rounded-[6px] w-400px h-200px">
      <div class="w-full flex items-center justify-between">
        <div class="text-17px text-[#333] font-600">
          小组名称
        </div>
        <img
          src="@/assets/img/taskCenter/close.png"
          alt="close"
          class="w-17px h-17px select-none van-haptics-feedback"
          @click="showMenu = false"
        >
      </div>
      <el-input
        v-model="tempName"
        placeholder="请输入小组名称"
        :maxlength="20"
        show-word-limit
        size="large"
        class="my-30px"
      ></el-input>
      <div class="w-full flex justify-end">
        <el-button @click="showMenu = false">
          取消
        </el-button>
        <el-button type="primary" @click="onAddConfirm">
          确定
        </el-button>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
.box-h {
  height: calc(100vh - 26px - 34px - 18px - 22px - 17px - 50px);
}

.left-h {
  height: calc(100% - 32px - 13px - 32px - 21px);
}

.right-h {
  height: calc(100% - 50px - 15px);
}

.multi-drag {
  color: transparent;
  box-shadow: 5px -5px 0 0 #6474FD;
  padding: 0;

  img[alt='dot'] {
    display: none;
  }

  &::after {
    display: none !important;
  }

  &::before {
    display: inline-block;
    content: '多个学生';
    position: absolute;
    white-space: nowrap;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    color: #333;
    z-index: 2;
    border-radius: 5px;
    padding: 0 10px;
    text-align: center;
  }
}

.group-radios {
  :deep() {
    .el-radio {
      margin-right: 36px;
    }

    .el-radio__input {
      .el-radio__inner {
        background: url(@/assets/img/taskCenter/empty.png) center / contain no-repeat;
        border-radius: initial;
        border: none;
      }

      &.is-checked {
        .el-radio__inner {
          background: url(@/assets/img/taskCenter/check.png) center / contain no-repeat;

          &::after {
            display: none;
          }
        }
      }
    }

    .el-radio__label {
      font-size: 15px;
    }
  }
}

.panel-btm {
  box-shadow: 0px -2px 7px 0px rgba(0, 0, 0, 0.04);
}

.item-selected {
  position: relative;

  &::after {
    display: inline-block;
    content: '';
    width: 11px;
    height: 11px;
    position: absolute;
    right: -1px;
    top: -1px;
    background: url(@/assets/img/taskCenter/tag.png) center / contain no-repeat;
  }
}

.item-multiple {
  position: relative;

  &::after {
    display: inline-block;
    content: '';
    width: 11px;
    height: 11px;
    position: absolute;
    right: -1px;
    top: -1px;
    background: url(@/assets/img/taskCenter/multiple.png) center / contain no-repeat;
  }
}

.group-select {
  :deep() {
    .el-select__wrapper {
      background: #FBFBFB;
    }
  }
}
</style>

<style lang="scss">
.group-message-pop {
  width: 128px;
  height: 53px;
  border: 1px solid #65CD64;
  border-radius: 6px;
  font-size: 16px;
  color: #333333;
  left: 0;
  right: 0;
  margin: 17px auto;

  &::before {
    display: inline-block;
    content: '';
    width: 17px;
    height: 17px;
    margin-right: 13px;
    background: url(@/assets/img/taskCenter/success.png) center / contain no-repeat;
  }
}
</style>
