<script setup lang="ts">
import BScroll from '@better-scroll/core'
import CorrectionCnp from './CorrectionCnp/index.vue'

const props = defineProps({
  currentImgObject: {
    type: Object,
    default: () => {},
    required: true,
  },
  imgList: {
    type: Array<any>,
    default: () => [],
    required: true,
  },
  showImageList: {
    type: Array<any>,
    default: () => [],
    required: true,
  },
})
const emit = defineEmits(['editImges', 'submitOfImg'])
const showDialog = defineModel<boolean>()
let boxRef: any = $ref(null)
let bs: any = $ref(null)
let lastDisabled = $ref<any>(false)
let nextDisabled = $ref<any>(false)
async function startRoll() {
  let left = 0
  const dom: any = document.getElementById('box')
  const sub: any = dom?.querySelector('.item-active')
  if (dom && sub)
    left = sub.offsetLeft - dom?.clientWidth / 2 + sub?.clientWidth / 2

  if (left < 0)
    left = 0

  if (left > 117 * props.imgList.length - dom?.clientWidth)
    left = 117 * props.imgList.length - dom?.clientWidth

  if (bs == null && boxRef) {
    bs = new BScroll(boxRef, {
      scrollX: true,
      click: true,
      startX: left * -1,
    })
  }
  else {
    bs?.refresh()
    bs?.scrollTo(left * -1, 0, 500)
  }
}
watch(
  () => showDialog.value,
  async (val) => {
    if (val) {
      currentImgIndex.value = props.imgList.findIndex(
        item => item.id == props.currentImgObject.id,
      )
      if (props.imgList?.length == 1) {
        lastDisabled = true
        nextDisabled = true
      }
      currentObject = props.currentImgObject.showImageList[currentImgIndex.value]
    }
    else {
      // if (props.showImageList?.length == 1) {
      //   try {
      //     await saveCanvasData()
      //   }
      //   catch (err: any) {
      //     err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
      //   }
      // }
    }
  },
)
let currentObject = $ref<any>(null)
const CorrectionCnpRef =
  useTemplateRef<typeof CorrectionCnp>('CorrectionCnpRef')
const currentImgIndex = ref(0)
/** 保存画板json数据 */
function saveCanvasJson() {
  let noteData = CorrectionCnpRef.value?.exportJsonString()
  if (noteData) {
    let current = props.showImageList.find(item => currentObject?.id == item.id)
    current.noteData = noteData
  }
}
async function onIndexChange(flag) {
  try {
    await saveCanvasData()
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
  if (flag == 'last') {
    currentImgIndex.value--
    if (currentImgIndex.value < 0)
      currentImgIndex.value = 0
  }
  else {
    currentImgIndex.value++
    if (currentImgIndex.value > props.imgList?.length - 1)
      currentImgIndex.value = props.imgList?.length - 1
  }
  currentObject = props.showImageList[currentImgIndex.value]
}
// 一键清除
function onClear() {
  emit('editImges', 'clear', currentImgIndex)
  currentObject.commentImage = currentObject.sourceImage// 将图片替换成最原始的图片
}
watch(() => currentImgIndex.value, async () => {
  await nextTick()
  if (props.imgList?.length > 6)
    startRoll()

  if (currentImgIndex.value == 0)
    lastDisabled = true

  if (currentImgIndex.value < props.imgList?.length - 1)
    nextDisabled = false

  if (currentImgIndex.value == props.imgList?.length - 1)
    nextDisabled = true

  if (currentImgIndex.value > 0)
    lastDisabled = false
}, { immediate: true })
/** 保存画板图片 */
async function saveCanvasImage() {
  let currentIndex = currentImgIndex.value
  let imageArr = props.imgList
  let exportSrc = await CorrectionCnpRef.value?.exportImage()
  if (exportSrc) {
    // 更改略缩图显示
    imageArr[currentIndex].thumbnailUrl = exportSrc
    imageArr[currentIndex].isAnnotation = 2
    currentObject.thumbnailUrl = exportSrc
    currentObject.isAnnotation = 2
    let noteParam = props.showImageList[currentImgIndex.value].noteData
    emit('editImges', imageArr, currentImgIndex, noteParam)
  }
}
/** 保存画板变更 */
async function saveCanvasData() {
  let isModified = CorrectionCnpRef.value?.isModified()
  if (!isModified)
    return

  await saveCanvasJson()
  await saveCanvasImage()
}
// 切换图片
async function handleChangeImageIndex(index) {
  try {
    if (index === currentImgIndex.value)
      return

    await saveCanvasData()
    currentImgIndex.value = index
    currentObject = props.showImageList[currentImgIndex.value]
  }
  catch (err: any) {
    err.message === '图片导出中' && $g.msg('数据保存中，请稍后...')
  }
}
// 提交图片标注
async function submitAnnotation() {
  try {
    await saveCanvasData()
    // 检查是否有任何 Fabric 操作;组件上绑定v-if这样每次打开就重新渲染组件，modifiedFlag就会初始化为false
    const hasModifications = CorrectionCnpRef.value?.isModified()
    if (hasModifications)
      emit('submitOfImg')

    showDialog.value = false
  }
  catch (err: any) {
    if (err.message === '图片导出中')
      $g.msg('数据保存中，请稍后...')
  }
}
</script>

<template>
  <div>
    <van-popup
      v-model:show="showDialog"
      round
      duration="0.1"
      :close-on-click-overlay="false"
      class="p-16px h-[80vh] w-[800px] flex flex-col"
    >
      <div class="w-full flex items-center mb-[17px] justify-between">
        <span class="text-17px flex-shrink-0 text-[#333] font-600">
          批改图片(切换保存图片)
        </span>
        <div class="flex w-full justify-center">
          <div
            :class="{
              'text-[rgb(199,199,201,1)]': lastDisabled,
              'cursor-pointer': !lastDisabled,
            }"
            class="flex items-center mr-[30px]"
            @click="onIndexChange('last')"
          >
            <img
              class="w-[13px] h-[13px] mr-[10px]"
              :src="
                lastDisabled
                  ? $g.tool.getFileUrl('correctionPage/last_disabled.png')
                  : $g.tool.getFileUrl('correctionPage/last.png')
              "
            />上一张
          </div>
          <div
            class="flex items-center"
            :class="{
              'text-[rgb(199,199,201,1)]': nextDisabled,
              'cursor-pointer': !nextDisabled,
            }"
            @click="onIndexChange('next')"
          >
            下一张<img
              class="w-[13px] h-[13px] ml-[10px]"
              :src="
                nextDisabled
                  ? $g.tool.getFileUrl('correctionPage/next_disabled.png')
                  : $g.tool.getFileUrl('correctionPage/next.png')
              "
            />
          </div>
        </div>
        <img
          src="@/assets/img/taskCenter/close.png"
          class="w-17px h-17px select-none van-haptics-feedback"
          @click="showDialog = false"
        />
      </div>

      <div class="flex-1 overflow-hidden flex flex-col">
        <!-- 正在批改的图片 -->
        <CorrectionCnp
          v-if="showDialog"
          ref="CorrectionCnpRef"
          :img-src="currentObject?.commentImage || ''"
          :note-data="currentObject?.noteData"
          @clear="onClear"
        />
        <!-- 缩略图列表 -->
        <div
          v-if="imgList.length > 1"
          id="box"
          ref="boxRef"
          class="mt-16px flex w-full items-center overflow-hidden relative"
        >
          <div class="flex w-fit">
            <div
              v-for="(item, index) in imgList"
              :key="index"
              :class="{
                '!border-[#6474FDFF] item-active': currentImgIndex === index,
              }"
              class="relative w-[107px] flex-shrink-0 border-[2px] br-[6px] border-[#E8E8E8FF] mr-10px cursor-pointer"
              @click="handleChangeImageIndex(index)"
            >
              <img
                class="object-cover w-[107px] h-[107px] br-[6px] flex-shrink-0"
                :src="item.thumbnailUrl"
              />
              <img
                v-if="item?.isAnnotation == 2"
                src="@/assets/img/taskCenter/correct.png"
                class="w-[38px] absolute top-0 left-0 h-[18px]"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-10px">
        <el-button type="primary" @click="submitAnnotation">
          提交
        </el-button>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped></style>
