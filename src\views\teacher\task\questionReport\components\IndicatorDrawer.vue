<script setup lang="ts">
const instructionInfo = $ref<any>([
  {
    title: '任务概况',
    expand: true,
    subTitle:
      '以班级整体展示数据，展示在当前数据范围下的班级任务概况。',
    contentList: [
      {
        title: '完成人数',
        expand: false,
        content: '在当前数据范围下，完成任务的人数/收到任务的人数',
      },
      {
        title: '平均进度',
        expand: false,
        content:
          '在当前数据范围下，班级的平均进度。平均进度=每个人完成的进度/班级需要完成的总进度*100%',
      },
      {
        title: '平均学习用时',
        expand: false,
        content:
          '班级学生学习用时的平均数。若中途退出后继续训练，时长会累加，退出时不计入时长',
      },
      {
        title: '平均答题数',
        expand: false,
        content:
          '在当前数据范围下，班级的平均答题数。此数值是截至当下的结果，会根据用户新提交答题结果，实时更新数值',
      },
      {
        title: '平均正确率',
        expand: false,
        content:
          '在当前数据范围下，班级的平均正确率。此数值是截至当下的结果，会根据用户新提交答题结果，实时更新数值。当教师批改后，数值会更新',
      },
    ],
  },
  {
    title: '学生列表',
    expand: false,
    subTitle: '以学生个体展示数据，展示在当前数据范围下的 学生任务详情',
    contentList: [
      {
        title: '进度',
        expand: false,
        content:
          '在当前数据范围下，本人进度-本人完成的进度/本人需完成的总进度*100%',
      },
      {
        title: '学习用时',
        expand: false,
        content:
          '学生从开始学习到最终提交用时时长。若中途退出后继续训练，时长会累加，退出时不计入时长',
      },
      {
        title: '答题数',
        expand: false,
        content:
          '当前数据范围下，学生截至目前完成的题目总数，此数据会根据学生作答而实时更新',
      },
      {
        title: '正确率',
        expand: false,
        content:
          '在当前数据范围下，学生的答题正确率。 此数值是截至当下的结果，会根据用户新提交答题结果，实时更新数值。当教师批改后，数值会更新',
      },
      {
        title: '完成时间',
        expand: false,
        content:
          '在当前数据范围下，学生完成整个任务的时间，包括试卷、视频类型的。若此处无数值，代表学生尚未完成任务',
      },
    ],
  },
])
</script>

<template>
  <div>
    <el-drawer size="46.48vw"
               title="指标说明"
               v-bind="$attrs"
    >
      <div class="pr-[16px]">
        <div
          v-for="(item, index) in instructionInfo"
          :key="index"
          class="w-full bg-[#FFFFFF] mb-[17px] br-[6px] p-17px"
        >
          <div class="flex justify-between" @click="item.expand = !item.expand">
            <div class="text-[#333333] text-[17px] font-600">
              {{ item?.title }}
            </div>
            <img :src="item.expand ? $g.tool.getFileUrl('taskCenter/grayTop.png') : $g.tool.getFileUrl('taskCenter/grayBottom.png')"
                 alt=""
                 class="w-13px h-13px"
            >
          </div>
          <div v-if="item.expand" class="text-[#666666] text-[14px] mt-[13px]">
            {{ item?.subTitle }}
          </div>
          <div v-if="item.expand">
            <div
              v-for="(subItem, subIndex) in item.contentList"
              :key="subIndex"
              class="bg-[#F3F4F9] br-[6px] p-13px mt-[13px]"
            >
              <div
                class="flex justify-between"
                @click="subItem.expand = !subItem.expand"
              >
                <div class="text-[#333333] text-[15px] font-600">
                  {{ subItem?.title }}
                </div>
                <img :src="subItem.expand ? $g.tool.getFileUrl('taskCenter/grayTop.png') : $g.tool.getFileUrl('taskCenter/grayBottom.png')"
                     alt=""
                     class="w-13px h-13px"
                >
              </div>
              <div
                v-if="subItem.expand"
                class="text-[#666666] text-[14px] mt-[13px]"
              >
                {{ subItem?.content }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">
:deep(){
  .el-drawer {
    background-color: #F3F4F9;;
  }
  .el-drawer__header {
    margin-bottom: 0px !important;
    padding: 16px !important;
    color: #333333;
    font-weight: 600;
    font-size: 16px;
  }
  .el-drawer__body {
    padding-top: 0px;
    padding-left: 16px;
    padding-right: 16px;
  }}
</style>
