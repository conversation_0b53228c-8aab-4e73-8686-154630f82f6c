<script setup>
import Draggable from 'vuedraggable'
// eslint-disable-next-line import/no-self-import
import NestedDraggable from './NestedDraggable.vue'

const props = defineProps({
  modelValue: Array, // 父组件传递的列表数据
  top: {
    type: Boolean,
    default: true,
  },
  currentKey: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:modelValue',
'dragstart',
'dragend',
'itemClick',
'onExpand'])

let expandMap = $ref({})

// 🎯 触发父组件的 `update:modelValue` 事件
function emitUpdate() {
  emit('update:modelValue', props.modelValue)
}

// 🎯 更新子节点的数据
function updateChildren(parent, newChildren) {
  parent.children = newChildren
  emitUpdate()
}

function handleStart(event) {
  emit('dragstart', event)
}

function handleEnd(event) {
  emit('dragend', event)
}

function onItemClick(item) {
  emit('itemClick', item)
}

function handleExpand(id) {
  expandMap[id] = true
  const item = props.modelValue.find(v => v.bookCatalogId === id)
  if (item)
    emit('onExpand', item.parentBookCatalogId)
}

async function onOpen(element) {
  expandMap[element.bookCatalogId] = !expandMap[element.bookCatalogId]
  if (expandMap[element.bookCatalogId]) {
    await nextTick()
    $g.tool.renderMathjax()
  }
}

onBeforeMount(() => {
  expandMap = props.modelValue.reduce((obj, item) => {
    obj[item.bookCatalogId] = false
    return obj
  }, {})
  const item = props.modelValue.find(v => v.bookCatalogId === props.currentKey)
  if (item)
    emit('onExpand', item.parentBookCatalogId)
})
</script>

<template>
  <Draggable
    :list="modelValue"
    item-key="id"
    ghost-class="opacity-25"
    drag-class="drag-item"
    handle=".handle"
    :animation="150"
    @change="emitUpdate"
    @start="handleStart"
    @end="handleEnd"
  >
    <template #item="{ element }">
      <div class="pl-16px">
        <div
          class="h-43px flex items-center justify-between relative pl-21px rounded-[9px]"
          :class="{
            'tree-item-active': currentKey === element.bookCatalogId,
            'cursor-pointer': !element.children?.length,
          }"
          @click="onItemClick(element)"
        >
          <img
            v-if="element.children?.length"
            src="@/assets/img/syncClass/arrow-gray-light.png"
            alt="menu"
            class="w-11px h-11px absolute left-5px top-1/2 -translate-y-1/2 van-haptics-feedback"
            :class="{ 'rotate-90': expandMap[element.bookCatalogId] }"
            @click="onOpen(element)"
          >
          <div
            class="leading-[22px] text-13px text-[#333] font-600 title-w"
            :class="{
              '!text-15px': top,
            }"
          >
            <g-mathjax :text="element.bookCatalogName" class="title-row-math truncate" />
          </div>
          <img
            src="@/assets/img/syncClass/menu.png"
            alt="menu"
            class="w-15px h-15px absolute right-9px top-1/2 -translate-y-1/2 cursor-move handle"
            @click.stop
          >
        </div>
        <!-- 递归渲染子节点 -->
        <template v-if="element.children?.length">
          <NestedDraggable
            v-show="expandMap[element.bookCatalogId]"
            :model-value="element.children"
            :top="false"
            :current-key="currentKey"
            @update:model-value="updateChildren(element, $event)"
            @dragstart="handleStart"
            @dragend="handleEnd"
            @item-click="onItemClick"
            @on-expand="handleExpand"
          />
        </template>
      </div>
    </template>
  </Draggable>
</template>

<style lang="scss">
.drag-item {
  max-height: 43px;
  overflow: hidden;
}

.tree-item-active {
  background: #6474FD10 !important;
}

.title-w{
  width: calc(100% - 35px);
}
</style>
