<script setup lang="ts">
import {
  exportClassAbnormalQuestion,
  getActivityStudentCount,
  getActivityStudentList,
} from '@/api/activity'
import usePagination from '@/hooks/usePagination'

let props = defineProps<{
  searchData: any
  subjectList: any
  excelName: string
}>()
let route = useRoute()
let router = useRouter()

let downLoading = $ref(false)
let statisticsCount = $ref<any>({})
const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,
  column: [
    {
      label: '学生姓名',
      prop: 'userName',
    },
    {
      label: '启鸣号',
      prop: 'thirdUniqueId',
      formatter: (row: any) => row.thirdUniqueId || row.accountName || '-',
    },
    {
      label: '做对试题数',
      prop: 'rightQuestionNum',
      sort: true,
    },
    {
      label: '待重做错题',
      prop: 'errorQuestionNum',
      sort: true,
    },
    {
      label: '课件',
      prop: 'attachReadNum',
      sort: true,
    },
    {
      label: '观看微课数',
      prop: 'videoWatchNum',
      sort: true,

    },
    {
      label: '学习知识卡片数',
      prop: 'articleReadNum',
      sort: true,
      width: '150px',
    },
    {
      label: '异常答题数',
      prop: 'abnormalQuestionNum',
      sort: true,
      width: '150px',
      headerSlot: true,
    },
    {
      label: '学习报告',
      prop: 'cz',
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
    all: false,
  },
})
let keyword = $ref<any>('')
async function search() {
  try {
    if (!tableOptions.pageOptions.all)
      tableOptions.pageOptions.page = 1

    tableOptions.loading = true
    await fetchStudentList()
  }
  catch (err) {
    tableOptions.loading = false
    console.log(err)
  }
}

/* 排序 */
async function sortChange({
  order,
  prop,
}) {
  await fetchStudentList({
    order,
    prop,
  })
}
/* 查看 */
function toDetail(row) {
  let excelName = `${props?.excelName}-${row?.userName}-${row?.thirdUniqueId}.xlsx` || '学生报告异常答题数(个人).xlsx'
  router.push({
    name: 'ModuleStatistics',
    query: {
      tabValue: 2,
      activityId: route.query.activityId,
      userName: row.userName,
      thirdUniqueId: row.thirdUniqueId || row.accountName,
      schoolStudentId: row.schoolStudentId,
      accountId: row.accountId,
      sysCourseId: props?.searchData?.activeTab,
      beginDate: props?.searchData?.beginDateTime ?? null,
      endDate: props?.searchData?.endDateTime ?? null,
      source: route?.query?.source ?? null,
      excelName,
    },
  })
}
/* 获取学生数量统计 */
async function fetchStudentStatistics() {
  try {
    let params: any = {
      activityId: route.query.activityId,
      sysCourseId: props?.searchData?.activeTab,
      schoolId: props?.searchData?.schoolId,
      sysGradeId: props?.searchData?.sysGradeId,
      schoolClassId: props?.searchData?.schoolClassId == 'all' ? null : props?.searchData?.schoolClassId,
    }
    if (props?.searchData?.beginDateTime || props?.searchData?.endDateTime) {
      params = {
        ...params,
        beginDateTime: props?.searchData?.beginDateTime ?? null,
        endDateTime: props?.searchData?.endDateTime ?? null,
      }
    }
    let res = await getActivityStudentCount(params)

    statisticsCount = res
  }
  catch (err) {
    console.log('获取学生统计数据失败', err)
  }
}
/* 获取学生列表 */
async function fetchStudentList(sortOptions?) {
  if (!props?.searchData?.schoolId) {
    tableOptions.loading = false
    return
  }
  try {
    tableOptions.loading = true
    let params: any = {
      ...tableOptions.pageOptions,
      activityId: route.query.activityId,
      sysCourseId: props?.searchData?.activeTab,
      schoolId: props?.searchData?.schoolId,
      sysGradeId: props?.searchData?.sysGradeId,
      schoolClassId: props?.searchData?.schoolClassId == 'all' ? null : props?.searchData?.schoolClassId,
      keyword,
    }
    if (props?.searchData?.beginDateTime || props?.searchData?.endDateTime) {
      params = {
        ...params,
        beginDateTime: props?.searchData?.beginDateTime,
        endDateTime: props?.searchData?.endDateTime,
      }
    }
    let {
      list = [],
      total = 0,
    } = await getPageData(params, sortOptions, tableOptions.pageOptions.all)
    tableOptions.loading = false
    tableOptions.pageOptions.total = total
    tableOptions.data = list
  }
  catch (err) {
    tableOptions.loading = false
    console.log('获取学生列表失败', err)
  }
}
const { getPageData } = usePagination({
  api: getActivityStudentList,
  pageOptions: tableOptions.pageOptions,
})
// 导出数据excel
async function exportDataToExcel() {
  try {
    downLoading = true
    let params: any = {
      activityId: route.query.activityId,
      schoolId: props?.searchData?.schoolId,
      sysGradeId: props?.searchData?.sysGradeId,
      schoolClassId: props?.searchData?.schoolClassId,
      // beginDateTime: props?.searchData?.beginDateTime,
    // endDateTime: props?.searchData?.endDateTime,
    }
    if (props?.searchData?.beginDateTime || props?.searchData?.endDateTime) {
      params = {
        ...params,
        beginDateTime: props?.searchData?.beginDateTime,
        endDateTime: props?.searchData?.endDateTime,
      }
    }
    const res = await exportClassAbnormalQuestion(params)

    // 转换数据格式，将科目异常答题数展开到每个学生记录中
    const excelData = res?.map((student: any) => {
      const studentData = { ...student }
      student?.sysSubjectList?.forEach((subject: any) => {
        studentData[subject?.sysSubjectId] = subject?.abnormalQuestionNum
      })
      return studentData
    })

    // 构建表头配置
    const subjectHeaders = props?.subjectList?.map((subject: any) => ({
      label: subject.sysSubjectName,
      prop: subject.sysSubjectId,
    })) || []

    const sheetsData = [{
      name: '学生报告',
      data: excelData,
      headers: [
        {
          label: '学生姓名',
          prop: 'studentName',
        },
        ...subjectHeaders,
      ],
    }]

    // 导出Excel文件
    const fileName = `${props?.excelName}.xlsx` || '学生报告异常答题数(班级).xlsx'
    $g.tool.downloadExcel(sheetsData, fileName, '无可下载的数据')
    downLoading = false
  }
  catch (err) {
    downLoading = false
    console.log('导出数据失败', err)
  }
}
watchDebounced(
  () => props.searchData,
  async (val: any, oldVal: any) => {
    if (val?.activeTab !== oldVal?.activeTab) {
      tableOptions.pageOptions.page = 1
      tableOptions.pageOptions.all = false
      keyword = ''
    }
    await fetchStudentStatistics()
    await fetchStudentList()
  },
  {
    debounce: 150,
    // immediate: true,
  },
)
onMounted(async () => {
  await fetchStudentStatistics()
  await fetchStudentList()
})
</script>

<template>
  <div>
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="text-[17px] font-600">
          学生列表
        </div>
        <div class="ml-10px text-[14px]">
          开通活动人数：<span class="text-[#65CD64]">{{ statisticsCount?.openedStudentNum ?? 0 }}</span>人,
          有使用数据的总计 <span class="text-[#65CD64]">{{ tableOptions?.pageOptions?.total ?? 0 }}</span>人,
          下方表格仅展示<span class="text-[#FF4646]">有使用数据</span>的学生
        </div>
      </div>

      <div class="flex items-center">
        <el-input
          v-model="keyword"
          clearable
          style="width: 181px"
          placeholder="输入学生姓名/启鸣号"
        />
        <el-button color="#6474FD"
                   class="w-64px ml-5px cursor-pointer"
                   @click="search"
        >
          搜索
        </el-button>
        <el-button
          :disabled="!tableOptions?.pageOptions?.total"
          :class="{ '!cursor-not-allowed !text-[#999] !border-[#999]': $g.tool.isTrue(!tableOptions?.pageOptions?.total) }"
          :loading="downLoading"
          class="min-w-[147px] h-[34px] bg-[#fff] br-[17px] border border-[#6474FD] cursor-pointer flex items-center justify-center "
          @click="exportDataToExcel"
        >
          <svg-ri-download-line class="w-17px h-17px flex-shrink-0 text-[#6474FD]" :class="{ '!text-[#999]': $g.tool.isTrue(!tableOptions?.pageOptions?.total) }" />
          <span
            class="text-[13px] text-[#6474FD] font-500 pl-[4px] leading-[19px]"
            :class="{ '!text-[#999] ': $g.tool.isTrue(!tableOptions?.pageOptions?.total) }"
          >
            下载异常答题报告
          </span>
        </el-button>
      </div>
    </div>
    <div class="text-[#636772] text-14px mt-13px">
      更新时间：实时统计
    </div>
    <div :class="$g.isPC ? 'h-[calc(100%-78px-32px)] overflow-auto no-bar' : ''">
      <g-table
        :table-options="tableOptions"
        :border="false"
        :stripe="true"
        :default-sort="{ prop: 'rightQuestionNum', order: 'descending' }"
        show-all-button
        background
        @change-page="fetchStudentList"
        @sort-change="sortChange"
        @show-all="fetchStudentList"
      >
        <template #header-abnormalQuestionNum="{ column }">
          <div class="inline-flex items-center">
            <span>{{ column.label }}</span>
            <el-popover
              placement="top"
              trigger="hover"
              width="284"
              :popper-options="{
                modifiers: [
                  {
                    name: 'offset',
                    options: {
                      offset: [-100, 12],
                    },
                  },
                ],
              }"
            >
              <template #reference>
                <img :src="$g.tool.getFileUrl('taskCenter/blue-info.png')"
                     alt=""
                     class="w-16px h-16px  ml-2px cursor-pointer mb-2px z-10"
                >
              </template>
              <div style="font-size: 15px;">
                对学生答题过程中出现的异常情况数量进行标记，其中异常答题主要涵盖答题速度过快或过慢这两种情况。
              </div>
            </el-popover>
          </div>
        </template>
        <template #cz="{ row }">
          <div class="text-[#6474FD] van-haptics-feedback" @click="toDetail(row)">
            查看
          </div>
        </template>
      </g-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
