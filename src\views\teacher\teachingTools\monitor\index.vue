<script setup lang="ts">
import { getAttendInfo, getClassList, getGradeList, getScreenshot, getStatisticsList } from '@/api/teachingTools'
import StudentPointsCard from './components/StudentPointsCard.vue'

const filterFormData = reactive<any>({
  options: {
    stageList: [], // 学段列表
    gradeList: [], // 年级列表
    classList: [], // 班级列表
  },
  data: {
    periodLevel: null, // 学段id
    gradeId: null, // 年级id
    classId: null, // 班级id
  },
})
let pcList = $ref<any[]>([]) // pc学生列表
let padList = $ref<any[]>([]) // pad学生列表
let offLineList = $ref<any[]>([]) // 离线学生列表
let showLoading = $ref<boolean>(true) // 加载状态
let lastTime = $ref<any>(null)
let timer = $ref<number | null>(null) // 定时器
let config: any = null
let wsServer: any = null
let mqtt: any = (window as any).mqtt
let customClientId = $ref('')
let shotLoading = $ref(false)
let countdownTime = $ref(0) // 倒计时时间
let countdownTimer = $ref<number | null>(null) // 倒计时定时器

const isHttps = $computed(() => {
  return window.location.protocol === 'https:'
})

/* 获取年级信息 */
async function getGradeListApi() {
  try {
    showLoading = true
    const res = await getGradeList({
      periodLevel: filterFormData.data.periodLevel,
    })
    filterFormData.options.gradeList = res.map((item) => {
      return {
        ...item,
        name: item.sysGradeName,
        id: item.sysGradeId,
      }
    })
    filterFormData.data.gradeId = res?.[0]?.sysGradeId
    filterFormData.data.gradeId && await getClassListApi()
  }
  catch (err) {
    showLoading = false
    shotLoading = false
    console.log(err)
  }
}
/* 获取班级信息 */
async function getClassListApi() {
  try {
    showLoading = true
    const res = await getClassList({
      sysGradeId: filterFormData.data.gradeId,
    })
    filterFormData.options.classList = res.map((item) => {
      return {
        ...item,
        name: item.className,
        id: item.schoolClassId,
      }
    })
    filterFormData.data.classId = res?.[0]?.schoolClassId
    filterFormData.data.classId && await getStudentListApi()
  }
  catch (err) {
    showLoading = false
    shotLoading = false
    console.log(err)
  }
}

/* 开始定时器 */
function startTimer() {
  // 清除已有定时器
  clearTimer()
  timer = window.setInterval(() => {
    getStudentListApi(false)
  }, 5000)
}

/* 清除定时器 */
function clearTimer() {
  if (timer !== null) {
    clearInterval(timer)
    timer = null
  }
}

/* 获取学生在线信息列表 */
async function getStudentListApi(showLoadingFlag = true, isInit = false) {
  try {
    if (showLoadingFlag) {
      showLoading = true
    }
    const res = await getStatisticsList({
      schoolClassId: filterFormData.data.classId,
    })
    pcList = res.pcOnlineList
    padList = res.padOnlineList
    offLineList = res.offlineList
    lastTime = res.lastScreenshotTime
    showLoading = false

    // 第一次加载完成后启动定时器
    if (showLoadingFlag && timer === null) {
      startTimer()
    }
    if (isInit) {
      if (res?.status === 1) {
        shotLoading = true
        nextTick(() => {
          $g.bus.emit('check-url')
        })
      }
      else {
        shotLoading = false
      }
    }
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}

/* 年级改变 */
async function handleGradeChange() {
  filterFormData.data.classId = null
  filterFormData.options.classList = []
  clearTimer() // 清除定时器
  await getClassListApi()
}

/* 班级改变 */
async function handleClassChange() {
  clearTimer() // 清除定时器
  // 清除倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
    countdownTime = 0
  }
  await getStudentListApi(false, true)
  setTimeout(() => {
    startTimer()
  }, 1000)
}

async function initConfig(only = false) {
  // 初始化配置
  const res = await getAttendInfo()

  config = res
  config.mqttPassword = $g.tool.decryptMqttPassword(
    config.clientId,
    config.mqttPassword,
  )
  config.mqttUserName = $g.tool.decryptMqttPassword(
    config.clientId,
    config.mqttUserName,
  )

  if (!only) { initWsSocket() }
}

function initWsSocket() {
  let protocol = isHttps ? 'wss' : 'ws'
  customClientId = `${config.pcClientId}_${$g.tool.uuid(4)}` // 连接选项
  const options = {
    protocolVersion: 5,
    protocol,
    clean: true, // true: 清除会话, false: 保留会话
    connectTimeout: 4000, // 超时时间
    // 认证信息
    clientId: customClientId,
    username: config.mqttUserName,
    password: config.mqttPassword,
    reconnectPeriod: 3000,
    rejectUnauthorized: false,
  }

  const connectUrl = isHttps ? config.wssPortDomain : config.wsPortDomain

  wsServer = mqtt.connect(connectUrl, options)

  wsServer.on('connect', () => {
    console.log('💊  连接==> ')
  })

  wsServer.on('offline', () => {
    console.log('离线')
  })

  wsServer.on('error', (error) => {
    console.log('error', error)
  })

  wsServer.on('reconnect', () => {
    console.log('重新连接...')
  })

  wsServer.on('disconnect', (packet) => {
    console.log('断开', packet)
  })

  wsServer.on('message', (topic, message) => {
    const { data, type } = JSON.parse(message)
    if (type === 10001) {
      $g.bus.emit(data.idNum, data)
    }
    else if (type === 10002) {
      shotLoading = false
    }
  })
}

/* 截屏 */
async function onScreenShot() {
  if (shotLoading || countdownTime > 0) { return }
  if (!padList?.length) { return }
  shotLoading = true
  getScreenshot({
    schoolClassId: filterFormData.data.classId || null,
    customClientId,
  })

  // 开始倒计时
  countdownTime = 10
  countdownTimer = window.setInterval(() => {
    countdownTime--
    if (countdownTime <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)
}

onMounted(() => {
  getGradeListApi()
  initConfig()
})

onBeforeUnmount(() => {
  if (wsServer) {
    wsServer.end(false, () => {
      console.log('disconnected successfully')
    })
  }
  // 清除倒计时定时器
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimer()
  // 清除倒计时定时器
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<template>
  <div class="flex flex-col h-full monitorContainer">
    <g-navbar title="平板监控" class="px-26px pt-26px">
    </g-navbar>
    <div class="px-26px overflow-y-auto pb-75px">
      <!-- 筛选 -->
      <div class="flex justify-between mt-26px ">
        <div class="flex items-center">
          <el-select
            v-model="filterFormData.data.gradeId"
            placeholder="请选择年级"
            class="custom-select w-[156px]"
            @change="handleGradeChange"
          >
            <el-option
              v-for="item in filterFormData.options.gradeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="filterFormData.data.classId"
            placeholder="请选择班级"
            class="custom-select w-[156px] ml-27px"
            @change="handleClassChange"
          >
            <el-option
              v-for="item in filterFormData.options.classList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <div v-else>
        <div class="text-[#97989A] text-14px mt-18px flex justify-between">
          <div>当前在线人数{{ pcList.length + padList.length }}，离线人数{{ offLineList.length }}，截图仅展示在线学生平板画面，暂不支持对PC端画面进行截图</div>
          <div class="flex items-center">
            <img
              class="w-[12px] h-[12px] mr-4px -translate-y-1px"
              :src="$g.tool.getFileUrl('teachingTools/refresh.png')"
            />
            <div>数据更新于{{ lastTime }}</div>
          </div>
        </div>
        <div class="font-600 text-18px my-25px">
          在线人数（{{ pcList.length + padList.length }}）
        </div>
        <div class="bg-[#fff] rounded-[9px] w-full p-18px">
          <!-- 平板 -->
          <div class="text-17px">
            平板在线（{{ padList.length }}）
          </div>
          <div
            v-if="padList.length"
            class="mb-18px flex flex-wrap"
          >
            <div v-for="item in padList" :key="item.idNum">
              <StudentPointsCard :student-info="item" :loading="shotLoading" class="mt-20px mr-22px" />
            </div>
          </div>
          <!-- PC -->
          <div class="text-17px">
            PC在线（{{ pcList.length }}）
          </div>
          <div class="flex flex-wrap">
            <div
              v-for="item in pcList"
              :key="item.idNum"
              class="w-88px h-54px border rounded-[5px] border-[#C4C4C4] flex justify-center items-center text-16px relative mt-20px mr-22px cursor-pointer"
            >
              {{
                item?.studentName?.length > 4
                  ? `${item?.studentName.substring(0, 4)}...`
                  : item?.studentName
              }}
              <div class="w-11px h-11px rounded-[100%] bg-[#00D3A9] absolute right-[-5px] top-[-5px]"></div>
            </div>
          </div>
        </div>
        <div class="font-600 text-18px my-25px">
          离线人数（{{ offLineList.length }}）
        </div>
        <div class="bg-[#fff] rounded-[9px] text-16px flex flex-wrap w-full p-18px">
          {{ offLineList.map(item => item.studentName).join('、') }}
        </div>
      </div>
    </div>
    <div class="bg-[#FFFFFF] w-full h-61px fixed bottom-0 flex justify-center items-center">
      <div
        class="w-110px h-45px rounded-[23px] flex flex-col justify-center items-center text-[#fff] cursor-pointer"
        :class="[
          padList.length && countdownTime === 0 ? 'bg-[#3C9EF9]' : 'bg-[rgba(102,102,102,0.6)]',
          countdownTime > 0 || !padList.length ? 'cursor-not-allowed' : 'cursor-pointer',
        ]"
        @click="onScreenShot"
      >
        <div>
          <template v-if="countdownTime > 0">
            <div class="flex flex-col justify-center items-center">
              <div class="text-16px font-500">
                截图中
              </div>
              <div class="text-12px">
                {{ countdownTime }}s后可再次截图
              </div>
            </div>
          </template>
          <template v-else>
            开始截图
          </template>
        </div>
        <img
          v-if="padList.length && countdownTime === 0"
          class="w-[15px] h-[13px]"
          :src="$g.tool.getFileUrl('teachingTools/clipper.png')"
        />
        <div v-else-if="!padList.length" class="text-11px">
          当前无人平板在线
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.custom-select {
  --el-border-radius-base: 16px;
  --el-fill-color-blank: rgba(255,255,255,1);
  --el-border-color: rgba(235, 235, 235, 1);
  --el-border-color-hover: #6365ff;
  --el-popover-border-radius: 9px; /* 弹窗圆角 */
  --el-text-color-primary: #3C9EF9; /* 选中文本颜色 */
  --el-input-text-color: #000;
  :deep() {
  .el-select__wrapper {
      height: 43px;
    }
  }
}
</style>
