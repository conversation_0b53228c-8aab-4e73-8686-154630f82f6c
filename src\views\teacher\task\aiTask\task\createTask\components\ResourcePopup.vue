<script setup lang="ts">
let showDialog = defineModel<boolean>('show')
const studentData = inject<Ref<any>>('studentData', ref({}))

// 已加入数量
let selectedResourceCount = $computed(() => {
  return (list) => {
    let count = 0
    list.forEach((its) => {
      count += its?.selectedResourceList?.length ?? 0
    })
    return count
  }
})
// 删除
function remove(parentIndex: number, childIndex: number) {
  const parent = studentData.value.selectedResource[parentIndex]
  if (parent && Array.isArray(parent.selectedResourceList) && childIndex > -1) {
    parent.selectedResourceList.splice(childIndex, 1)
    // 如果该章节下已无资源，移除该章节
    if (parent.selectedResourceList.length === 0)
      studentData.value.selectedResource.splice(parentIndex, 1)
  }
}
function formatDesc(obj: any) {
  if (!obj.resourceList?.length) return ''
  const typeMap: Record<number, string> = {
    1: '道题',
    2: '个卡片',
    3: '个视频',
    4: '个课件',
  }
  const result: string[] = obj.resourceList
    .filter(item => item.resourceNum > 0 && typeMap[item.resourceType])
    .map(item => `${item.resourceNum}${typeMap[item.resourceType]}`)
  if (obj.computeDuration && obj.computeDuration > 0)
    result.push(`约${Math.ceil(obj.computeDuration / 60)}分钟`)

  return `(${result.join('，')})`
}
</script>

<template>
  <div>
    <van-popup
      v-model:show="showDialog"
      position="right"
      safe-area-inset-top
      safe-area-inset-bottom
      close-on-popstate
      class="w-600px h-screen flex flex-col py-17px pl-21px pr-18px overflow-hidden bg-[#F3F4F9]"
      teleport="#app"
      v-bind="$attrs"
    >
      <div class="flex items-center justify-between font-600 text-17px mb-21px">
        <span>已加入({{ selectedResourceCount(studentData.selectedResource) }})</span>
        <img
          class="cursor-pointer w-15px"
          src="@/assets/img/taskCenter/close.png"
          @click="showDialog = false"
        />
      </div>
      <!-- 内容 -->
      <div>
        <div v-for="(item, parentIndex) in studentData.selectedResource"
             :key="item.bookCatalogId"
             class="p-17px br-6px bg-[#fff]"
        >
          <g-mathjax :text="item. bookCatalogName" class="font-600 text-[15px] truncate" />
          <div v-for="(it, childIndex) in item.selectedResourceList"
               :key="it.bookCatalogId"
               class="flex justify-between mt-17px"
          >
            <div class="flex items-center ">
              <img
                :src="$g.tool.getFileUrl('taskCenter/book.png')"
                alt=""
                class="w-17px h-17px mr-10px"
              />
              <div class="text-15px text-[#333] font-500 leading-[19px] mb-6px">
                {{ it.bookCatalogName }}<span class="pl-8px">{{ formatDesc(it) }}</span>
              </div>
            </div>

            <div
              class=" text-[#FF4646] border-[1px] border-solid border-[#FF4646] px-9px h-30px leading-[30px] rounded-[4px] flex-shrink-0 cursor-pointer"
              @click="remove(parentIndex, childIndex)"
            >
              取消加入
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped>

</style>
