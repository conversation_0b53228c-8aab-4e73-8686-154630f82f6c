<script lang="ts" setup>
import type {
  UploadProps,
  UploadRequestOptions,
  UploadUserFile,
} from 'element-plus'

import AliOss from '@/plugins/OSS'

const props = defineProps({
  mode: {
    // picture-card图片卡片模式
    type: String as PropType<'picture-card' | 'picture' | 'text'>,
    default: 'picture-card',
  },
  // 允许上传的文件类型 (MIME类型 文件扩展名或混合使用 例如 image/*、.pdf)
  accept: {
    type: String,
    default: 'image/*',
  },
  // 文件大小限制，单位 字节
  maxSize: {
    type: Number,
  },
  // 文件数量限制 默认不限制
  limit: {
    type: Number,
  },
  // 是否显示上传提示,布尔值或者字符串提示
  tips: {
    type: [Boolean, String],
    default: true,
  },
  // 视频分辨率限制 例:["1920×1080", "1280×720"]
  videoResolution: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['uploadSuccess'])
const aliOss = new AliOss()
aliOss.on('onProgress', ({
  val,
  speed,
}, id) => {
  fileList.value.forEach((e) => {
    if (e.uid == id) {
      e.percentage = val
      e.speed = speed
    }
  })
})

const fileList = defineModel<any[]>('fileList', {
  default: () => [],
})

async function customUpload({ file }) {
  try {
    file.status = 'uploading'
    let {
      uid,
      raw,
      size,
      name,
    } = file
    aliOss
      .uploadFile({
        file: raw,
        id: uid,
        size,
        name,
      })
      .then(async ({
        resource_url,
        fullUrl,
      }) => {
        const item = $g._.find(fileList.value, e => e.uid == file.uid)
        item.status = 'success'
        item.resource_url = resource_url
        item.fullUrl = fullUrl
        item.url = fullUrl
        // 视频类型
        if (['mp4', 'm3u8'].includes(item?.name?.split('.')?.pop())) {
          let videoInfo: any = await getVideoInfo(raw)
          item.duration = videoInfo?.duration
          item.url = `${resource_url}?x-oss-process=video/snapshot,t_1000,f_jpg,w_400,h_600,m_fast`
        }
        // 音频
        if ($g.tool.resourceType(item.name) == 'audio')
          item.duration = await getAudioDuration(raw)

        emit('uploadSuccess', file, fileList)
      })
      .catch((err) => {
        console.log(err)
        $g.showToast('上传失败')
        const index = $g._.findIndex(fileList.value, e => e.uid == file.uid)
        if (index >= 0)
          fileList.value.splice(index, 1)
      })
  }
  catch (error) {
    $g.showToast(error)
  }
}
function getAudioDuration(file) {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file)
    const audio = document.createElement('audio')
    audio.src = url
    audio.addEventListener('loadedmetadata', () => {
      resolve(Number.parseInt(audio.duration as any))
      URL.revokeObjectURL(url)
    })
    audio.addEventListener('error', (e) => {
      reject('音频加载失败')
      URL.revokeObjectURL(url)
    })
  })
}
function getVideoInfo(file) {
  return new Promise((resolve, reject) => {
    let url = URL.createObjectURL(file)
    let videoElement = document.createElement('video')
    videoElement.addEventListener('loadedmetadata', () => {
      let duration = Number.parseInt(videoElement.duration as any)
      let width = videoElement.videoWidth
      let height = videoElement.videoHeight
      // 释放 URL 对象
      URL.revokeObjectURL(url)

      resolve({
        duration,
        width,
        height,
      })
    })

    // 处理视频加载错误
    videoElement.addEventListener('error', (e) => {
      URL.revokeObjectURL(url)
      reject(`视频加载失败：${e.message}`)
    })

    // 设置视频文件源
    videoElement.src = url
  })
}
async function limitVideoResolution(file) {
  return new Promise((resolve) => {
    if (!props.videoResolution.length) {
      resolve(true)
      return
    }
    getVideoInfo(file).then((res: any) => {
      // 遍历目标分辨率数组，检查是否匹配
      let isValid = props.videoResolution.some((resolution) => {
        const [targetWidth, targetHeight] = (resolution as string)
          .split('×')
          .map(Number)
        return res.width === targetWidth && res.height === targetHeight
      })
      if (!isValid) {
        $g.showToast(
          `视频分辨率不符合要求，请上传${props.videoResolution.join('、')}分辨率的视频`,
          'error',
        )
      }
      resolve(isValid)
    })
  })
}
async function handleChange(
  uploadFile: UploadUserFile,
  uploadFiles: UploadUserFile[],
) {
  // 如果是新添加的文件，立即开始上传
  if (uploadFile.status === 'ready') {
    // 进行文件验证
    const isValid = await validateFile(uploadFile.raw, uploadFiles)
    if (!isValid) {
      // 验证失败，从文件列表中移除该文件
      const index = uploadFiles.findIndex(file => file.uid === uploadFile.uid)
      if (index !== -1) {
        fileList.value.splice(index, 1)
        uploadFiles.splice(index, 1)
      }
      return
    }
    customUpload({ file: uploadFile } as UploadRequestOptions)
  }
}

function handleRemove(
  uploadFile: UploadUserFile,
  uploadFiles: UploadUserFile[],
) {
  if (uploadFile.status == 'uploading')
    aliOss.cancelUpload(uploadFile.uid)
}

// 格式化接受的文件类型显示
const formatAcceptType = computed(() => {
  return props.accept
    .split(',')
    .map((type) => {
      type = type.trim()
      // 处理 MIME 类型
      if (type.includes('/')) {
        if (type === 'image/*')
          return '图片'

        if (type === 'video/*')
          return '视频'

        if (type === 'audio/*')
          return '音频'

        return type.split('/')[1].toUpperCase()
      }
      // 处理文件扩展名
      return type.toUpperCase()
    })
    .join('、')
})

// 验证文件
async function validateFile(file, uploadFiles): Promise<boolean> {
  // 检查文件类型
  if (props.accept) {
    const isValidType =
      file.type === props.accept ||
      props.accept.includes(`${file.type.split('/')[0]}/*`) ||
      props.accept.split(',').some(type => file.name.endsWith(type.trim()))
    if (!isValidType) {
      $g.showToast(`只能上传 ${props.accept} 类型的文件`)
      return false
    }
  }

  // 检查文件大小
  if (props.maxSize && file.size > props.maxSize) {
    $g.showToast(`文件大小不能超过 ${$g.tool.formatFileSize(props.maxSize)}`)
    return false
  }
  // 检查数量限制
  if (props.limit && uploadFiles.length > props.limit) {
    $g.showToast(`最多只能上传 ${props.limit} 个文件`)
    return false
  }
  // 适配分辨率检测
  const limitVideoResolutionFlag = await limitVideoResolution(file)
  if (!limitVideoResolutionFlag)
    return false

  return true
}

// 图片预览
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  const index = fileList.value.findIndex(item => item.uid == uploadFile.uid)
  const list = fileList.value.map(item => item.fullUrl)
  $g.flutter('previewImage', {
    urls: list,
    index,
    canShare: false,
    isShowDown: false,
    onTapClose: true,
  })
}

defineExpose({ handleRemove })
</script>

<template>
  <div class="no-conversion">
    <el-upload
      v-model:file-list="fileList"
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      :list-type="mode"
      v-bind="$attrs"
      :http-request="customUpload"
      :on-change="handleChange"
      :on-remove="handleRemove"
      :accept="accept"
      :auto-upload="false"
      :multiple="multiple"
    >
      <!-- 使用具名插槽允许自定义上传样式 -->
      <slot>
        <div class="flex-cc flex-col">
          <svg-ri-add-line class="text-20px text-[#6474FD]" />
          <div class="mt-1 text-sm text-[#8c939d] hidden">
            点击上传
          </div>
        </div>
      </slot>

      <!-- 提示信息插槽 -->
      <template v-if="tips" #tip>
        <slot name="tip">
          <div class="text-sm space-y-1 mt-10px">
            <el-tag type="info">
              <div v-if="typeof tips === 'string'">
                {{ tips }}
              </div>
              <template v-else>
                提示:
                <span>只支持上传{{ formatAcceptType }}类型&nbsp;</span>
                <span v-if="maxSize">
                  单个文件大小限制：{{ $g.tool.formatFileSize(maxSize) }}&nbsp;
                </span>
                <span v-if="limit">最多可上传 {{ limit }} 个文件</span>
              </template>
            </el-tag>
          </div>
        </slot>
      </template>
    </el-upload>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-upload-list--picture-card .el-progress {
    top: 50%;
  }
  .el-upload.el-upload--picture-card {
    --el-upload-picture-card-size: 96px;
    border: 1px solid #DCDFE6;
    background-color: #FAFBFF;
  }
  .el-upload-list.el-upload-list--picture-card {
    --el-upload-list-picture-card-size: 96px;
  }
}
</style>
