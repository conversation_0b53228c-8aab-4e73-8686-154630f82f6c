<script setup>
import { ImagePreview } from 'vant'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  images: {
    type: Array,
    default: () => [],
  },
  startPosition: {
    type: Number,
    default: 0,
  },
  // 最小缩放比例
  minZoom: {
    type: Number,
    default: 0.1,
  },
  // 最大缩放比例
  maxZoom: {
    type: Number,
    default: 3,
  },
  // 每次缩放的步长
  zoomStep: {
    type: Number,
    default: 0.2,
  },
  // 每次旋转的角度
  rotateStep: {
    type: Number,
    default: 90,
  },
})

let show = $ref(false)
// 当前缩放比例
let scale = $ref(1)
// 当前旋转角度（使用累加值确保连续性）
let rotate = $ref(0)
// 记录旋转次数，用于确保永远是顺时针方向
let rotateCount = $ref(0)

onMounted(() => {
  show = true
})

watch(
  () => show,
  (newVal) => {
    if (!newVal) {
      const element = document.getElementById(props.id)
      element.remove()
    }
  },
)

const imagePreviewRef = $ref(null)
let currentIndex = $ref(0)
const maxLength = props.images.length
currentIndex = props.startPosition

function pageChange(index) {
  currentIndex = index
}

function changeImg(val) {
  // 如果切换到头尾 则return
  if (currentIndex + val < 0 || currentIndex + val > maxLength - 1)
    return

  currentIndex += val
  imagePreviewRef?.swipeTo(currentIndex)
}

// 放大图片
function zoomIn() {
  if (scale < props.maxZoom) {
    scale = Math.min(props.maxZoom, scale + props.zoomStep)
    applyTransform()
  }
}

// 缩小图片
function zoomOut() {
  if (scale > props.minZoom) {
    scale = Math.max(props.minZoom, scale - props.zoomStep)
    applyTransform()
  }
}

// 旋转图片
function rotateImage(type) {
  if (type === 'anticlockwise') {
    rotateCount--
  }
  else {
    rotateCount++
  }
  rotate = rotateCount * props.rotateStep
  applyTransform()
}

// 重置图片状态
function resetImage() {
  scale = 1
  rotate = 0
  rotateCount = 0
  applyTransform()
}

// 应用变换到当前图片
function applyTransform() {
  const currentImage = document.querySelectorAll('.van-image-preview__image img')[currentIndex]
  if (currentImage)
    currentImage.style.transform = `scale(${scale}) rotate(${rotate}deg)`
}

// 切换图片时重置变换
watch(() => currentIndex, () => {
  resetImage()
})
</script>

<template>
  <div>
    <ImagePreview
      ref="imagePreviewRef"
      v-model:show="show"
      :images="images"
      :start-position="startPosition"
      :closeable="true"
      @change="pageChange"
    />
    <Teleport v-if="show" to="body">
      <!-- 左右导航按钮 -->
      <div v-if="images?.length > 1"
           class="left flex-cc"
           @click="changeImg(-1)"
      >
        <svg-ri-arrow-left-s-line />
      </div>
      <div v-if="images?.length > 1"
           class="right flex-cc"
           @click="changeImg(1)"
      >
        <svg-ri-arrow-right-s-line />
      </div>

      <!-- 图片控制面板 -->
      <div class="image-controls">
        <div class="control-btn flex-cc" @click="zoomIn">
          <svg-ri-zoom-in-line class="text-24px" />
        </div>
        <div class="control-btn flex-cc" @click="zoomOut">
          <svg-ri-zoom-out-line class="text-24px" />
        </div>
        <div class="control-btn flex-cc" @click="rotateImage('anticlockwise')">
          <svg-ri-anticlockwise-2-line class="text-24px" />
        </div>
        <div class="control-btn flex-cc" @click="rotateImage('clockwise')">
          <svg-ri-clockwise-2-line class="text-24px" />
        </div>
      </div>
    </Teleport>
  </div>
</template>

<style lang="scss" scoped>
.left {
  position: absolute;
  z-index: 9999;
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
  width: 44px;
  height: 44px;
  font-size: 24px;
  color: #fff;
  background: #00000099;
  border-radius: 50%;
  cursor: pointer;
}
.right {
  position: absolute;
  z-index: 9999;
  top: 50%;
  transform: translateY(-50%);
  right: 40px;
  width: 44px;
  height: 44px;
  font-size: 24px;
  color: #fff;
  background: #00000099;
  border-radius: 50%;
  cursor: pointer;
}

.image-controls {
  position: absolute;
  z-index: 9999;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16px;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 24px;
}

.control-btn {
  width: 40px;
  height: 40px;
  color: #fff;
  background: rgba(204, 204, 204, 0.3);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: rgba(204, 204, 204, 0.5);
  }
}

:deep() {
  .van-image-preview__close-icon {
    font-size: 20px;
  }

  .van-image-preview__image img {
    transition: transform 0.3s ease;
  }
}
</style>
