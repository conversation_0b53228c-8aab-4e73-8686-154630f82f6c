<script setup lang="ts">
import { getActivityStudentCount, getCorrectionReport } from '@/api/activity'

let props = defineProps<{
  searchData: any
  subjectList: any
  excelName: string
  sysCourseId: string
}>()
let statisticsCount = $ref<any>({
  openedStudentNum: 0,
  usedStudentNum: 0,
})
const route = useRoute()
let sortType = $ref<any>(null)
let sortProp = $ref<any>(null)
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      label: '学生姓名',
      prop: 'accountName',
    },
    {
      label: '启鸣号',
      prop: 'idNum',
    },
    {
      label: '已订正题目数',
      prop: 'amendTotal',
      sort: true,
    },
    {
      label: '未订正题目数',
      prop: 'notAmendTotal',
      sort: true,
    },
    {
      label: '全部错题数',
      prop: 'errorTotal',
      sort: true,
    },
    {
      label: '订正率',
      prop: 'amendRate',
      formatter: (row: any) => `${Math.round(row.amendRate * 100)}%`,
      sort: true,
    },
    {
      label: '订正详情',
      prop: 'cz',
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})
let keyword = $ref<any>('')
const router = useRouter()
/* 获取学生数量统计 */
async function fetchStudentStatistics() {
  try {
    let params: any = {
      activityId: route.query.activityId,
      sysCourseId: props?.searchData?.activeTab,
      schoolId: props?.searchData?.schoolId,
      sysGradeId: props?.searchData?.sysGradeId,
      schoolClassId: props?.searchData?.schoolClassId == 'all' ? null : props?.searchData?.schoolClassId,
    }
    if (props?.searchData?.beginDateTime || props?.searchData?.endDateTime) {
      params = {
        ...params,
        beginDateTime: props?.searchData?.beginDateTime ?? null,
        endDateTime: props?.searchData?.endDateTime ?? null,
      }
    }
    let res = await getActivityStudentCount(params)

    statisticsCount = res
  }
  catch (err) {
    console.log('获取学生统计数据失败', err)
  }
}
/* 获取订正报告 */
async function fetchCorrectionReport() {
  try {
    tableOptions.loading = true
    let params: any = {
      activityId: route.query.activityId,
      ...props.searchData,
      sysCourseId: props.sysCourseId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.pageSize,
      keyword,
      amendSortType: sortProp,
      sortValue: sortType,
    }
    let res = await getCorrectionReport(params)
    tableOptions.loading = false
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  }
  catch (err) {
    console.log('获取订正报告失败', err)
  }
}

async function search() {
  tableOptions.pageOptions.page = 1
  await fetchCorrectionReport()
}
watchDebounced(
  () => [props.searchData, props.sysCourseId],
  async ([_, tab], oldValue) => {
    console.log('tab', props.sysCourseId)
    if (!oldValue || tab !== oldValue[1]) {
      tableOptions.pageOptions.page = 1
      tableOptions.pageOptions.all = false
      keyword = ''
    }
    await fetchCorrectionReport()
    await fetchStudentStatistics()
  },
  {
    debounce: 150,
    // immediate: true,
  },
)
onBeforeMount(() => {
  fetchStudentStatistics()
  fetchCorrectionReport()
})
function toDetail(row?) {
  router.push({
    name: 'CorrectionReport',
    query: {
      sysCourseId: props.sysCourseId,
      accountId: row?.accountId,
      beginDateTime: props.searchData?.beginDateTime,
      endDateTime: props.searchData?.endDateTime,
      userName: row?.accountName,
      courseName: props.subjectList?.find(item => item.sysCourseId == props.sysCourseId)?.sysSubjectName,
    },
  })
}
async function sortChange({
  prop,
  order,
}) {
  sortType = order ? order.toUpperCase() : null
  const sortMap = {
    amendTotal: 'AMEND',
    notAmendTotal: 'NO_AMEND',
    errorTotal: 'ALL_ERROR',
    amendRate: 'AMEND_RATE',
  }
  sortProp = sortMap[prop]
  tableOptions.pageOptions.page = 1
  await fetchCorrectionReport()
}
</script>

<template>
  <div class="bg-white p-[17px] rounded-[6px]">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="text-[17px] font-600" @click="toDetail()">
          学生列表
        </div>
        <div class="ml-10px text-[14px]">
          开通活动人数：<span class="text-[#65CD64]">{{ statisticsCount?.openedStudentNum ?? 0 }}</span>人,
          有使用数据的总计 <span class="text-[#65CD64]">{{ tableOptions?.pageOptions?.total ?? 0 }}</span>人,
          下方表格仅展示<span class="text-[#FF4646]">有使用数据</span>的学生
        </div>
      </div>

      <div class="flex items-center">
        <el-input
          v-model="keyword"
          clearable
          style="width: 181px"
          placeholder="输入学生姓名/启鸣号"
        />
        <el-button color="#6474FD"
                   class="w-64px ml-5px cursor-pointer"
                   @click="search"
        >
          搜索
        </el-button>
      </div>
    </div>
    <div class="text-[#636772] text-14px mt-13px">
      更新时间：实时统计
    </div>
    <g-table :table-options="tableOptions"
             @change-page="fetchCorrectionReport"
             @sort-change="sortChange"
    >
      <template #cz="{ row }">
        <div class="text-[#6474FD] van-haptics-feedback cursor-pointer" @click="toDetail(row)">
          查看
        </div>
      </template>
    </g-table>
  </div>
</template>

<style lang="scss" scoped>

</style>
