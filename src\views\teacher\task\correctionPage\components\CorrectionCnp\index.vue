<script setup lang="ts">
import OSS from '@/plugins/OSS'
import {
  Canvas,
  Control,
  Path,
  PencilBrush,
  Textbox,
  util,
} from 'fabric'
import TextBoxEditDialog from './TextBoxEditDialog.vue'

const props = defineProps({
  imgSrc: {
    type: String,
    default: '',
  },
  // 初始化时传入的json数据
  noteData: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['clear'])
// fabric canvas实例
let canvas: Canvas | null = null
// 容器元素
const container = useTemplateRef('container')
// 图片容器
const imgContainer = useTemplateRef('imgContainer')
// 图片最小缩放限制
const MIN_SCALE = 0.2
// 图片最大缩放限制
const MAX_SCALE = 2
// 图片是否已经加载完成
const imgLoaded = ref(false)
// 图片是否加载失败
const imgLoadError = ref(false)
// 图片容器的缩放平移数据
const transform = ref({
  scale: 1,
  translateX: 0,
  translateY: 0,
})
// 当前工具栏选择的模式
const curMode = ref('move')
// 工具栏列表
const toolList = [
  {
    name: '撤销',
    type: 'undo',
    icon: 'correctionPage/undo_disabled.png',
    activeIcon: 'correctionPage/undo.png',
  },
  {
    name: '重做',
    type: 'redo',
    icon: 'correctionPage/redo_disabled.png',
    activeIcon: 'correctionPage/redo.png',
  },
  {
    name: '一键清除',
    type: 'clear',
    icon: 'correctionPage/clear.png',
    activeIcon: 'correctionPage/clear.png',
  },
  {
    name: '移动',
    type: 'move',
    icon: 'correctionPage/move.png',
    activeIcon: 'correctionPage/move_active.png',
  },
  {
    name: '打√',
    type: 'truth',
    icon: 'correctionPage/truth.png',
    activeIcon: 'correctionPage/truth_active.png',
  },
  {
    name: '打×',
    type: 'wrong',
    icon: 'correctionPage/wrong.png',
    activeIcon: 'correctionPage/wrong_active.png',
  },
  {
    name: '文字',
    type: 'text',
    icon: 'correctionPage/text.png',
    activeIcon: 'correctionPage/text_active.png',
  },
  {
    name: '绘制',
    type: 'draw',
    icon: 'correctionPage/draw.png',
    activeIcon: 'correctionPage/draw_active.png',
  },
]

// 删除图标
let deleteImg = document.createElement('img')
deleteImg.src = 'data:image/svg+xml,%3C%3Fxml version=\'1.0\' encoding=\'utf-8\'%3F%3E%3C!DOCTYPE svg PUBLIC \'-//W3C//DTD SVG 1.1//EN\' \'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\'%3E%3Csvg version=\'1.1\' id=\'Ebene_1\' xmlns=\'http://www.w3.org/2000/svg\' xmlns:xlink=\'http://www.w3.org/1999/xlink\' x=\'0px\' y=\'0px\' width=\'595.275px\' height=\'595.275px\' viewBox=\'200 215 230 470\' xml:space=\'preserve\'%3E%3Ccircle style=\'fill:%23F44336;\' cx=\'299.76\' cy=\'439.067\' r=\'218.516\'/%3E%3Cg%3E%3Crect x=\'267.162\' y=\'307.978\' transform=\'matrix(0.7071 -0.7071 0.7071 0.7071 -222.6202 340.6915)\' style=\'fill:white;\' width=\'65.545\' height=\'262.18\'/%3E%3Crect x=\'266.988\' y=\'308.153\' transform=\'matrix(0.7071 0.7071 -0.7071 0.7071 398.3889 -83.3116)\' style=\'fill:white;\' width=\'65.544\' height=\'262.179\'/%3E%3C/g%3E%3C/svg%3E'

// 历史记录栈
let history = shallowRef<any[]>([])
// 历史记录下标
let historyIndex = ref(-1)
// 最大的历史记录栈数量
const MAX_HISTORY_LENGTH = 20
// 是否可以撤销
let canUndo = computed(() => historyIndex.value > 0)
// 是否可以重做
let canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 是否显示编辑textbox文字的弹窗
let showEditDialog = $ref(false)
// 当前激活的TextBox
let activeTextBox = undefined as any
// 存储正在编辑的文字
let editText = ref('')

// 画笔粗细
const PENCIL_WIDTH = 4
// 画笔颜色
const PENCIL_COLOR = '#ff0101'
// 画布是否有操作痕迹
let modifiedFlag = false
// 是否在导出中
let isExporting = ref(false)

/** 初始化数据 */
async function initData() {
  resetData()
  await nextTick()
  await initImage()
  initCanvas()
}

watch(() => props.imgSrc, (v) => {
  v && initData()
}, { immediate: true })

/** 重置数据 */
function resetData() {
  // 清空历史记录
  history.value = []
  historyIndex.value = -1
  // 重置当前模式
  curMode.value = 'move'
  // 重置图片缩放数据
  transform.value = {
    scale: 1,
    translateX: 0,
    translateY: 0,
  }
  // 清空canvas
  if (canvas) {
    canvas.dispose()
    canvas = null
  }
  modifiedFlag = false
}

/** 初始化图片 */
async function initImage() {
  imgLoaded.value = false
  imgLoadError.value = false

  try {
    await new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = resolve
      img.onerror = reject
      img.src = props.imgSrc
    })
    if (!container.value || !imgContainer.value)
      throw new Error('容器不存在')

    const containerWidth = container.value.clientWidth
    const containerHeight = container.value.clientHeight
    const imgWidth = imgContainer.value.clientWidth
    const imgHeight = imgContainer.value.clientHeight
    // 计算缩放比例
    const scaleX = containerWidth / imgWidth
    const scaleY = containerHeight / imgHeight
    // 使用 Math.min 和 Math.max 限制缩放范围
    const scale = Math.min(Math.max(Math.min(scaleX, scaleY), MIN_SCALE), MAX_SCALE) - 0.01

    // 计算平移距离
    const translateX = (containerWidth - imgWidth * scale) / 2
    const translateY = (containerHeight - imgHeight * scale) / 2

    transform.value = {
      scale,
      translateX,
      translateY,
    }
  }
  catch (err) {
    console.log('图片初始化错误', err)
    imgLoadError.value = true
  }
  finally {
    imgLoaded.value = true
  }
}

/** 初始化canvas */
async function initCanvas() {
  console.log('initCanvas', props.noteData)
  if (imgLoadError.value) return
  await nextTick()
  try {
    canvas = new Canvas('correctionCanvas', {
      width: imgContainer.value!.clientWidth,
      height: imgContainer.value!.clientHeight,
      selection: false, // 禁止框选
    })
    canvas.freeDrawingBrush = new PencilBrush(canvas)
    canvas.freeDrawingBrush.width = PENCIL_WIDTH
    canvas.freeDrawingBrush.color = PENCIL_COLOR

    if (props.noteData) {
    // 加载历史数据
      canvasLoadFormJSON(JSON.parse(props.noteData))
    }

    saveHistory(true)

    // 添加对自由绘制笔迹的处理
    canvas.on('path:created', (e) => {
      if (e.path) {
      // 笔记创建时，将路径设为不可选择
        e.path.selectable = false
        e.path.evented = false
        saveHistory()
      }
    })

    // 监听对象修改事件
    canvas.on('object:modified', () => saveHistory())
  }
  catch (err) {
    console.log('初始化canvas失败', err)
  }
}

/** 工具栏图标 */
function getToolIcon(toolItem) {
  switch (toolItem.type) {
    case 'undo':
      return $g.tool.getFileUrl(canUndo.value ? toolItem.activeIcon : toolItem.icon)
    case 'redo':
      return $g.tool.getFileUrl(canRedo.value ? toolItem.activeIcon : toolItem.icon)
    default:
      return $g.tool.getFileUrl(toolItem.type === curMode.value ? toolItem.activeIcon : toolItem.icon)
  }
}

/** canvas加载JSON数据 */
function canvasLoadFormJSON(JSON) {
  canvas?.loadFromJSON(JSON, () => {
    canvas?.requestRenderAll()
    // 恢复不同类型的对象的属性设置
    setTimeout(() => {
      canvas?.getObjects().forEach(processObject)
    }, 50)
  })
}

/** 撤销处理函数 */
function undo() {
  if (historyIndex.value <= 0) return
  historyIndex.value--
  const prevState = history.value[historyIndex.value]
  canvasLoadFormJSON(prevState)
}
/** 一键清除处理函数 */
function clear() {
  history.value = []
  historyIndex.value = -1
  if (canvas) {
    canvas?.clear()
    saveHistory(true)
  }
  modifiedFlag = false
  emit('clear')
}
// 重做处理函数
function redo() {
  if (historyIndex.value >= history.value.length - 1) return
  historyIndex.value++
  const nextState = history.value[historyIndex.value]
  canvasLoadFormJSON(nextState)
}

/** 工具栏点击处理函数 */
function handleToolClick(type) {
  if (canvas?.getActiveObject()) {
    canvas?.discardActiveObject()
    canvas?.requestRenderAll()
  }

  if (['undo',
'redo',
'clear'].includes(type)) {
    type === 'undo' && undo()
    type === 'redo' && redo()
    type === 'clear' && clear()
    return
  }

  curMode.value = type
  canvas!.isDrawingMode = type === 'draw'
}

/** 保存历史记录 */
function saveHistory(init = false) {
  if (!init)
    modifiedFlag = true

  const json = canvas?.toJSON()

  // 如果在历史中间位置编辑，则删除后续历史
  if (historyIndex.value < history.value.length - 1)
    history.value = history.value.slice(0, historyIndex.value + 1)

  // 添加新的历史记录
  history.value.push(json)

  // 如果历史记录超过最大长度，删除最早的记录
  if (history.value.length > MAX_HISTORY_LENGTH)
    history.value = history.value.slice(-MAX_HISTORY_LENGTH)

  historyIndex.value = history.value.length - 1
}

// 添加符号相关配置
const symbolConfig = ref({
  truth: {
    color: '#00FF00', // 绿色
    path: 'M 0 0 L 10 10 L 25 -5', // 对勾的路径
    scale: $g.isPC ? 3 : 4, // 默认缩放比例
  },
  wrong: {
    color: '#FF0000', // 红色
    path: 'M 0 0 L 20 20 M 0 20 L 20 0', // 叉号的路径
    scale: $g.isPC ? 3 : 4,
  },
})

// 处理滚轮缩放
function handleWheel(e: WheelEvent) {
  e.preventDefault()

  if (!container.value || !imgContainer.value || !imgLoaded.value) return

  // 获取鼠标相对于容器的位置
  const rect = container.value.getBoundingClientRect()
  const mouseX = e.clientX - rect.left
  const mouseY = e.clientY - rect.top

  // 计算鼠标相对于图片的位置（考虑当前变换）
  const imgX = (mouseX - transform.value.translateX) / transform.value.scale
  const imgY = (mouseY - transform.value.translateY) / transform.value.scale

  // 计算新的缩放比例
  const delta = e.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.min(Math.max(transform.value.scale * delta, MIN_SCALE), MAX_SCALE)

  // 计算新的平移位置，保持鼠标位置不变
  const newTranslateX = mouseX - imgX * newScale
  const newTranslateY = mouseY - imgY * newScale

  transform.value = {
    scale: newScale,
    translateX: newTranslateX,
    translateY: newTranslateY,
  }
}
// 记录拖动开始时的位置和平移数据
let startDragData = {
  clientX: 0,
  clientY: 0,
  translateX: 0,
  translateY: 0,
  activeObject: undefined as any,
}
// 处理鼠标按下事件
function handleMouseDown(e: MouseEvent) {
  // 如果图片未加载完成或当前有选中对象，则不处理
  if (!imgLoaded.value || !container.value || !imgContainer.value) return

  startDragData = {
    clientX: e.clientX,
    clientY: e.clientY,
    translateX: transform.value.translateX,
    translateY: transform.value.translateY,
    activeObject: canvas?.getActiveObject() || undefined,
  }
  container.value?.addEventListener('mousemove', handleMouseMove)
  container.value?.addEventListener('mouseup', handleMouseUp)
  container.value?.addEventListener('mouseleave', handleMouseUp)
}
// 处理鼠标移动事件
function handleMouseMove(e: MouseEvent) {
  if (canvas?.getActiveObject() || curMode.value === 'draw') return
  const deltaX = e.clientX - startDragData.clientX
  const deltaY = e.clientY - startDragData.clientY

  transform.value = {
    ...transform.value,
    translateX: startDragData.translateX + deltaX,
    translateY: startDragData.translateY + deltaY,
  }
}
// 处理鼠标抬起事件
function handleMouseUp(e: MouseEvent) {
  container.value?.removeEventListener('mousemove', handleMouseMove)
  container.value?.removeEventListener('mouseup', handleMouseUp)
  container.value?.removeEventListener('mouseleave', handleMouseUp)

  const isClick = startDragData.clientX === e.clientX && startDragData.clientY === e.clientY
  if (!isClick) return

  let endActiveObject = canvas?.getActiveObject()

  // 如果点击前后都没有激活的对象，则新增对象
  if (!startDragData.activeObject && !endActiveObject) {
    if (curMode.value === 'truth')
      addSymbol(e.clientX, e.clientY, 'truth')

    else if (curMode.value === 'wrong')
      addSymbol(e.clientX, e.clientY, 'wrong')

    else if (curMode.value === 'text')
      addText(e.clientX, e.clientY)
  }

  // 如果点击前后都有，则点击了对象
  if (startDragData.activeObject && startDragData.activeObject === endActiveObject && e.target === canvas?.getTopContext()?.canvas) {
    if (endActiveObject instanceof Textbox)
      handleEditTextBox(endActiveObject)
  }
}

// 记录触摸开始时的数据
let touchStartData = {
  touches: [] as Touch[],
  translateX: 0,
  translateY: 0,
  scale: 1,
  activeObject: undefined as any,
}

// 处理触摸开始事件
function handleTouchStart(e: TouchEvent) {
  if ($g.isPC || !imgLoaded.value || !container.value || !imgContainer.value) return

  // 记录触摸点信息
  touchStartData = {
    touches: Array.from(e.touches),
    translateX: transform.value.translateX,
    translateY: transform.value.translateY,
    scale: transform.value.scale,
    activeObject: canvas?.getActiveObject(),
  }

  container.value?.addEventListener('touchmove', handleTouchMove)
  container.value?.addEventListener('touchend', handleTouchEnd)
  container.value?.addEventListener('touchcancel', handleTouchEnd)
}

// 处理触摸移动事件
function handleTouchMove(e: TouchEvent) {
  if (canvas?.getActiveObject() || curMode.value === 'draw') return

  if (e.touches.length === 1) {
    // 单指移动
    const touch = e.touches[0]
    const startTouch = touchStartData.touches[0]

    const deltaX = touch.clientX - startTouch.clientX
    const deltaY = touch.clientY - startTouch.clientY

    transform.value = {
      ...transform.value,
      translateX: touchStartData.translateX + deltaX,
      translateY: touchStartData.translateY + deltaY,
    }
  }
  else if (e.touches.length === 2) {
    // 双指缩放
    const touch1 = e.touches[0]
    const touch2 = e.touches[1]
    const startTouch1 = touchStartData.touches[0]
    const startTouch2 = touchStartData.touches[1]

    // 计算当前和起始时的触摸点距离
    const currentDistance = Math.hypot(
      touch2.clientX - touch1.clientX,
      touch2.clientY - touch1.clientY,
    )
    const startDistance = Math.hypot(
      startTouch2.clientX - startTouch1.clientX,
      startTouch2.clientY - startTouch1.clientY,
    )
    // 计算缩放比例
    const scale = touchStartData.scale * (currentDistance / startDistance)
    const newScale = Math.min(Math.max(scale, MIN_SCALE), MAX_SCALE)

    // 计算当前和起始时的中心点
    const currentCenterX = (touch1.clientX + touch2.clientX) / 2
    const currentCenterY = (touch1.clientY + touch2.clientY) / 2
    const startCenterX = (startTouch1.clientX + startTouch2.clientX) / 2
    const startCenterY = (startTouch1.clientY + startTouch2.clientY) / 2

    // 计算中心点移动距离
    const centerDeltaX = currentCenterX - startCenterX
    const centerDeltaY = currentCenterY - startCenterY

    // 获取容器位置
    const rect = container.value!.getBoundingClientRect()
    const centerImgX = (startCenterX - rect.left - touchStartData.translateX) / touchStartData.scale
    const centerImgY = (startCenterY - rect.top - touchStartData.translateY) / touchStartData.scale

    // 计算新的平移位置，同时考虑缩放和中心点移动
    const newTranslateX = startCenterX - rect.left - centerImgX * newScale + centerDeltaX
    const newTranslateY = startCenterY - rect.top - centerImgY * newScale + centerDeltaY

    transform.value = {
      scale: newScale,
      translateX: newTranslateX,
      translateY: newTranslateY,
    }
  }
}

// 处理触摸结束事件
async function handleTouchEnd(e: TouchEvent) {
  container.value?.removeEventListener('touchmove', handleTouchMove)
  container.value?.removeEventListener('touchend', handleTouchEnd)
  container.value?.removeEventListener('touchcancel', handleTouchEnd)

  // 图片容器是否是固定位置
  let picIsFixed = transform.value.translateX === touchStartData.translateX &&
    transform.value.translateY === touchStartData.translateY &&
    transform.value.scale === touchStartData.scale

  let startClientX = touchStartData.touches[0].clientX
  let startClientY = touchStartData.touches[0].clientY

  let endClientX = e.changedTouches[0].clientX
  let endClientY = e.changedTouches[0].clientY

  // 手指是否是固定位置
  let fingerIsFixed = endClientX === startClientX && endClientY === startClientY

  // 通过对比触摸开始和结束的位置变化，判断是否是点击操作
  if (!picIsFixed || !fingerIsFixed) return
  await new Promise(resolve => setTimeout(resolve))

  let endActiveObject = canvas?.getActiveObject()
  // 如果点击前后都没有激活的对象，则新增对象
  if (!touchStartData.activeObject && !endActiveObject) {
    if (curMode.value === 'truth')
      addSymbol(startClientX, startClientY, 'truth')

    else if (curMode.value === 'wrong')
      addSymbol(startClientX, startClientY, 'wrong')

    else if (curMode.value === 'text')
      addText(startClientX, startClientY)
  }

  // 如果前后都有且为同一个对象，则是点击了某一个对象
  if (touchStartData.activeObject && touchStartData.activeObject === endActiveObject && e.target === canvas?.getTopContext()?.canvas) {
    if (endActiveObject instanceof Textbox)
      handleEditTextBox(endActiveObject)
  }
}

// 修改 addText 函数
function addText(clientX, clientY) {
  if (!canvas || !container.value) return

  // 获取点击位置
  const rect = container.value.getBoundingClientRect()
  let pointer = {
    x: clientX - rect.left,
    y: clientY - rect.top,
  }

  // 计算相对于图片的位置
  const left = (pointer.x - transform.value.translateX) / transform.value.scale
  const top = (pointer.y - transform.value.translateY) / transform.value.scale
  if (left < 0 || top < 0) return
  // 创建 Textbox 对象
  const textbox = new Textbox('点击编辑文字', {
    left,
    top,
    width: 200, // 设置文本框宽度
    fontSize: 20,
    fill: '#ff0000',
    fontFamily: 'Arial',
    selectable: true,
    hasControls: true,
    hasBorders: true,
    originX: 'center',
    originY: 'center',
    editingBorderColor: '#00ff00', // 编辑时的边框颜色
    cursorColor: '#000000', // 光标颜色
    cursorWidth: 2, // 光标宽度
    textAlign: 'left', // 文字对齐方式
    breakWords: true, // 允许单词换行
    lockUniScaling: true, // 锁定宽高比
    splitByGrapheme: true, // 允许中文换行
  })

  processObject(textbox)
  // 添加对象到画布
  canvas.add(textbox)
  canvas.setActiveObject(textbox)

  saveHistory()
}

// 添加符号函数
function addSymbol(clientX, clientY, type: 'truth' | 'wrong') {
  if (!canvas || !container.value) return

  // 获取点击位置
  const rect = container.value.getBoundingClientRect()
  let pointer = {
    x: clientX - rect.left,
    y: clientY - rect.top,
  }

  // 计算相对于图片的位置
  const left = (pointer.x - transform.value.translateX) / transform.value.scale
  const top = (pointer.y - transform.value.translateY) / transform.value.scale

  if (left < 0 || top < 0) return

  // 创建路径对象
  const path = new Path(symbolConfig.value[type].path, {
    left,
    top,
    stroke: symbolConfig.value[type].color,
    strokeWidth: 2,
    fill: 'transparent',
    scaleX: symbolConfig.value[type].scale,
    scaleY: symbolConfig.value[type].scale,
    originX: 'center',
    originY: 'center',
  })

  processObject(path)
  // 添加对象到画布
  canvas.add(path)
  canvas.setActiveObject(path)
  saveHistory()
}

// 处理添加的对象的属性
function processObject(object: any) {
  // 先处理自由绘制的线条
  if (object instanceof Path && object.stroke === PENCIL_COLOR && object.strokeWidth === PENCIL_WIDTH) {
    object.selectable = false
    object.evented = false
    return
  }

  if (object instanceof Path) {
    // 创建的形状
    object.lockScalingFlip = true
    object.setControlsVisibility({
      mt: false, // 隐藏顶部中间控制点
      mb: false, // 隐藏底部中间控制点
      ml: false, // 隐藏左侧中间控制点
      mr: false, // 隐藏右侧中间控制点
      mtr: false, // 隐藏旋转控制点
      tr: false, // 隐藏右上角控制点
    })
  }
  // 文字输入控件
  else if (object instanceof Textbox) {
    object.padding = 10
    object.editable = false

    // 设置控制点显示
    object.setControlsVisibility({
      mt: false, // 隐藏顶部中间控制点
      mb: false, // 隐藏底部中间控制点
      mtr: false, // 隐藏旋转控制点
      tr: false, // 隐藏右上角控制点
    })
  }

  // 给控件添加删除图标
  object.controls.deleteControl = new Control({
    x: 0.5,
    y: -0.5,
    cursorStyle: 'pointer',
    mouseUpHandler: (_eventData, transform) => {
      transform.target.canvas?.remove(transform.target)
      saveHistory()
    },
    render: (ctx, left, top, _styleOverride, fabricObject) => {
      const size = 30
      ctx.save()
      ctx.translate(left, top)
      ctx.rotate(util.degreesToRadians(fabricObject.angle))
      ctx.drawImage(deleteImg, -size / 2, -size / 2, size, size)
      ctx.restore()
    },
  })

  // 设置控制器样式
  object.borderScaleFactor = 3
  object.borderColor = '#6474FD'
  object.cornerColor = '#6474FD'
  object.cornerSize = $g.isPC ? 20 : 25

  return object
}

/** 编辑textBox文字处理函数 */
function handleEditTextBox(object: Textbox) {
  activeTextBox = object
  let text = object.get('text')
  editText.value = text === '点击编辑文字' ? '' : text
  showEditDialog = true
}

/** 编辑完成的处理函数 */
function handleEditConfirm() {
  activeTextBox.set('text', editText.value ? editText.value : '点击编辑文字')
  saveHistory()
  canvas?.requestRenderAll()
}

async function exportImage() {
  if (!canvas || !imgContainer.value || !modifiedFlag) return ''
  if (isExporting.value)
    throw new Error('图片导出中')

  try {
    isExporting.value = true
    // 创建一个隐藏的 canvas
    const exportCanvas = document.createElement('canvas')
    exportCanvas.width = imgContainer.value.clientWidth
    exportCanvas.height = imgContainer.value.clientHeight
    const ctx = exportCanvas.getContext('2d')
    if (!ctx) return

    // 设置图像的 crossOrigin 属性
    const img = new Image()
    img.crossOrigin = 'anonymous' // 允许跨域
    img.src = `${props.imgSrc}?${Date.now()}`

    await new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = reject
    })
    ctx.drawImage(img, 0, 0, exportCanvas.width, exportCanvas.height)

    // 绘制当前 canvas 的内容
    const canvasDataUrl = canvas.toDataURL()
    const overlayImg = new Image()
    overlayImg.src = canvasDataUrl
    await new Promise((resolve, reject) => {
      overlayImg.onload = resolve
      overlayImg.onerror = reject
    })
    ctx.drawImage(overlayImg, 0, 0, exportCanvas.width, exportCanvas.height)

    let blob: Blob = await new Promise((resolve) => {
      exportCanvas.toBlob((blob) => {
        // 清理
        exportCanvas.remove()
        resolve(blob!)
      }, 'image/jpeg', 1)
    })
    let aliOss = new OSS()
    let data = {
      file: blob,
      name: 'image.png',
      size: blob.size,
    }
    let res = await aliOss.uploadFile(data)
    return res.fullUrl || ''
  }
  catch (err) {
    console.log('导出失败', err)
    $g.msg('保存图片失败')
  }
  finally {
    isExporting.value = false
  }
}
function exportJsonString() {
  if (!canvas || !modifiedFlag) return null
  return JSON.stringify(canvas.toJSON())
}
function isModified() {
  return modifiedFlag
}

defineExpose({
  exportJsonString,
  exportImage,
  isModified,
})
</script>

<template>
  <!-- 工具栏 -->
  <div class="flex items-center justify-end mb-16px" :class="imgLoadError && 'pointer-events-none'">
    <img
      v-for="item in toolList"
      :key="item.name"
      :src="getToolIcon(item)"
      :title="item.name"
      class="w-23px ml-13px cursor-pointer"
      @click="handleToolClick(item.type)"
    />
  </div>

  <div
    ref="container"
    v-loading="!imgLoaded || isExporting"
    :element-loading-text="isExporting ? '数据保存中...' : '加载中...'"
    class="flex-1 rounded-[6px] relative overflow-hidden"
    @wheel="handleWheel"
    @mousedown="handleMouseDown"
    @touchstart="handleTouchStart"
  >
    <span v-if="imgLoadError" class="absolute top-0 left-0 w-full h-full flex items-center justify-center text-[#999999]">
      作答图片加载失败
    </span>
    <div
      ref="imgContainer"
      :class="(!imgLoaded || imgLoadError) && 'opacity-0' "
      class="img-container w-max h-max absolute origin-top-left touch-none"
      :style="{ transform: `translate(${transform.translateX}px, ${transform.translateY}px) scale(${transform.scale})` }"
      @contextmenu.prevent
    >
      <img
        :key="imgSrc"
        :src="imgSrc"
        class="w-auto h-auto select-none shadow-[0_0_5px_0px_rgba(0,0,0,0.5)]"
      />
      <div class="w-full h-full absolute top-0 left-0 ">
        <canvas id="correctionCanvas" />
      </div>
    </div>
  </div>

  <!-- 编辑文字弹窗 -->
  <TextBoxEditDialog v-model="showEditDialog"
                     v-model:text="editText"
                     @confirm="handleEditConfirm"
  />
</template>

<style lang="scss" scoped>

</style>
