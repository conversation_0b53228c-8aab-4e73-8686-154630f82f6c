<script setup lang="ts" name="PaperDetail">
import { getFilesApi, getListApi, getWordStateApi } from '@/api/ccyReport'

const router = useRouter()
const route = useRoute()
let downLoading = $ref<any>(false)
let timer = $ref<any>(null)
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      label: '学生姓名',
      prop: 'userName',
    },
    {
      label: '测验模块',
      prop: 'activityThemeNameList',
      slot: true,
    },
    {
      label: '题目数',
      prop: 'questionNum',
    },
    {
      label: '操作',
      prop: 'cz',
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})
async function getList() {
  try {
    tableOptions.loading = true
    const res = await getListApi({
      studentTrainPeriodTestId: route.query.studentTrainPeriodTestId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.pageSize,
    })
    tableOptions.loading = false
    tableOptions.data = res?.list || []
    tableOptions.pageOptions.total = res?.total || 0
  }
  catch (err) {
    tableOptions.loading = false
  }
}
function toDetail(item) {
  if (item?.questionState != 3)
    return

  router.push({
    name: 'ExamQViewEdit',
    query: {
      studentId: item?.studentTrainPeriodTestSchoolStudentId,
    },
  })
}
async function getFiles() {
  try {
    const files = await getFilesApi({
      studentTrainPeriodTestId: route.query.studentTrainPeriodTestId,
    })

    const fileList = files.map((it) => {
      const fileNameParts = [
        it?.studentName,
        it?.idNum,
        it?.schoolName,
        it?.gradeName && it?.className ? `${it.gradeName}${it.className}` : '',
        it?.exerciseTaskTypeName ? `【${it.exerciseTaskTypeName}】` : '',
        '试卷',
      ].filter(Boolean)

      return {
        name: `${fileNameParts.join('-')}.docx`,
        url: it?.wordUrl,
      }
    })

    const zipName = files?.[0]?.exerciseTaskName || '试卷压缩包'
    await $g.tool.downloadAndZipFiles(fileList, zipName, (loading) => {
      downLoading = loading
    })
  }
  catch (err) {
    downLoading = false
  }
}
async function downLoad() {
  try {
    if (downLoading)
      return

    downLoading = true
    const res: any = await getWordStateApi({
      studentTrainPeriodTestId: route.query.studentTrainPeriodTestId,
    })
    if (res?.taskEnd == 2) {
      if (res?.success > 0)
        getFiles()

      else
        downLoading = false
    }
    else {
      timer = setInterval(async () => {
        const res: any = await getWordStateApi({
          studentTrainPeriodTestId: route.query.studentTrainPeriodTestId,
        })
        if (res?.taskEnd == 2) {
          if (res?.success > 0)
            getFiles()

          else
            downLoading = false

          clearInterval(timer)
        }
      }, 3000)
    }
  }
  catch (err) {
    downLoading = false
    clearInterval(timer)
  }
}
onMounted(() => {
  getList()
})
onBeforeUnmount(() => {
  if (timer)
    clearInterval(timer)
})
</script>

<template>
  <div class="px-26px pt-26px">
    <g-navbar title="返回" class="mb-17px">
      <template #right>
        <div class="bg-[#fff] cursor-pointer flex items-center text-[13px] br-[20px] text-theme-primary px-[13px] py-[8px] border border-theme-primary" @click="downLoad">
          <img src="@/assets/img/report/download.png" class="w-[17px] h-[17px] mr-[4px]" />{{ downLoading ? '下载中...' : '下载学生试卷' }}
        </div>
      </template>
    </g-navbar>
    <div
      class="bg-white p-[17px] rounded-[6px]"
      :style="{
        height: 'calc(100vh - 106px )',
      }"
    >
      <g-table
        :table-options="tableOptions"
        :border="false"
        :stripe="true"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        @change-page="getList"
      >
        <template #cz="{ row }">
          <div :class="{ 'text-theme-primary van-haptics-feedback': row?.questionState == 3 }" @click="toDetail(row)">
            查看试卷
          </div>
        </template>
        <template #activityThemeNameList="{ row }">
          <div v-if="row?.activityThemeNameList?.length">
            <span v-for="(item, index) in row?.activityThemeNameList" :key="index">{{ item }}<span v-if="index < row.activityThemeNameList?.length - 1">、</span></span>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </g-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
