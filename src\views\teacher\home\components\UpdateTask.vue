<script setup lang="ts">
import { taskDetail, updateTask } from '@/api/taskCenter'
import CustomSelectionDialog from '@/views/teacher/task/createTask/components/CustomSelectionDialog.vue'
import { deduplicateByKey, studentListInit } from '@/views/teacher/task/createTask/tool'
import DateChoose from './DateChoose.vue'

const props = defineProps({
  taskId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['updateList'])

const showDialog = defineModel<boolean>('show')
let taskData: any = $ref({})
let radio1 = $ref('0')
let radio2 = $ref('1')
let time1 = $ref([])
let time2 = $ref([])
let isHidden = $ref(true)
let submitLoading = $ref(false)
let studentDialog = $ref(false)
let studentData: any = ref({})
let supplementaryStudent: number = $ref(0) // 补录学生人数
let studentTotalText = $ref('')
let studentTotal = $ref(0)

provide('studentData', studentData)

// 合并每个班选择的学生（包括组中的学生）
function classStudentList() {
  if (!studentData.value.classList.length)
    return []

  let deepCloneArr = JSON.parse(JSON.stringify(studentData.value.classList))
  // 合并并去重选择的学生
  deepCloneArr.forEach((item) => {
    item.combineStudent = deduplicateByKey([...(item.selectStudentArr || []), ...(item.selectGroupArr?.flatMap(v => v.list).filter(v => (v.studentJoinSchoolClassIdList)?.includes(item.schoolClassId)) || [])], 'schoolStudentId')
  })
  return deepCloneArr
}

// 班级学生显示文本
function classStudent() {
  studentTotal = studentData.value.selectStudentList.length
  let str = ''
  const classList = classStudentList()
  classList.forEach((item, index) => {
    str += `${index == 0 ? '' : '、'}${item?.sysGradeName || ''}${item?.className || ''}${item?.combineStudent?.length}人`
  })
  supplementaryStudent = studentTotal - taskData.studentNum
  studentTotalText = str
}

// 获取详情
async function getTaskDetail() {
  try {
    const res = await taskDetail({ taskId: props.taskId })
    taskData = res
    radio1 = taskData.state == 0 ? '1' : '0'
    time1 = res.releaseTime.split(' ')
    radio2 = res.requireCompleteTime ? '1' : '0'
    time2 = res.requireCompleteTime ? res.requireCompleteTime.split(' ') : []
    studentData.value = studentListInit(taskData)
    classStudent()
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
}

// 处理班级数据
function processingClassList() {
  let classList: any = JSON.parse(JSON.stringify(studentData.value.classList))
  let groups = deduplicateByKey(classList.flatMap(item => item.selectGroupArr).filter(Boolean), 'schoolClassId')
  classList = [...classList.filter(v => v.selectStudentArr?.length),
...groups.map((h) => {
    return {
      ...h,
      arrangeObjectType: 2,
      selectStudentArr: h.list,
    }
  })]
  return classList.map((v) => {
    return {
      schoolClassId: v.schoolClassId,
      arrangeObjectType: v.arrangeObjectType,
      schoolStudentIds: v.selectStudentArr.map(h => h.schoolStudentId),
    }
  })
}

// 修改任务
async function sendUpdate() {
  try {
    if (!taskData.taskName) {
      $g.showToast('任务名称不能为空！')
      return
    }
    submitLoading = true
    const classList = processingClassList()
    const hasTime = time2.every(v => v)
    if (radio2 == '1' && !hasTime) {
      $g.showToast('请选择要求完成时间！')
      return
    }
    await updateTask({
      taskId: props.taskId,
      taskName: taskData.taskName,
      requireCompleteTime: radio2 == '0' ? null : time2.join(' '),
      teacherMessage: taskData.teacherMessage,
      classList,
    })
    emit('updateList')
    showDialog.value = false
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
  finally {
    submitLoading = false
  }
}
</script>

<template>
  <el-dialog
    v-model="showDialog"
    top="2vh"
    class="w-[680px] !br-[10px]"
    :close-on-click-modal="false"
    :show-close="false"
    :z-index="1000"
    @opened="getTaskDetail"
  >
    <template #header>
      <div class="flex justify-between items-center">
        <span class="font-600 text-17px mr-7px">修改任务</span>
        <img
          :src="$g.tool.getFileUrl('taskCenter/close.png')"
          alt=""
          class="h-15px w-15px van-haptics-feedback"
          @click="showDialog = false"
        >
      </div>
    </template>
    <div class=" overflow-auto pb-10px" :class="$g.isFlutter ? 'max-h-[74vh]' : 'max-h-[80vh]'">
      <div class="px-10px">
        <div class="bg-[#ffedd8] border border-[#f1dbbf] p-10px br-[8px] flex items-center">
          <div class="text-[white] w-20px h-20px text-center leading-[20px] bg-[#ff771b] br-[50%] mr-10px">
            !
          </div>
          <span class="text-16px">任务修改将同步更新到学生端</span>
        </div>
        <div class="text-16px mb-10px mt-16px">
          <span class="text-[red]">*</span>任务名称：
        </div>
        <el-input
          v-model="taskData.taskName"
          maxlength="30"
          placeholder="请输入任务名称"
          show-word-limit
          type="text"
        >
        </el-input>
        <div class="text-16px mb-10px mt-16px">
          任务学生：
        </div>
        <div class="text-[#999] flex items-center">
          <div class="flex items-center text-13px">
            <span class="text-16px">共{{ studentTotal }}人</span>(<div class="max-w-[360px] line-1">
              {{ studentTotalText }}
            </div>)
          </div>
          <el-button class="h-29px ml-6px flex-shrink-0"
                     type="primary"
                     @click="studentDialog = true"
          >
            <span v-if="supplementaryStudent > 0">查看补录学生（{{ supplementaryStudent }}人）</span>
            <span v-else>+ 补录学生</span>
          </el-button>
        </div>
        <div class="text-16px mb-10px mt-16px">
          发布时间：
        </div>
        <DateChoose
          v-model:radio-value="radio1"
          v-model:time="time1"
          :is-disabled="true"
          :unlimited="false"
        />
        <div class="text-16px mb-16px mt-16px">
          要求完成时间：
        </div>
        <DateChoose
          v-model:radio-value="radio2"
          v-model:time="time2"
          unlimited
          :recommendations="taskData?.releaseTime"
        />
        <!-- <div class="flex w-fit items-center text-16px mt-20px mb-10px cursor-pointer text-[#6474FD]" @click="isHidden = !isHidden">
          更多配置
          <img
            src="@/assets/img/taskCenter/moreOption.png"
            class="w-14px h-14px ml-4px"
            :class="!isHidden ? 'rotate-180' : ''"
            alt="more"
          >
        </div>
        <div v-show="!isHidden"> -->
        <div v-if="taskData.taskType == 3" class="flex items-center mt-16px">
          <div class="flex items-center mr-10px">
            <span class="text-[#a8abb1]">视频进度条：</span>
            <el-popover
              placement="top"
              :width="280"
              :trigger="$g.isFlutter ? 'click' : 'hover'"
              effect="dark"
            >
              <p>可拖动：观看视频时可任意拖动进度条</p>
              <p>首次观看不可拖动：第一次观看某个视频时，无法拖动进度条，当这个视频看完后，再次看这个视频可任意拖动进度条</p>
              <template #reference>
                <img :src="$g.tool.getFileUrl('taskCenter/tip.png')" class="h-12px w-12px cursor-pointer">
              </template>
            </el-popover>
          </div>
          <el-radio-group v-model="taskData.configVideoProgressBar" disabled>
            <el-radio :value="1">
              可拖动
            </el-radio>
            <el-radio :value="2">
              首次观看不可拖动
            </el-radio>
          </el-radio-group>
        </div>
        <template v-else>
          <div class="flex items-center mt-16px">
            <div class="flex items-center w-125px mr-12px">
              <span class="text-[#a8abb1]">答案公布时间：</span>
              <el-popover
                placement="top"
                :width="280"
                :trigger="$g.isFlutter ? 'click' : 'hover'"
                effect="dark"
              >
                <p>学生交卷后：每个学生交卷后可立即看到答案解析及批改结果</p>
                <p>所有学生交卷后：所有学生交卷，或到达要求完成时间，已交卷的学生可立即看到答案解析及批改结果</p>
                <template #reference>
                  <img :src="$g.tool.getFileUrl('taskCenter/tip.png')" class="h-12px w-12px cursor-pointer">
                </template>
              </el-popover>
            </div>
            <el-radio-group v-model="taskData.configAnswerPublishTime" disabled>
              <el-radio :value="1">
                学生交卷后
              </el-radio>
              <el-radio :value="2">
                所有学生交卷后<span class="text-12px">(若已到完成时间自动公布)</span>
              </el-radio>
            </el-radio-group>
          </div>
          <div class="flex items-center">
            <div class="flex items-center w-125px mr-12px">
              <span class="text-[#a8abb1]">批改设置：</span>
              <el-popover
                placement="top"
                :width="280"
                :trigger="$g.isFlutter ? 'click' : 'hover'"
                effect="dark"
              >
                <p>教师批改-教师批改(纠正)学生自查后的主观题对错</p>
                <p>学生自查-公布答案后学生自评主观题的对错</p>
                <template #reference>
                  <img :src="$g.tool.getFileUrl('taskCenter/tip.png')" class="h-12px w-12px cursor-pointer">
                </template>
              </el-popover>
            </div>
            <el-radio-group v-model="taskData.configCorrect" disabled>
              <el-radio :value="1">
                教师批改
              </el-radio>
              <el-radio :value="2">
                学生自查
              </el-radio>
            </el-radio-group>
          </div>
          <div class="flex items-center">
            <div class="flex items-center w-125px mr-12px">
              <span class="text-[#a8abb1]">已到完成时间后：</span>
              <el-popover
                placement="top"
                :width="280"
                :trigger="$g.isFlutter ? 'click' : 'hover'"
                effect="dark"
              >
                <p>允许作答：未交卷的学生可以继续作答</p>
                <p>不允许作答：到达完成时间后，不论学生是否完成都自动提交试卷，未作答的题目视为“我不会”</p>
                <template #reference>
                  <img :src="$g.tool.getFileUrl('taskCenter/tip.png')" class="h-12px w-12px cursor-pointer">
                </template>
              </el-popover>
            </div>
            <el-radio-group v-model="taskData.configReachCompleteTime" disabled>
              <el-radio :value="1">
                允许作答
              </el-radio>
              <el-radio :value="2">
                不允许作答<span class="text-12px">(到完成时间自动交卷)</span>
              </el-radio>
            </el-radio-group>
          </div>
        </template>
        <!-- </div> -->
        <div class="text-16px mb-10px mt-16px">
          老师留言：
        </div>
        <el-input
          v-model="taskData.teacherMessage"
          resize="none"
          :rows="4"
          maxlength="100"
          class="w-[80%]"
          show-word-limit
          type="textarea"
          placeholder="请输入留言内容（选填）"
        />
      </div>
      <template v-if="$g.tool.isTrue(taskData)">
        <CustomSelectionDialog
          v-model:show="studentDialog"
          title="选择补录学生"
          :class-type="taskData.classType"
          :subject-id="taskData.sysSubjectId"
          @confirm="classStudent"
        />
      </template>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button class="h-29px" @click="showDialog = false">
          取消
        </el-button>
        <el-button
          class="h-29px"
          type="primary"
          :loading="submitLoading"
          @click="sendUpdate"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
