<script setup lang="ts">
import {
  getGroupList,
  getStudentList,
  getTeacherClassList,
} from '@/api/taskCenter'
import { combineStudentAndGroup, deduplicateByKey } from '../tool'

const props = defineProps({
  classType: {
    type: [String, Number],
    default: 1,
  },
  title: {
    type: String,
    default: '自定义选择',
  },
  subjectId: {
    type: [String, Number],
  },
})
const emit = defineEmits(['confirm'])
const showDialog = defineModel<boolean>('show')
const tab1Ref = $ref<any>(null)
const tab2Ref = $ref<any>(null)
const underlineWidth = ref(0)
const underlineLeft = ref(0)
const route = useRoute()
const studentData = inject<Ref<any>>('studentData', ref({}))
const curClass = $computed(
  () => classList.find(h => h.schoolClassId == curClassId) || {
    selectGroupArr: [],
    selectStudentArr: [],
  },
)
const underlineStyle = computed(() => {
  return {
    width: `${underlineWidth.value}px`,
    transform: `translateX(${underlineLeft.value}px)`,
  }
})

let classList = $ref<any>([])
let curClassId = $ref<any>()
let activeTab = $ref(0)
let keyword = $ref('')
let studentList = $ref<any>([])
let allStudentList = $ref<any>([]) // 搜索会返回所有的班级学生
let groupList = $ref<any>([])
let selectStudent = $ref<any>([]) // 所有学生的选中id
let selectGroup = $ref<any>([]) // 所有组的选中id数组
let checkedAll = $ref(false)
let indeterminate = $ref(false)
let clickedId = $ref<any>()
let loading = $ref(true)

// 根据班级类型重新获取班级列表
watch(
  () => props.classType,
  async (val) => {
    if (!route.query.subjectId && !props.subjectId) return
    try {
      loading = true
      let res = await getTeacherClassList({
        classType: val,
        sysSubjectId: route.query.subjectId || props.subjectId,
      })
      classList =
      res?.map((v) => {
        return {
          ...v,
          selectStudentArr: [], // 当前班级选中的学生数组
          selectGroupArr: [], // 当前班级选中的组数组
        }
      }) || []
      curClassId = classList[0]?.schoolClassId
      loading = false
    }
    catch (err) {
      loading = false
    }
    if (curClassId)
      await Promise.all([getStudent(), getGroup()])

    loading = false
  },
  {
    immediate: true,
  },
)

watch(
  showDialog,
  (val) => {
    if (val) {
      initDialog()
      nextTick(() => {
        activeTab = 0
        let studentContainer = document.getElementById('studentContainer')
        if (studentContainer)
          studentContainer.scrollTop = 0

        updateUnderline(tab1Ref)
      })
    }
    else {
      loading = true
    }
  },
  {
    immediate: true,
  },
)

async function switchTab(tabValue, tabRef) {
  activeTab = tabValue
  clickedId = null
  getAllStatus()
  await nextTick()
  updateUnderline(tabRef)
}

function updateUnderline(tab) {
  underlineWidth.value = tab.offsetWidth
  underlineLeft.value = tab.offsetLeft
}

async function initDialog() {
  if (!classList[0]?.schoolClassId) return
  changeClass(classList[0]?.schoolClassId)
  classList.forEach((h) => {
    let find = studentData.value.classList.find(
      v => v.schoolClassId == h.schoolClassId,
    )
    if (find) {
      // h.selectStudentArr = [...find.selectStudentArr]
      // h.selectGroupArr = [...find.selectGroupArr]
      Object.assign(h, find)
    }
    else {
      h.selectStudentArr = []
      h.selectGroupArr = []
    }
  })
  selectStudent = classList
    .flatMap(item => item.selectStudentArr)
    .map(item => item.schoolStudentId)
  selectGroup = [
    ...new Set(
      classList
        .flatMap(item => item.selectGroupArr)
        .map(item => item.schoolClassId),
    ),
  ]
  getAllStatus()
}

function dealName(name) {
  if (name.length > 10)
    return `${name.substring(0, 10)}...`

  return name
}

async function getStudent() {
  if (!curClassId || (!route.query.subjectId && !props.subjectId)) return
  let res = await getStudentList({
    schoolClassId: curClassId,
    keyword,
    sysSubjectId: route.query.subjectId || props.subjectId,
  })
  allStudentList = res // 搜索情况下，返回的是所有班级的学生，需要进行过滤
  studentList = (res || []).filter(h => h.schoolClassId == curClassId)
}

async function getGroup() {
  if (!curClassId || (!route.query.subjectId && !props.subjectId)) return
  let res = await getGroupList({
    sysSubjectId: route.query.subjectId || props.subjectId,
    belongsSchoolClassId: curClassId,
  })
  // 要过滤掉小组没人的
  groupList =
    res
      .filter(v => v.list.length)
      .map((h) => {
        return {
          ...h,
          belongsSchoolClassIdList: h.belongsSchoolClassList,
          list: h.list.map((v) => {
            return {
              ...v,
            }
          }),
        }
      }) || []
}
async function changeClass(schoolClassId) {
  loading = true
  curClassId = schoolClassId
  clickedId = null
  let studentContainer = document.getElementById('studentContainer')
  let groupContainer = document.getElementById('groupContainer')
  if (studentContainer)
    studentContainer.scrollTop = 0

  if (groupContainer)
    groupContainer.scrollTop = 0

  await Promise.all([getStudent(), getGroup()])
  getAllStatus()
  loading = false
}

// 全选状态，会根据组，学生tab和班级进行改变
function getAllStatus() {
  if (activeTab == 0) {
    indeterminate =
      curClass.selectStudentArr.length &&
      curClass.selectStudentArr.length < studentList.length
    checkedAll =
      curClass.selectStudentArr.length &&
      curClass.selectStudentArr.length === studentList.length
    curClass.arrangeObjectType = curClass.classStudentNum == curClass.selectStudentArr.length ? 1 : 2
    return
  }
  indeterminate =
    curClass.selectGroupArr.length &&
    curClass.selectGroupArr.length < groupList.length
  checkedAll =
    curClass.selectGroupArr.length &&
    curClass.selectGroupArr.length === groupList.length
}

function handleChangeStudent() {
  curClass.selectStudentArr = [...curClass.selectStudentArr, ...studentList,
  ].filter(v => selectStudent.includes(v.schoolStudentId))
  curClass.selectStudentArr = deduplicateByKey(curClass.selectStudentArr, 'schoolStudentId')
  getAllStatus()
}

function handleChangeGroup() {
  let selectGroupObjArr = groupList.filter(v =>
    selectGroup.includes(v.schoolClassId))

  classList.forEach((h) => {
    let arr = selectGroupObjArr.filter(item =>
      item.belongsSchoolClassIdList.find(v => v.schoolClassId == h.schoolClassId))
    h.selectGroupArr = [...h.selectGroupArr, ...arr]
    h.selectGroupArr = h.selectGroupArr.filter(v => selectGroup.includes(v.schoolClassId))
    h.selectGroupArr = deduplicateByKey(h.selectGroupArr, 'schoolClassId')
  })
  getAllStatus()
}

// 去重班级和组的总和
function reDuplicate(item) {
  return [
    ...new Set([
      ...item.selectStudentArr.map(h => h.schoolStudentId),
      ...item.selectGroupArr
        .flatMap(v => v.list)
        .filter(k =>
          k.studentJoinSchoolClassIdList
            ?.includes(item.schoolClassId))
        .map(h => h.schoolStudentId),
    ]),
  ].length
}

// 搜索
const handleKeywordInput = $g._.debounce(async () => {
  await getStudent()
  if (keyword) {
    // 搜索为空
    if (!allStudentList.length)
      return

    let classIds = [
      ...new Set(allStudentList.map(item => item.schoolClassId)),
    ]
    classList.forEach((h) => {
      if (classIds.includes(h.schoolClassId)) {
        h.notShow = false
      }
      else {
        h.notShow = true

        // 以下为特殊情况，当前选中班级没有搜索的学生而班级被隐藏了，切换到第一个有学生的班级
        if (curClassId === h.schoolClassId) {
          let find = classList.find(h => classIds.includes(h.schoolClassId))
          curClassId = find.schoolClassId
          studentList = allStudentList.filter(
            h => h.schoolClassId === curClassId,
          )
          getGroup()
        }
      }
    })
    return
  }
  classList.forEach((h) => {
    h.notShow = false
  })
}, 500)

function dealCheckedAll(isChecked) {
  indeterminate = false
  if (activeTab === 0) {
    if (isChecked) {
      selectStudent = [
        ...new Set([
          ...selectStudent,
          ...studentList
            .filter(
              v =>
                !studentData.value.disabledStudentIds.includes(
                  v.schoolStudentId,
                ),
            )
            .map(item => item.schoolStudentId),
        ]),
      ]
      return
    }
    selectStudent = selectStudent.filter(
      h =>
        !studentList
          .filter(
            v =>
              !studentData.value.disabledStudentIds.includes(v.schoolStudentId),
          )
          .some(v => v.schoolStudentId === h),
    )
  }
  else {
    if (isChecked) {
      selectGroup = [
        ...new Set([
          ...selectGroup,
          ...groupList
            .filter(
              h =>
                !studentData.value.disabledGroupIds.includes(h.schoolClassId),
            )
            .map(item => item.schoolClassId),
        ]),
      ]
      return
    }
    selectGroup = selectGroup.filter(
      h =>
        !groupList
          .filter(
            h =>
              !studentData.value.disabledGroupIds.includes(h.schoolClassId),
          )
          .some(v => v.schoolClassId === h),
    )
  }
}
function handleCheckedAll(isChecked) {
  dealCheckedAll(isChecked)
  if (activeTab === 0) {
    handleChangeStudent()
    return
  }
  handleChangeGroup()
}

function clearAll() {
  selectGroup = selectGroup.filter(v => studentData.value.disabledGroupIds.includes(v))
  selectStudent = selectStudent.filter(v => studentData.value.disabledStudentIds.includes(v))
  classList.forEach((h) => {
    h.selectGroupArr = h.selectGroupArr.filter(v => studentData.value.disabledGroupIds.includes(v.schoolClassId))
    h.selectStudentArr = h.selectStudentArr.filter(v => studentData.value.disabledStudentIds.includes(v.schoolStudentId))
  })
  getAllStatus()
}

function confirm() {
  Object.assign(studentData.value, {
    classList: JSON.parse(
      JSON.stringify(
        classList.filter(
          h => h.selectStudentArr.length || h.selectGroupArr.length,
        ),
      ),
    ),
    selectStudentList: [
      ...new Set(
        [
          ...combineStudentAndGroup(classList), ...(studentData.value.specialClass?.selectStudentArr || []),
        ].map(item => item.schoolStudentId),
      ),
    ],
  })
  showDialog.value = false
  emit('confirm', studentData.value)
}
</script>

<template>
  <div>
    <el-dialog
      v-model="showDialog"
      top="13vh"
      class="w-[640px]"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <span class="font-600 text-17px mr-7px">{{ title }}</span>
            <span
              class="border border-[#6474FD] rounded-[2px] w-50px h-19px flex-cc text-13px text-[#6474FD] lh-[19px]"
            >
              {{ classType == 1 ? '行政班' : '教学班' }}
            </span>
          </div>
          <div class="flex items-center">
            <el-input
              v-show="activeTab === 0"
              v-model="keyword"
              class="h-29px w-192px mr-17px"
              placeholder="搜索名称/启鸣号"
              clearable
              @input="handleKeywordInput"
            />
            <img
              :src="$g.tool.getFileUrl('taskCenter/close.png')"
              alt=""
              class="h-15px w-15px van-haptics-feedback"
              @click="showDialog = false"
            />
          </div>
        </div>
      </template>
      <div class="border border-[#DCDFE6] rounded-[4px] p-13px flex h-349px">
        <div
          v-if="allStudentList.length || activeTab == 1 && classList?.length"
          class="w-118px flex-shrink-0 overflow-auto"
        >
          <div
            v-for="item in classList.filter((h) => !h.notShow)"
            :key="item.schoolClassId"
            class="text-15px p-7px cursor-pointer text-[#929296] mb-13px pr-0"
            :class="{ highlight: curClassId === item.schoolClassId }"
            @click="changeClass(item.schoolClassId)"
          >
            {{ dealName(item.sysGradeName + item.className)
            }}<span
              v-if="item.selectStudentArr.length || item.selectGroupArr.length"
            >
              ({{ reDuplicate(item) }}人)
            </span>
          </div>
        </div>
        <g-empty v-else></g-empty>
        <el-divider
          direction="vertical"
          class="h-full flex-shrink-0 ml-12px mr-18px"
        />
        <!-- <g-loading v-if="loading" class="h-200px w-full"></g-loading> -->

        <div class="flex-1 myDialogCheckbox flex flex-col w-0 relative pr-5px">
          <div class="flex text-15px tabs text-[#929296] flex-shrink-0">
            <div
              ref="tab1Ref"
              class="mr-35px cursor-pointer"
              :class="{ activeTab: activeTab === 0 }"
              @click="switchTab(0, tab1Ref)"
            >
              学生({{ curClass.selectStudentArr.filter(h => studentList.some(v => v.schoolStudentId == h.schoolStudentId))?.length ?? 0 }}/{{
                studentList.length
              }})
            </div>
            <div
              ref="tab2Ref"
              class="cursor-pointer"
              :class="{ activeTab: activeTab === 1 }"
              @click="switchTab(1, tab2Ref)"
            >
              组({{ curClass.selectGroupArr?.length ?? 0 }}/{{ groupList.length }})
            </div>
            <div class="underline" :style="underlineStyle"></div>
          </div>
          <el-checkbox
            v-if="
              (studentList.length && activeTab == 0)
                || (groupList.length && activeTab == 1)
            "
            v-model="checkedAll"
            class="absolute right-5px h-20px lh-[20px] font-500 text-[#333]"
            :indeterminate="indeterminate"
            @change="handleCheckedAll"
          >
            全选
          </el-checkbox>

          <div
            v-if="activeTab === 0"
            id="studentContainer"
            class="mt-16px flex-1 overflow-auto"
          >
            <template v-if="!loading">
              <el-checkbox-group
                v-if="studentList.length"
                v-model="selectStudent"
                class="flex flex-wrap"
                @change="handleChangeStudent"
              >
                <el-checkbox
                  v-for="item in studentList"
                  :key="item.schoolStudentId"
                  :value="item.schoolStudentId"
                  class="text-[#333] mb-17px w-[42.5%] h-20px"
                  :disabled="
                    studentData.disabledStudentIds.includes(item.schoolStudentId)
                  "
                >
                  <div class="flex">
                    <div class="max-w-75px truncate">
                      {{ item.userName }}
                    </div>
                    <div>（{{ item.thirdUniqueId }}）</div>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
              <g-empty v-else></g-empty>
            </template>
          </div>

          <div
            v-else
            id="groupContainer"
            class="mt-17px flex-1 overflow-auto myGroupCheckbox"
          >
            <el-checkbox-group
              v-if="groupList.length"
              v-model="selectGroup"
              @change="handleChangeGroup"
            >
              <div
                v-for="item in groupList"
                :key="item.schoolClassId"
                class="flex mb-12px py-9px items-start pl-13px"
                :class="{
                  highlightContainer: clickedId === item.schoolClassId,
                }"
              >
                <el-checkbox
                  class="text-[#333] text-14px w-16px flex-shrink-0 mt-2px"
                  :value="item.schoolClassId"
                  :disabled="
                    studentData.disabledGroupIds.includes(item.schoolClassId)
                  "
                >
                </el-checkbox>
                <div class="flex-1 ml-11px">
                  <div>
                    <div
                      class="flex items-center h-20px leading-[20px] cursor-pointer"
                      @click="
                        () => {
                          item.unfold = !item.unfold
                          clickedId = item.schoolClassId
                        }
                      "
                    >
                      <img
                        :src="
                          item.unfold
                            ? $g.tool.getFileUrl('taskCenter/arrow-bottom.png')
                            : $g.tool.getFileUrl('taskCenter/arrow-right.png')
                        "
                        alt=""
                        :class="item.unfold ? 'w-10px h-7px' : 'w-7px h-10px'"
                      />
                      <div class="ml-13px">
                        {{ item.className }}（{{ item.studentCount }}）
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="item.unfold"
                    class="flex flex-wrap gap-x-25px gap-y-9px mt-9px"
                  >
                    <div
                      v-for="student in item.list"
                      :key="student.schoolStudentId"
                      class="w-75px truncate !lh-[21px]"
                    >
                      {{ student.userName }}
                    </div>
                  </div>
                </div>
              </div>
            </el-checkbox-group>
            <g-empty v-else></g-empty>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button class="h-29px" @click="showDialog = false">
            取消
          </el-button>
          <el-button class="h-29px" @click="clearAll">
            清空全部
          </el-button>
          <el-button class="h-29px"
                     type="primary"
                     @click="confirm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.highlight {
  font-weight: 600;
  color: #6474fd !important;
  background: rgba(100, 116, 253, 0.14);
  border-radius: 4px;
}
.tabs {
  position: relative;
  display: flex;
  width: 100%;
}
.underline {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 1px;
  background-color: #6474fd;
  transition:
    transform 0.3s ease,
    width 0.3s ease;
}
.activeTab {
  font-weight: 600;
  color: #333;
}

:deep(.myDialogCheckbox) {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #333333;
  }
  .el-checkbox__label {
    padding-left: 6px;
  }
  .myGroupCheckbox {
    .el-checkbox {
      display: flex;
      align-items: start;
      height: auto !important;
    }
    .el-checkbox-group {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
}

.highlightContainer {
  background: #f3f4f9;
  border-radius: 4px;
}
</style>
