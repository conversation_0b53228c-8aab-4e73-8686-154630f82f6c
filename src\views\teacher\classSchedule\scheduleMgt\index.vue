<script lang="ts" setup>
import {
  addTimetable,
  changeTimetableSts,
  getPhaseList,
  getTimetableDetail,
  getTimetableList,
} from '@/api/aiTask'
import importExcel from '@/plugins/importExcel'

interface Teacher {
  teacherName: string
  courseName: string
  week: number | null
}
interface ContentItem {
  weedDayIndex: number
  teacherList: Teacher[]
}
interface TimetableItem {
  timetableTimeName: string
  beginTime: string
  endTime: string
  contentList: ContentItem[]
}
const downUrl = 'https://edu-jzt.oss-cn-chengdu.aliyuncs.com/tutoring_test/xlsx/202507/20250722/4a5d3287f65ea7a6eb0237e3a377d8c9.xlsx'

const route = useRoute()
const tableOptions = reactive({
  ref: null as any,
  key: '',
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    {
      type: 'index',
      label: '序号',
    },
    {
      prop: 'phaseName',
      label: '学期',
    },
    {
      prop: 'isEnable',
      label: '启用状态',
      slot: true,
    },
    {
      prop: 'cz',
      label: '操作',
      slot: true,
    },
  ],
  data: [],
})
let showDialog = $ref(false)
let showPreviewDialog = $ref(false)
let previewLoading = $ref(true)
let isCheck = $ref(false)
let phaseList = $ref<any>()
let uploadRef = $ref<any>(null)
const formOptions = $ref({
  className: null,
  tlSchoolPhaseId: '',
  phaseName: null,
  isEnable: 2,
  timetable: [] as any,
})
const rules = $ref({
  tlSchoolPhaseId: [{
    required: true,
    message: '请选择学期',
    trigger: 'change',
  }],
  isEnable: [{
    required: true,
    message: '请选择状态',
    trigger: 'change',
  }],
  timetable: [{
    required: true,
    message: '请上传课程表',
    trigger: 'change',
  }],
})
const scheduleForm = $ref<any>()
let uploadSheetData = $ref<any>()

const dialogTitle = $computed(() => {
  return `${isCheck ? '查看' : '新增'}课程表`
})

const calSrc = $computed(() => {
  if (!formOptions.timetable[0]?.resource_url) return
  return $g.tool.getWeb365Url(formOptions.timetable[0]?.resource_url)
})

// 表格数据
function getListApi() {
  getTimetableList({ schoolClassId: route.query.schoolClassId }).then((res) => {
    tableOptions.data = res || []
    tableOptions.loading = false
  }).catch((e) => {
      tableOptions.data = []
      tableOptions.loading = false
    })
}

// 学期列表
function fetchPhaseList() {
  getPhaseList({ schoolClassId: route.query.schoolClassId }).then((res) => {
    phaseList = res || []
  }).catch((e) => {
      phaseList = []
    })
}

// 查看获取详情
function openDialog(row?) {
  if (!$g.tool.isTrue(row)) {
    scheduleForm?.resetFields()
    formOptions.timetable = []
    showDialog = true
    return
  }
  getTimetableDetail({ schoolClassTimetableId: row.schoolClassTimetableId }).then((res) => {
    formOptions.isEnable = res.isEnable
    formOptions.phaseName = res.phaseName
    formOptions.timetable = [
      {
        name: res.sheetFilename,
        resource_url: res.sheetUrl,
        url: res.sheetUrl,
      },
    ]
    isCheck = true
    showDialog = true
  }).catch((e) => {
    })
}

function closeDialog() {
  showDialog = false
  setTimeout(() => {
    isCheck = false
  }, 500)
}

function submitForm() {
  scheduleForm.validate((valid) => {
    if (valid)
      submit ()
  })
}
const submit = $g._.throttle(
  () => {
    const params = {
      tlSchoolPhaseId: formOptions.tlSchoolPhaseId,
      schoolClassId: route.query?.schoolClassId,
      isEnable: formOptions.isEnable,
      sheetUrl: formOptions.timetable[0]?.resource_url,
      sheetFilename: formOptions.timetable[0]?.name,
      timeList: uploadSheetData,
    }
    addTimetable(params).then((res) => {
      $g.showToast('上传成功')
      getListApi()
      closeDialog()
    }).catch((e) => {})
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
)

// 启用禁用状态
function changeSwitch(row) {
  changeTimetableSts({ schoolClassTimetableId: row.schoolClassTimetableId }).then((res) => {
    getListApi()
  })
}

// 下载模板
function download() {
  $g.tool.downloadFile(downUrl)
}

// 删除/预览文件
function handleFile(item, index) {
  if (isCheck) {
    showDialog = false
    showPreviewDialog = true
    return
  }
  if (item.percentage != 100)
    uploadRef.handleRemove(item)

  formOptions.timetable?.splice(index, 1)
}

// 获取文件上传成功后的回调
async function uploadSuccess(file?) {
  scheduleForm.validateField('timetable') // 手动触发一下校验
  let tableTemplate = [
    'timetableTimeName',
    'time',
    'week1',
    'week2',
    'week3',
    'week4',
    'week5',
    'week6',
    'week7',
  ]
  const regionArr: any = await importExcel(file.raw, tableTemplate)
  if (!regionArr?.length) return
  let delFront3Line = regionArr.slice(3, regionArr?.length)?.filter(v => v.time)

  uploadSheetData = handleDataToNew(delFront3Line)
}

// 表格上传后的数据处理
function handleDataToNew(data1: any[]): TimetableItem[] {
  return data1.map((item) => {
    const [beginTime, endTime] = item.time.split('-')
    const contentList: ContentItem[] = []

    for (let i = 1; i <= 7; i++) {
      const weekKey = `week${i}` as keyof typeof item
      const weekContent = item[weekKey]?.trim()

      if (!weekContent) continue

      // 处理单双周数据
      if (weekContent.includes('单周') || weekContent.includes('双周')) {
        processWeeklyData(weekContent, i, contentList)
      }
      else {
        // 处理普通每周数据
        processRegularData(weekContent, i, contentList)
      }
    }

    return {
      timetableTimeName: item.timetableTimeName,
      beginTime: beginTime.trim(),
      endTime: endTime.trim(),
      contentList,
    }
  })
}

// 处理单双周数据的辅助函数
function processWeeklyData(content: string, dayIndex: number, contentList: ContentItem[]) {
  const lines = content.split('\n').filter(line => line.trim())
  let currentWeekType: any = null
  let currentCourse = ''

  lines.forEach((line) => {
    // 检测单双周标记
    if (line.startsWith('单周-')) {
      currentWeekType = 1 // 单周转为1
      currentCourse = line.split('-')[1]
    }
    else if (line.startsWith('双周-')) {
      currentWeekType = 2 // 双周转为2
      currentCourse = line.split('-')[1]
    }
    // 检测老师行（非空且不包含特殊标记）
    else if (currentWeekType && currentCourse && line.trim() && !line.includes('-')) {
      contentList.push({
        weedDayIndex: dayIndex,
        teacherList: [{
          week: currentWeekType,
          teacherName: line.trim(),
          courseName: currentCourse.trim(),
        }],
      })
    }
  })
}
// 处理常规每周数据的辅助函数
function processRegularData(content: string, dayIndex: number, contentList: ContentItem[]) {
  const lines = content.split('\n').filter(line => line.trim())

  // 成对处理课程和老师
  for (let i = 0; i < lines.length; i += 2) {
    if (i + 1 >= lines.length) break

    const course = lines[i].trim()
    const teacher = lines[i + 1].trim()

    if (course && teacher) {
      contentList.push({
        weedDayIndex: dayIndex,
        teacherList: [{
          week: 0, // 标记为每周
          teacherName: teacher,
          courseName: course,
        }],
      })
    }
  }
}

onMounted(() => {
  getListApi()
  fetchPhaseList()
})
</script>

<template>
  <div class="h-screen p-26px" style="width: 100vw">
    <g-navbar title="课程表管理">
    </g-navbar>
    <div class="flex justify-between items-center my-15px">
      <span>年级班级：{{ route.query?.className || '-' }}</span>
      <div>
        <el-button
          type="primary"
          @click="openDialog('')"
        >
          新增课程表
        </el-button>
        <el-button type="primary" @click="download">
          下载课表模板
        </el-button>
      </div>
    </div>

    <g-table :table-options="tableOptions" @change-page="getListApi">
      <template #isEnable="{ row }">
        <van-switch
          v-model="row.isEnable"
          :active-value="2"
          :inactive-value="1"
          size="18px"
          @change="changeSwitch(row)"
        />
      </template>
      <template #cz="{ row }">
        <el-button
          text
          type="primary"
          @click="openDialog(row)"
        >
          查看
        </el-button>
      </template>
    </g-table>

    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      :form-options="formOptions"
      @close="closeDialog"
    >
      <el-form
        ref="scheduleForm"
        :model="formOptions"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item label="班级：">
          <span>{{ route.query?.className || '-' }}</span>
        </el-form-item>
        <el-form-item label="学期：" prop="tlSchoolPhaseId">
          <el-select
            v-if="!isCheck"
            v-model="formOptions.tlSchoolPhaseId"
            placeholder="请选择学期"
          >
            <el-option
              v-for="phase in phaseList"
              :key="phase.tlSchoolPhaseId"
              :label="phase.phaseName"
              :value="phase.tlSchoolPhaseId"
            ></el-option>
          </el-select>
          <span v-else>{{ formOptions.phaseName }}</span>
        </el-form-item>
        <el-form-item label="课程表状态：" prop="isEnable">
          <el-select
            v-if="!isCheck"
            v-model="formOptions.isEnable"
            placeholder="请选择状态"
          >
            <el-option label="启用" :value="2"></el-option>
            <el-option label="禁用" :value="1"></el-option>
          </el-select>
          <span v-else>{{ formOptions.isEnable == 1 ? '禁用' : '启用' }}</span>
        </el-form-item>
        <el-form-item
          label="上传课程表："
          prop="timetable"
          :class="{ 'timetable-upload': formOptions.timetable?.length }"
        >
          <g-upload
            ref="uploadRef"
            v-model:file-list="formOptions.timetable"
            type="drag"
            mode="text"
            accept=".xls,.xlsx"
            :limit="1"
            :show-file-list="false"
            :oss="{
              params: {
                application: 'teachertargetrecord',
              },
            }"
            @upload-success="uploadSuccess"
          >
            <el-button v-show="!formOptions.timetable?.length" type="primary">
              点击上传
            </el-button>
            <template #tip>
              <div v-show="!formOptions.timetable?.length">
                <el-tag type="info">
                  仅支持.xls,.xlsx格式且大小不超过20M
                </el-tag>
              </div>
            </template>
          </g-upload>
          <div
            v-for="(item, index) in formOptions.timetable"
            :key="item.uid"
            class="rounded-[6px] mr-15px mb-10px text-12px"
          >
            <div class="flex items-center box-border">
              <div class="font-600 flex-1 truncate">
                {{ item.name }}
              </div>
              <div
                class="text-[#FF4646] cursor-pointer  text-center ml-13px cursor-pointer"
                :class="{ '!text-[#6474fd]': isCheck }"
                @click="handleFile(item, index)"
              >
                {{ isCheck ? '预览' : '删除' }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex justify-end">
        <el-button @click="closeDialog">
          取消
        </el-button>
        <el-button type="primary" @click="submitForm">
          确定
        </el-button>
      </div>
    </el-dialog>
    <van-popup
      v-model:show="showPreviewDialog"
      safe-area-inset-top
      safe-area-inset-bottom
      close-on-popstate
      class="w-[100vw] h-[80vh] p-30px overflow-hidden bg-[#FFFFFF] br-[12px]"
      teleport="#app"
      v-bind="$attrs"
      closeable
    >
      <g-loading v-show="previewLoading" class="h-200px"></g-loading>

      <iframe
        v-show="!previewLoading"
        :key="formOptions.timetable[0]?.resource_url + Date.now()"
        :src="calSrc"
        frameborder="0"
        class="w-full h-full"
        @load="previewLoading = false"
      ></iframe>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
.timetable-upload{
  :deep(){
    .el-upload--text {
      display: none;
    }
    .el-upload-list {
      margin-top: 0 !important;
    }
  }
}
</style>
