@use './normalize.scss';
@use './tool.scss';
@use './vant.reset.scss';
@use './element.reset.scss';

/* tailwindcss */
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}

html,
body {
  height: 100vh;
  background: #f3f4f9;
  overflow: auto;
  font-size: 15px;
  -webkit-overflow-scrolling: touch;
  color: #333;
  &::-webkit-scrollbar {
    display: none;
  }
}

#app {
}



// 锐字圆体（固定文案：智习室作业）
@font-face {
  font-family: 'rzyt';
  src: url('./font/RuiZiYuanTi_min.ttf');
}
.rzyt {
  font-family: 'rzyt';
}

// 数
@font-face {
  font-family: 'din-regular';
  src: url('./font/D-DIN-PRO-400-Regular.otf');
}

.din-regular{
  font-family: 'din-regular';
}
