<script setup lang="ts">
const props = defineProps({
  questionList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  activeQuestionId: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['changeQuestion'])

const currentQuestion = computed(() => {
  return props.questionList.find(item => item.questionId === props.activeQuestionId)
})

const canJumpToPre = computed(() => {
  return props.questionList.findIndex(item => item.questionId === props.activeQuestionId) > 0
})

const canJumpToNext = computed(() => {
  return props.questionList.findIndex(item => item.questionId === props.activeQuestionId) < props.questionList.length - 1
})

/** 上一题 */
function handleJumpToPre() {
  if (!canJumpToPre.value)
    return

  let preQuestionId = props.questionList[props.questionList.findIndex(item => item.questionId === props.activeQuestionId) - 1].questionId
  handleQuestionChange(preQuestionId)
}

/** 下一题 */
function handleJumpToNext() {
  if (!canJumpToNext.value)
    return

  let nextQuestionId = props.questionList[props.questionList.findIndex(item => item.questionId === props.activeQuestionId) + 1].questionId
  handleQuestionChange(nextQuestionId)
}

/** 切换题目 */
function handleQuestionChange(id) {
  if (id === props.activeQuestionId)
    return

  emit('changeQuestion', id)
}
</script>

<template>
  <div class="flex-cc select-none">
    <el-popover
      placement="bottom"
      popper-class="!w-[400px] flex flex-wrap  rounded-[6px] pb-0"
      :show-arrow="false"
      :teleported="false"
      trigger="click"
    >
      <div
        v-for="(question, index) in questionList"
        :key="question.id"
        class="w-80px h-30px flex-cc cursor-pointer flex-shrink-0 border border-[#E8E8E8FF] rounded-[5px] mb-16px"
        :class="[question.questionId === activeQuestionId ? 'bg-[#ECEFFFFF] border-[#ECEFFFFF] text-[#6474FDFF]' : '',
                 (index + 1) % 4 === 0 ? 'mr-0' : 'mr-16px']"
        @click="handleQuestionChange(question.questionId)"
      >
        第{{ question.qnum }}
      </div>
      <template #reference>
        <div class="w-140px h-43px px-13px flex justify-between items-center bg-white rounded-[9px] cursor-pointer mr-17px">
          <span class="text-13px font-600">第{{ currentQuestion?.qnum }}</span>
          <svg-ri-arrow-left-s-line class="-rotate-90 opacity-50" />
        </div>
      </template>
    </el-popover>

    <div
      class="flex-cc cursor-pointer mr-30px text-17px text-[#6474FDFF]"
      :class="{ '!text-[#6C6C74FF] !cursor-not-allowed': !canJumpToPre }"
      @click="handleJumpToPre"
    >
      <svg-ri-arrow-left-s-line />
      <span class="ml-16px font-600">上一题</span>
    </div>

    <div
      class="flex-cc cursor-pointer text-17px text-[#6474FDFF]"
      :class="{ '!text-[#6C6C74FF] !cursor-not-allowed': !canJumpToNext }"
      @click="handleJumpToNext"
    >
      <span class="mr-16px font-600">下一题</span>
      <svg-ri-arrow-right-s-line />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.active {
  font-weight: bold;
  color: #409eff;
}
</style>
