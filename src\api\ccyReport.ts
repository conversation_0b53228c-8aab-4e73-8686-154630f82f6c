import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config
// 获取列表信息
export async function getListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/activity/periodTest/studentPage`,
    data,
  )
}
// word生成状态总览
export async function getWordStateApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/activity/periodTest/wordState`,
    data,
  )
}
//
export async function getFilesApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/activity/periodTest/wordList`,
    data,
  )
}

/** ai批改结果分页 */
export function getAutoCorrectResultPage(data?) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/questionAutoCorrect/ai/result/page`,
    data,
  )
}

/** ai批改 查看批改结果 */
export function getAutoCorrectResult(data?) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/questionAutoCorrect/ai/results`,
    data,
  )
}
