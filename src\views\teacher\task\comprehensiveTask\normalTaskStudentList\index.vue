<script setup lang="ts" name="NormalTaskStudentList">
import { getPatternStudentList } from '@/api/taskCenter'
import { useRouter } from 'vue-router'
import SearchFrom from '../components/SearchForm.vue'
import studentTables from '../taskOverview/components/studentTables.vue'

const route = useRoute()
const router = useRouter()
const tableRef: any = $ref(null)
const taskName = ref(route.query.taskName as string || '')
const taskPatternType = ref(route.query.taskPatternType as string || '')
let sortOption = $ref({}) // 添加排序状态

// 组装 currentGroup，headerType: 1-默写 2-阅题 3-举一反一
const currentGroup = computed(() => ({
  headerType: Number(taskPatternType.value) === 2 ? 1 : Number(taskPatternType.value) === 3 ? 2 : Number(taskPatternType.value) === 4 ? 3 : 0,
  taskPatternType: Number(taskPatternType.value),
}))

const hideOperation = computed(() => Number(taskPatternType.value) === 3)

const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,
  column: [],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})

const formOptions: any = $ref({
  items: {
    keyword: {
      label: '搜索学生',
      slot: true,
    },
  },
  data: { keyword: '' },
})

async function getData(option: any = null) {
  try {
    tableOptions.loading = true
    const {
      page = 1,
      pageSize = 10,
    } = tableOptions.pageOptions
    // 处理排序参数
    if (option?.order) sortOption = option
    if (option?.cancelSort) sortOption = {}

    const query: any = {
      page,
      pageSize,
      taskId: route.query.taskId,
      taskPatternType: taskPatternType.value,
      ...formOptions.data,
      ...sortOption, // 添加排序参数
    }
    const res = await getPatternStudentList(query)
    // 兼容字段名，保证有taskState和correctionState
    tableOptions.data = (res?.list || []).map(item => ({
      ...item,
      taskState: item.taskState ?? item.status ?? item.TaskState,
      correctionState: item.correctionState ?? item.correctStatus ?? item.CorrectionState,
    }))
    tableOptions.pageOptions.total = res?.total || 0
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
  finally {
    tableOptions.loading = false
  }
}

function initPage(option: any = null) {
  tableOptions.pageOptions.page = 1
  getData(option)
}

onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})
</script>

<template>
  <div class="p-26px" style="width: 100vw;">
    <g-navbar :title="taskName">
    </g-navbar>
    <div class="mt-20px br-[6px] p-17px bg-[white]">
      <SearchFrom :form-option="formOptions" @change="initPage">
        <template #keyword>
          <el-input
            v-model="formOptions.data.keyword"
            style="width: 181px"
            class="h-34px"
            clearable
            placeholder="请输入学生姓名或ID"
            @keyup.enter.prevent="initPage()"
          />
          <el-button color="#6474FD"
                     class="w-64px ml-6px h-34px border-none"
                     @click="initPage()"
          >
            搜索
          </el-button>
        </template>
      </SearchFrom>
    </div>
    <div class="mt-20px br-[6px] p-17px bg-[white]">
      <studentTables
        ref="tableRef"
        :current-group="currentGroup"
        :table-options="tableOptions"
        :hide-clearance="true"
        :hide-operation="hideOperation"
        @get-data="getData"
        @init-page="initPage"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
  .el-tabs__nav-wrap:after {
    height: 0px;
  }
  .el-tabs__item{
    color:#6C6C74
  }
  .el-tabs__item:hover{
    color:#333;
  }
  .is-active{
    color:#333;
    font-weight: 600;
  }
}
</style>
