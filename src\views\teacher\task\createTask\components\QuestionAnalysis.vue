<script setup lang="ts">
// 弹窗显示
const showDialog = ref(false)

const gChartRef = ref()
// 题目列表数据
const questionList = inject<Ref<any[]>>('questionList', ref([]))

const colorMap = {
  容易: '#8CE48C',
  较易: '#959BFF',
  一般: '#6474FD',
  较难: '#6ED3F4',
  困难: '#FF9945',
  其他: '#C7CAE4',
}

// 统计数据
const questionAnalysisData = $computed(() => {
  // 题型统计数据
  let typeArr: { type: number | string
    name: string
    value: number }[] = []
  // 难度统计数据
  let difficultyArr: {
    difficulty: number | string
    name: string
    value: number
    color: string
  }[] = []

  questionList.value.forEach((question) => {
    // 统计题型
    let typeIndex = typeArr.findIndex(v => v.type === question.sysQuestionTypeId)
    typeIndex === -1
      ? typeArr.push({
          type: question.sysQuestionTypeId,
          name: question.sysQuestionTypeName,
          value: 1,
        })
      : typeArr[typeIndex].value++

    // 统计难度
    let difficultyIndex = difficultyArr.findIndex(
      v => v.difficulty === question.sysQuestionDifficultyId,
    )
    difficultyIndex === -1
      ? difficultyArr.push({
          difficulty: question.sysQuestionDifficultyId,
          name: question.sysQuestionDifficultyName,
          value: 1,
          color:
            colorMap[question.sysQuestionDifficultyName] || colorMap['其他'],
        })
      : difficultyArr[difficultyIndex].value++
  })

  typeArr.push({
    type: 'all',
    name: '合计',
    value: questionList.value.length,
  })

  return {
    typeArr,
    difficultyArr,
  }
})

let chartOption = $computed(() => ({
  legend: {
    bottom: 0,
  },
  series: [
    {
      name: '难度',
      type: 'pie',
      center: ['50%', '40%'],
      radius: ['40%', '60%'],
      data: questionAnalysisData.difficultyArr.map(item => ({
        ...item,
        itemStyle: {
          color: item.color,
        },
      })),
      avoidLabelOverlap: false,
      label: {
        formatter: '{b}:{c}',
      },
    },
  ],
}))
useEventListener('resize', () => {
  gChartRef.value?.resize()
})
</script>

<template>
  <el-button
    class="bg-[#ECEFFF] text-[#5864F8] border-[#646AB4] text-15px font-600 h-30px"
    :class="!questionList.length && 'opacity-50'"
    :disabled="!questionList.length"
    plain
    @click="showDialog = true"
  >
    题目分析
  </el-button>
  <van-popup
    v-model:show="showDialog"
    position="right"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="w-476px h-screen flex flex-col py-17px px-21px overflow-hidden bg-[#F3F4F9]"
    teleport="#app"
    v-bind="$attrs"
  >
    <div class="flex items-center justify-between font-600 text-17px mb-21px">
      <span>题目分析</span>
      <img
        class="cursor-pointer w-15px"
        src="@/assets/img/taskCenter/close.png"
        @click="showDialog = false"
      />
    </div>

    <div class="flex-1 overflow-auto" :class="!$g.isPC && 'no-bar'">
      <!-- 题型统计 -->
      <div class="bg-[white] rounded-[6px] p-17px">
        <div class="text-17px font-600 leading-[24px] mb-17px">
          题型统计
        </div>

        <table class="w-full">
          <thead>
            <tr class="text-left">
              <th
                class="w-1/2 bg-[#6474FD1A] px-16px py-10px rounded-[4px_0_0_4px]"
              >
                题型
              </th>
              <th
                class="w-1/2 bg-[#6474FD1A] px-16px py-10px rounded-[0_4px_4px_0]"
              >
                题量
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in questionAnalysisData.typeArr"
              :key="item.type"
              :class="{
                'border-t border-solid border-[#e8e8e8]': index !== 0,
              }"
            >
              <td class="px-16px py-10px">
                {{ item.name }}
              </td>
              <td class="px-16px py-10px">
                {{ item.value }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 难度结构 -->
      <div class="bg-[white] rounded-[8px] p-16px mt-16px">
        <div class="text-17px font-600 leading-[24px] mb-16px">
          难度结构
        </div>
        <div class="w-full h-300px">
          <g-chart ref="gChartRef" :option="chartOption" />
        </div>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped></style>
