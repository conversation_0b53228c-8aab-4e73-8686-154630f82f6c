<script setup lang="ts">
import { deleteConfirmed, deleteTask, getTaskList } from '@/api/taskCenter'
import { useUserStore } from '@/stores/modules/user'
import UpdateTask from './UpdateTask.vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => {

    },
  },
})
const emit = defineEmits(['getListApi'])

const router = useRouter()
let dialogKey = $ref($g.tool.uuid(4))
let showDialog = $ref(false)
let taskList = $ref([])

// 获取任务列表数据
async function fetchTaskList() {
  try {
    const res = await getTaskList({})
    taskList = res.list || []
  }
  catch (error) {
  }
}

onMounted(() => {
  fetchTaskList()
})

// 是否是错题任务
const isErrorTask = $computed(() => {
  return props.data?.taskType == 4
})

const taskTypeMap = {
  1: {
    name: '学科网',
    className: 'xkw',
  },
  2: {
    name: '校本',
    className: 'xb',
  },
  3: {
    name: '资源',
    className: 'zy',
  },
  4: {
    name: '错题',
    className: 'ct',
  },
}

// 平均用时
const averageTime = $computed(() => {
  if (!props.data.averageTime || props.data.averageTime < 30) return ''
  let hours = Math.floor($g.math(props.data.averageTime).divide(3600)
    .value())
  let minutes = Math.floor($g.math((props.data.averageTime % 3600)).divide(60)
    .value())
  let remainingSeconds = props.data.averageTime % 60
  // 四舍五入剩余的秒数并加到分钟上
  if (remainingSeconds >= 30) { // 如果剩余秒数大于等于30秒，则分钟数加1
    minutes += 1
  }
  // 如果分钟数达到或超过60，调整小时数和分钟数
  if (minutes >= 60) {
    hours += 1
    minutes = 0
  }
  return [hours, minutes]
})

// 预计用时
const estimateTime = $computed(() => {
  if (!props.data.estimateTime) return ''
  const hours = Math.floor(props.data.estimateTime / 60) // 计算小时数
  const minutes = props.data.estimateTime % 60 // 计算剩余分钟数
  return [hours, minutes]
})

const expired = $computed(() => {
  const targetTime = new Date(props.data?.requireCompleteTime).getTime()
  // 获取当前时间
  const currentTime = Date.now()
  // 比较当前时间是否大于目标时间
  return currentTime > targetTime
})

async function handleCommand(command) {
  try {
    if (command == 1) {
      dialogKey = $g.tool.uuid(4)
      await nextTick()
      showDialog = true
      return true
    }
    if (command == 2) {
      const res = await deleteConfirmed({ taskId: props.data.taskId })
      if (res.studyingRate > 0) {
        await $g.showConfirmDialog({
          title: '确定删除吗?',
          message: `当前任务有${$g.math(res.studyingRate).multiply(100).value()}%的同学在学习，将同步删除学生端对应任务。确认删除吗?`,
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          className: 'global-custom-van-dialog',
          overlayClass: 'bg-[rgba(0,0,0,0.3)]',
          zIndex: 4001,
        })
      }
      try {
        await deleteTask({ taskId: props.data.taskId })
        $g.msg('删除成功！', 'success')
        emit('getListApi', {
          id: props.data.taskId,
          isDelete: true,
        })
      }
      catch (error) {
        $g.msg('删除失败！', 'error')
        console.log('⚡[ error ] >', error)
      }
      return true
    }
    if (command == 3) {
      const {
        taskType,
        sysSubjectId,
        taskId,
        taskName,
        sysSubjectName,
      } = props.data
      router.push({
        name: 'CreateTask',
        query: {
          pageType: taskType,
          subjectId: sysSubjectId,
          taskId,
          taskName,
          subjectName: sysSubjectName,
        },
      })
    }
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
}

const startTime = $computed(() => {
  if (!props.data.releaseTime)
    return ''

  const arr = props.data.releaseTime.split('-')
  arr.shift()
  return arr.join('/')
})

const endTime = $computed(() => {
  if (!props.data.requireCompleteTime)
    return ''

  const arr = props.data.requireCompleteTime.split('-')
  arr.shift()
  return arr.join('/')
})

const classText = $computed(() => {
  if (!props.data.taskSchoolClassList)
    return ''

  const classTypeMap = {
    1: '行政',
    2: '教学',
    3: '自定义分组',
  }

  return props.data.taskSchoolClassList.reduce((str, item, index, arr) => {
    const prevItem = arr[index - 1]
    const isFirstOfType = !prevItem || prevItem.classType !== item.classType
    const prefix = isFirstOfType
      ? `${str ? '，' : ''}${classTypeMap[item.classType]}：`
      : '、'

    return str + prefix + item.className
  }, '')
})

function updateList() {
  emit('getListApi', {
    id: props.data.taskId,
    isDelete: false,
  })
}
function toReport(item) {
  // 处理特殊的任务模式类型跳转
  if (item?.taskPatternType && [2,
3,
4].includes(item.taskPatternType)) {
    router.push({
      name: 'NormalTaskStudentList',
      query: {
        taskId: item.taskId,
        taskName: item.taskName,
        taskPatternType: item.taskPatternType,
      },
    })
    return
  }

  // 原有的跳转逻辑
  if (item?.taskType == 3) {
    router.push({
      name: 'ResourceReport',
      query: {
        taskId: item?.taskId,
        taskName: item?.taskName,
      },
    })
    return
  }
  router.push({
    name: 'QuestionReport',
    query: {
      exerciseSourceType: 2,
      exerciseSourceId: item.taskId,
      title: item.taskName,
      configCorrect: item.configCorrect,
      schoolId: useUserStore().userInfo?.schoolId ?? useUserStore().userInfo.schoolList?.[0].schoolId,
    },
  })
}
</script>

<template>
  <div v-if="$g.tool.isTrue(data)">
    <div
      class="br-[6px] bg-[white] p-11px w-full cursor-pointer"
      :class="{ expiredBg: expired && !isErrorTask }"
      @click="toReport(data)"
    >
      <div class="flex items-center">
        <div class=" min-w-32px text-center px-1px py-3px flex-shrink-0 text-10px text-[white] mr-5px br-[2px]" :class="taskTypeMap[data?.taskType].className">
          {{ taskTypeMap[data?.taskType].name }}
        </div>
        <div class="flex-1 van-ellipsis text-16px font-600">
          {{ data.taskName }}
        </div>
      </div>
      <div class="flex justify-between mt-11px mb-16px pl-7px h-49px">
        <div>
          <p class="text-19px">
            <template v-if="data.correctRate">
              <span class="din-regular">{{ data.correctRate || '-' }}</span><span class="text-13px">%</span>
            </template>
            <span v-else>--</span>
          </p>
          <p class="text-13px text-[#929296]">
            正确率
          </p>
        </div>
        <template v-if="!isErrorTask">
          <div>
            <p class="text-19px">
              <template v-if="estimateTime">
                <span v-if="estimateTime[0]">
                  <span class="din-regular">{{ estimateTime[0] }}</span><span class="text-13px">时</span>
                </span>
                <span v-if="estimateTime[1]">
                  <span class="din-regular">{{ estimateTime[1] }}</span><span class="text-13px">分</span>
                </span>
              </template>
              <span v-else>--</span>
            </p>

            <p class="text-13px text-[#929296]">
              预计用时
            </p>
          </div>
        </template>

        <div>
          <p class="text-19px">
            <template v-if="data.averageProgress">
              <span class="din-regular">{{ data.averageProgress || '-' }}</span><span class="text-13px">%</span>
            </template>
            <span v-else>--</span>
          </p>
          <p class="text-13px text-[#929296]">
            平均进度
          </p>
        </div>
        <div>
          <p class="text-19px">
            <template v-if="averageTime">
              <span v-if="averageTime[0]">
                <span class="din-regular">{{ averageTime[0] }}</span><span class="text-13px">时</span>
              </span>
              <span v-if="averageTime[1]">
                <span class="din-regular">{{ averageTime[1] }}</span><span class="text-13px">分</span>
              </span>
            </template>
            <span v-else>--</span>
          </p>
          <p class="text-13px text-[#929296]">
            平均用时
          </p>
        </div>
      </div>
      <div class="flex items-center justify-between w-full">
        <div class="flex max-w-[88%] items-center text-[#929296] text-12px">
          <div class="van-ellipsis" :class="[!isErrorTask ? 'max-w-[50%]' : 'max-w-[100%]']">
            {{ classText }}
          </div>
          <template v-if="!isErrorTask">
            <div class="border-l-[1px] h-8px border-[#E6E6E6] mx-6px flex-shrink-0"></div>
            <div class="van-ellipsis flex-1">
              {{ startTime }} 至 {{ endTime }}
            </div>
          </template>
        </div>
        <div v-if="!isErrorTask"
             class="flex-shrink-0 cursor-pointer"
             @click.stop
        >
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="el-dropdown-link h-22px w-30px pt-7px flex-cc">
              <img :src="$g.tool.getFileUrl('taskCenter/ellipsis.png')" class="w-12px h-3px">
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">
                  修改任务
                </el-dropdown-item>
                <el-dropdown-item command="2">
                  删除任务
                </el-dropdown-item>
                <el-dropdown-item command="3">
                  复制任务
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <UpdateTask
      :key="dialogKey"
      v-model:show="showDialog"
      :task-id="data.taskId"
      @update-list="updateList"
      @click.stop
    ></UpdateTask>
  </div>
</template>

<style lang="scss" scoped>
.xkw {
  background: linear-gradient(270deg, #FEA08A 0%, #FF8C8B 100%);
}

.xb {
  background: linear-gradient(90deg, #FFA64A 0%, #FAC464 100%);
}

.zy {
  background: linear-gradient(96deg, #24C5F5 0%, #1EA0F0 100%);
}

.ct {
  background: linear-gradient( 120deg, #6242FF 0%, #9F8CFF 100%);
}

.expiredBg {
  background: url(@/assets/img/taskCenter/expired.png) right bottom / 68px 50px no-repeat;
}

:deep() {
  overflow: hidden;

  .el-overlay-dialog {
    overflow: hidden !important;
  }
}
</style>
