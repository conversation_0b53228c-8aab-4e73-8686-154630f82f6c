<script setup lang="ts">
const props = defineProps({
  mindMap: { type: Object },
  list: {
    type: Array,
    default() {
      return []
    },
  },
  isFIB: {
    type: <PERSON><PERSON><PERSON>,
  },
})
const emit = defineEmits(['changStatus'])
let activeNodes: any = $ref([])
let readonly = $ref(false)
let backEnd = $ref(false)
let forwardEnd = $ref(false)

function getMindMap() {
  return toRaw(props.mindMap)
}

// 执行命令
function execCommand(args) {
  getMindMap()?.execCommand(...(Array.isArray(args) ? args : [args]))
}

const hasGeneralization = $computed(() => {
  return (
    activeNodes.findIndex((node) => {
      return node.isGeneralization
    }) !== -1
  )
})

const hasRoot = $computed(() => {
  return (
    activeNodes.findIndex((node) => {
      return node.isRoot
    }) !== -1
  )
})

// 监听模式切换
// function onModeChange(mode) {
//   readonly = mode === "readonly"
// }

// 监听节点激活
function onNodeActive(args) {
  activeNodes = [...args[1]]
}

// 监听前进后退
function onBackForward(index, len) {
  backEnd = index <= 0
  forwardEnd = index >= len - 1
}

function changeFIB() {
  emit('changStatus')
}

// 打开公式侧边栏
function showFormula() {
  $g.bus.emit('mind_map_click_bar', 'FormulaSidebar')
}

onBeforeMount(() => {
  // bus.on('mode_change', onModeChange)
  $g.bus.on('mind_map_node_active', onNodeActive)
  $g.bus.on('mind_map_back_forward', onBackForward)
})

onBeforeUnmount(() => {
  // bus.off('mode_change', onModeChange)
  $g.bus.off('mind_map_node_active', onNodeActive)
  $g.bus.off('mind_map_back_forward', onBackForward)
})
</script>

<template>
  <div class="flex">
    <template v-for="(item, index) in list" :key="index">
      <div
        v-if="item == 'back'"
        class="toolbarBtn"
        :class="{
          disabled: readonly || backEnd,
        }"
        @click="execCommand('BACK')"
      >
        <span class="icon iconfont iconhoutui-shi"></span>
        <span class="text">回退</span>
      </div>
      <div
        v-if="item == 'forward'"
        class="toolbarBtn"
        :class="{
          disabled: readonly || forwardEnd,
        }"
        @click="execCommand('FORWARD')"
      >
        <span class="icon iconfont iconqianjin1"></span>
        <span class="text">前进</span>
      </div>

      <div
        v-if="item == 'deleteNode'"
        class="toolbarBtn"
        :class="{
          disabled: activeNodes.length <= 0,
        }"
        @click="execCommand('REMOVE_NODE')"
      >
        <span class="icon iconfont iconshanchu"></span>
        <span class="text">删除节点</span>
      </div>
      <div
        v-if="item == 'formula'"
        class="toolbarBtn"
        :class="{
          disabled: activeNodes.length <= 0 || hasGeneralization,
        }"
        @click="showFormula"
      >
        <span class="icon iconfont icongongshi"></span>
        <span class="text">公式</span>
      </div>
      <div v-if="item == 'fib'"
           class="toolbarBtn"
           @click="changeFIB"
      >
        <span class="icon iconfont" :class="isFIB ? '!border-[#4998f8]' : ''">
          <g-icon name="ri-space"
                  size=""
                  :color="isFIB ? '#4998f8' : ''"
          />
        </span>

        <span class="text" :class="isFIB ? '!text-[#4998f8]' : ''">
          填空预览
        </span>
      </div>
      <div
        v-if="item === 'summary'"
        class="toolbarBtn"
        :class="{
          disabled: activeNodes.length <= 0 || hasRoot || hasGeneralization,
        }"
        @click="execCommand('ADD_GENERALIZATION')"
      >
        <span class="icon iconfont icongaikuozonglan"></span>
        <span class="text">概要</span>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.toolbarBtn {
  display: flex;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
  margin-right: 20px;
  &:last-of-type {
    margin-right: 0;
  }
  &:hover {
    &:not(.disabled) {
      .icon {
        background: #f5f5f5;
      }
    }
  }
  &.active {
    .icon {
      background: #f5f5f5;
    }
  }
  &.disabled {
    color: #bcbcbc;
    cursor: not-allowed;
    pointer-events: none;
  }
  .icon {
    display: flex;
    height: 26px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    padding: 0 5px;
  }
  .text {
    margin-top: 3px;
  }
}
</style>
