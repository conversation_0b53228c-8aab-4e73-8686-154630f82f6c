<script setup lang="ts">
import { deleteTaskGroup, taskGroupTaskComplete } from '@/api/comprehensiveTask'

const props = defineProps<{
  item: any
  index: number
  len: number
  state: any
}>()
const emit = defineEmits(['toggleGroup', 'getTaskList'])
const router = useRouter()
const route = useRoute()
const TYPE_ARR = {
  1: '学科网选题',
  2: '校本练习题',
  3: '资源任务',
  4: '错题任务',
}

function toEditTask(item: any) {
  if (props.state != 1) return
  router.push({
    name: 'ComprehensiveTaskPackage',
    query: {
      ...route.query,
      pageType: item.taskType,
      taskScheduleId: item.taskScheduleId,
      taskScheduleGroupId: item.taskScheduleGroupId,
    },
  })
}
function deleteTask(item) {
  $g.confirm({
    content: '确定删除任务吗？',
  }).then(async () => {
    try {
      console.log('⚡[ item ] >', item)
      let res = await deleteTaskGroup({ taskScheduleGroupId: item.taskScheduleGroupId })
      console.log('⚡[ res ] >', res)
      $g.msg('删除任务组成功')
      emit('getTaskList')
    }
    catch (error) {
      console.log('error => ', error)
    }
  })
}

function toggleGroup(item, idx) {
  if ([2,
3,
4].includes(props.state)) return

  emit('toggleGroup', item, idx)
}

function handleChange(item) {
  taskGroupTaskComplete({ taskScheduleGroupId: item.taskScheduleGroupId }).then((res) => {
    item.isFinish = 2
  }).catch(() => {
    })
}
</script>

<template>
  <div :class="{ '-mt-34px': index != 0 }">
    <span>任务{{ $g.tool.numberToChinese(index + 1) }}</span>

    <div class="border border-[#DADDE8] rounded-[9px] mt-17px">
      <div class="flex items-center justify-between px-17px py-13px bg-[#EFF1FF]">
        <span class="font-500">{{ item.groupName }}</span>
        <div class="flex items-center">
          <svg-ri-draggable class="w-15px h-15px mr-[13px] cursor-move handle" :class="{ '!cursor-not-allowed': state != 1 }" />
          <el-dropdown :disabled="state != 1">
            <svg-ri:more-fill class="text-14px text-[#8c939d] cursor-pointer outline-none"  :class="{ '!cursor-not-allowed': state != 1 }"></svg-ri:more-fill>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :class="{ '!text-[#999] !cursor-not-allowed': state != 1 }" @click="toEditTask(item)">
                  编辑任务
                </el-dropdown-item>
                <el-dropdown-item @click="deleteTask(item)">
                  删除任务
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div class="p-17px !pb-15px">
        <div class="flex items-center pl-9px">
          <svg-task-taskType class="text-[#666] w-21px h-[21px] -mt-1px"></svg-task-taskType>
          <span class="pl-10px">任务类型： {{ TYPE_ARR[item.taskType] || '-' }}</span>
        </div>
        <div class="flex items-center my-17px pl-9px">
          <svg-task-taskRange class="text-[#666] w-21px h-[21px] -mt-2px"></svg-task-taskRange>
          <span class="pl-10px">任务范围： {{ item.studentNum || 0 }} 人</span>
        </div>
        <div class="flex items-center pl-9px">
          <svg-task-taskFile class="text-[#666] w-21px h-[21px] -mt-1px"></svg-task-taskFile>
          <span class="pl-10px">任务数量： {{ item.taskNum || 0 }} 个任务</span>
        </div>

        <div class="w-full border border-dashed border-[#ccc] mt-18px mb-15px"></div>

        <div class="text-[#666] flex items-center justify-between ">
          <div>
            <span>生效时间：{{ item.releaseTime }}</span>
            <el-divider direction="vertical" class="mx-13px"></el-divider>
            <span>失效时间：{{ item.requireCompleteTime || '不限时' }}</span>
          </div>
          <!-- <van-switch
            v-model="item.isFinish"
            size="large"
            :active-value="2"
            :inactive-value="1"
            :disabled="state != 2 || (item.isFinish == 2 && state == 2)"
            @change="handleChange(item)"
          /> -->
        </div>
      </div>
    </div>

    <div
      v-if="index != len - 1"
      class="w-20px mx-auto flex flex-col items-center justify-center my-11px cursor-pointer"
      :class="{ '!cursor-not-allowed': [2, 3, 4].includes(state) }"
      @click="toggleGroup(item, index)"
    >
      <div class="h-[33px] w-1px border border-dashed" :class="item.isLink ? 'border-[#6474FD]' : 'border-[#ccc]'"></div>
      <svg-task-link class="ml-1px w-17px h-17px my-5px" :color="item.isLink ? '#6474FD' : '#ccc'"></svg-task-link>
      <div class="h-[33px] w-1px border border-dashed" :class="item.isLink ? 'border-[#6474FD]' : 'border-[#ccc]'"></div>
    </div>
  </div>
</template>
