/** 批改结果 */
export enum CorrectStatus {
  /** 全错 */
  ERROR = 1,
  /** 半对 */
  HALF_RIGHT,
  /** 全对 */
  RIGHT,
  /** 我不会 */
  CANT,
}

/** 答题方式 */
export enum AnswerType {
  /** 客观题作答 */
  OBJECTIVE = 1,
  /** 主观题作答-白板作答 */
  SUBJECTIVE_CANVAS,
  /** 主观题作答-键盘作答 */
  SUBJECTIVE_KEYBOARD,
  /** 主观题作答-拍照作答 */
  SUBJECTIVE_PHOTO,
}

/** 教师的批改状态 */
export enum TeacherCorrectStatus {
  /** 未批改 */
  UNCORRECTED = 1,
  /** 批改中 */
  CORRECTING,
  /** 已批改 */
  CORRECTED,
}
