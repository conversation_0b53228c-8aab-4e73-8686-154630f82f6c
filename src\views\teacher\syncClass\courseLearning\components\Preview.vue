<script setup lang="ts">
import MindMap from '../MindMap/index.vue'

const props = defineProps({
  currentFile: {
    type: Object as PropType<any>,
    required: true,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  showTitle: {
    type: Boolean,
    default: false,
  },
  showEdit: {
    type: Boolean,
    default: false,
  },
  type: {
    type: Number,
    default: 3, // 3: 图片、视频、文件类型 2: markdown、富文本、思维导图
  },
  articleInfo: {
    type: Object as PropType<any>,
    required: true,
  },
})

const emit = defineEmits(['edit', 'update:show'])

let showLoading = $ref(false)
let showDialog = defineModel<boolean>('show')

/* 计算文件类型 */
const calFileType = $computed(() => {
  const imgType = ['jpg',
'png',
'jpeg',
'heic',
'heif',
'gif',
'webp']
  const videoType = ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8']
  const audioType = ['mp3',
'ogg',
'wav',
'aac']
  const fileType = ['doc',
'docx',
'xls',
'xlsx',
'pdf',
'ppt',
'pptx']

  if (videoType.includes(props.currentFile?.fileExtension) || $g.tool.isTrue(props.currentFile.fileAbsoluteUrlM3u8))
    return 'video'

  if (imgType.includes(props.currentFile?.fileExtension))
    return 'img'

  if (audioType.includes(props.currentFile?.fileExtension))
    return 'audio'

  if (fileType.includes(props.currentFile?.fileExtension))
    return 'file'

  return 'default'
})
const calSrc = $computed(() => {
  if (calFileType !== 'file')
    return props.currentFile.fileAbsoluteUrl ?? props.currentFile.fileAbsoluteUrlM3u8

  return $g.tool.getWeb365Url(props.currentFile?.fileAbsoluteUrl)
})

function goEdit() {
  emit('update:show', false)
  emit('edit')
}

watch(
  () => props.currentFile,
  () => {
    showLoading = true
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <van-popup
    v-model:show="showDialog"
    safe-area-inset-top
    safe-area-inset-bottom
    close-on-popstate
    class="w-[900px] h-[500px]  py-30px px-[30px] overflow-hidden bg-[#FFFFFF] br-[12px]"
    teleport="#app"
    v-bind="$attrs"
  >
    <div class="w-full  relative" :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'">
      <div v-if="showEdit || showTitle" class="flex justify-between items-center mb-13px">
        <span v-if="showTitle" class="text-15px font-600">{{ currentFile?.fileName }}</span>
        <div v-else></div>
        <el-button
          v-if="showEdit"
          class="h-30px bg-[#6474FD] text-[#fff]  text-15px"
          @click="goEdit"
        >
          去修改
        </el-button>
      </div>
      <div v-if="type == 3">
        <div v-if="calFileType == 'file'"
             class="w-full"
             :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'"
        >
          <g-loading v-show="showLoading" class="h-200px"></g-loading>
          <iframe
            v-show="!showLoading"
            :key="props.currentFile?.fileAbsoluteUrl"
            :src="calSrc"
            frameborder="0"
            class="w-full"
            :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'"
            @load="showLoading = false"
          ></iframe>
        </div>
        <template v-else-if="calFileType == 'video'">
          <g-video :url="calSrc"
                   class="w-full"
                   :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'"
          />
        </template>
        <div v-else-if="calFileType == 'img'"
             class="overflow-auto"
             :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'"
        >
          <img
            :src="calSrc"
            alt=""
            srcset=""
            class="object-contain"
          >
        </div>
        <div v-else>
          暂不支持该文件类型预览！
        </div>
      </div>
      <div v-if="type == 2"
           class="overflow-auto"
           :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'"
      >
        <!-- 富文本 -->
        <g-mathjax
          v-if="articleInfo.bookCatalogArticleFormatType == 1"
          :text="articleInfo.content"
        />

        <!-- markdown -->
        <div v-else-if="articleInfo.bookCatalogArticleFormatType == 2">
          <g-markdown-new :content="articleInfo.content"></g-markdown-new>
        </div>

        <!-- 思维导图 -->
        <MindMap
          v-else-if="
            articleInfo.bookCatalogArticleFormatType == 3
          "
          :is-f-i-b="false"
          :mode="2"
          :remote-map-data="articleInfo.content"
          :height="showEdit || showTitle ? 400 : 440"
          :class="showEdit || showTitle ? 'h-[400px]' : 'h-[440px]'"
        />
      </div>
      <img
        v-if="showClose"
        class="w-18px h-18px absolute top-[-20px] right-[-20px] cursor-pointer"
        src="@/assets/img/taskCenter/close.png"
        @click="showDialog = false"
      >
    </div>
  </van-popup>
</template>

<style lang="scss" scoped></style>
