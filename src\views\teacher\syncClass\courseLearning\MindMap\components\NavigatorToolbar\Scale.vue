<script setup lang="ts">
const props = defineProps({
  mindMap: {
    type: Object,
    default: () => {},
  },
})

let scaleNum: any = $ref(100)
let cacheScaleNum: any = $ref(0)

onMounted(() => {
  $g.bus.on('mind_map_view_data_change', watchMap)
})

const watchMap = $g._.throttle(() => {
  scaleNum = toPer(props.mindMap.view.scale)
}, 100)

// 转换成百分数
function toPer(scale) {
  return (scale * 100).toFixed(0)
}

// 缩小
function narrow() {
  props.mindMap.view.narrow()
  nextTick(() => {
    scaleNum = toPer(props.mindMap.view.scale)
  })
}

// 放大
function enlarge() {
  props.mindMap.view.enlarge()
  nextTick(() => {
    scaleNum = toPer(props.mindMap.view.scale)
  })
}

// 聚焦时缓存当前缩放倍数
function onScaleNumInputFocus() {
  cacheScaleNum = scaleNum
}

// 手动输入缩放倍数
function onScaleNumChange() {
  const scaleNum2 = Number(scaleNum)
  if (Number.isNaN(scaleNum) || scaleNum2 <= 0) {
    scaleNum = cacheScaleNum
  }
  else {
    const cx = props.mindMap.width / 2
    const cy = props.mindMap.height / 2
    props.mindMap.view.setScale(scaleNum / 100, cx, cy)
  }
}
</script>

<template>
  <div class="scaleContainer select-none">
    <g-icon
      name="ri-subtract-line"
      size="14"
      color=""
      @click="narrow"
    />
    <svg-ri-subtract-line
      class="text-14px text-[#8c939d] cursor-pointer"
      @click="narrow"
    ></svg-ri-subtract-line>
    <div class="scaleInfo">
      <input
        v-model="scaleNum"
        type="text"
        @change="onScaleNumChange"
        @focus="onScaleNumInputFocus"
      />%
    </div>
    <svg-ri-add-line
      class="text-14px text-[#8c939d] cursor-pointer"
      @click="enlarge"
    ></svg-ri-add-line>
  </div>
</template>

<style lang="scss" scoped>
.scaleContainer {
  display: flex;
  align-items: center;

  .btn {
    cursor: pointer;
  }

  .scaleInfo {
    margin: 0 5px;
    display: flex;
    align-items: center;
    input {
      width: 35px;
      text-align: center;
      background-color: transparent;
      border: none;
      outline: none;
    }
  }
}
</style>
