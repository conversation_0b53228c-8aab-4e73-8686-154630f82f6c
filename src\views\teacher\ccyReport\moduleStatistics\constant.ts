export const TASK_TYPE_MAP = {
  测验: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  诊断: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  评估: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  周考: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  月考: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  高考模拟卷: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  个性模拟考: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  标准模考: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  阶段性考试: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  自定义考试: {
    color: '#6474FD',
    borderColor: 'rgba(100, 116, 253, 0.14)',
    background: '#F3F4F9',
  },
  随堂检验: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  能力拓展: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  易错防范: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  课后达标: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  知识精讲: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  实战演练: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  错题本: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3E9FF',
  },
  资源: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3F5FF',
  },
  作业: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3F5FF',
  },
  错题: {
    color: '#6474FD',
    borderColor: 'rgba(100,116,253,0.14)',
    background: '#E3F5FF',
  },
}
