<script setup lang="ts">
import {
  getErrorExamClassListApi,
  getErrorExamListApi,
  getErrorExamQuestionFilterRangeApi,
  getErrorExamQuestionPaperApi,
  getErrorGradeSelectApi,
  getStageSelectApi,
  publishErrorTaskApi,
  setErrorExamQuestionFilterRangeApi,
} from '@/api/taskCenter'
import AddClassDialog from './components/AddClassDialog.vue'
import NumberCard from './components/NumberCard.vue'
import QuestionWrap from './components/QuestionWrap.vue'
import RemoveDialog from './components/RemoveDialog.vue'
import SubmitDialog from './components/SubmitDialog.vue'
import TaskRecordDialog from './components/TaskRecordDialog.vue'
import ErrorTaskSet from './ErrorTaskSet/index.vue'

const quesPanelItemClass = 'ques-panel-item'
let stepVal = $ref(1)
const route = useRoute()
// 学段
let stageList = $ref<any[]>([])
// 年级
let gradeList = $ref<any[]>([])
// 考试列表
let examList = $ref<any>([])
// 当前学段
let currentStage = $ref<any>(null)
// 当前年级
let currentGrade = $ref<any>(null)
// 当前考试
let currentExam = $ref<any>(null)
// 班级
let classList = $ref<any>([])
// 选中的班级
let currentClass = $ref<any>(null)
// 移除的班级
let removeClassList = $ref<any[]>([])
// 添加班级弹窗
let addClassDialogVisible = $ref<boolean>(false)
let showLoading = $ref<boolean>(true)
// 发布记录弹窗
let taskRecordDialogVisible = $ref<boolean>(false)
// 题目过滤范围
let filterRange = $ref<any>()
// 删除班级弹窗
let removeDialogVisible = $ref<boolean>(false)
// 提交弹窗
let submitDialogVisible = $ref<boolean>(false)
let formData = $ref<any>({})
function setStepVal(val: number) {
  stepVal = val
}
provide('setStepVal', setStepVal)
/* 获取题目过滤范围 */
async function getFilterRange() {
  try {
    let res = await getErrorExamQuestionFilterRangeApi({
      sysSubjectId: route.query.subjectId,
      schoolClassId: currentClass,
    })
    filterRange = res
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}
/* 获取学段 */
async function getStage() {
  try {
    let res = await getStageSelectApi({
      sysSubjectId: route.query.subjectId,
    })
    stageList = res.map((v) => {
      return {
        ...v,
        label: v.sysStageName,
        value: v.sysStageId,
      }
    }) || []
    if (!stageList.length) { showLoading = false; return }
    currentStage = stageList[0].value
    await getGrade()
  }
  catch (err) {
    showLoading = false
    console.log('获取学段失败', err)
  }
}

/* 获取年级 */
async function getGrade() {
  try {
    let res = await getErrorGradeSelectApi({
      sysStageId: currentStage,
      sysSubjectId: route.query.subjectId,
    })
    gradeList = res.map((v) => {
      return {
        ...v,
        label: v.sysGradeName,
        value: v.sysGradeId,
      }
    }) || []
    if (!gradeList.length) { showLoading = false; return }
    currentGrade = gradeList[0].value
    await getExam()
  }
  catch (err) {
    showLoading = false
    console.log('获取年级失败', err)
  }
}
/* 获取考试列表 */
async function getExam() {
  try {
    let res = await getErrorExamListApi({
      sysGradeId: currentGrade,
      sysSubjectId: route.query.subjectId,
    })
    examList = res.map((v) => {
      return {
        ...v,
        label: v.examName,
        value: v.examPaperId,
      }
    }) || []
    if (!examList.length) { showLoading = false; return }
    currentExam = examList[0].value
    await getClassList()
  }
  catch (err) {
    showLoading = false
    console.log('获取考试列表失败', err)
  }
}
// 获取班级
async function getClassList() {
  try {
    classList = []
    removeClassList = []
    let res = await getErrorExamClassListApi({
      sysSubjectId: route.query.subjectId,
      sysGradeId: currentGrade,
      examPaperId: currentExam,
    })
    classList = res
    if (!classList.length) { showLoading = false; return }
    currentClass = classList[0].schoolClassId
    await getFilterRange()
    await getQuestionPaper()
  }
  catch (err) {
    showLoading = false
    console.log('获取班级列表失败', err)
  }
}
// 切换学段
async function handleStageChange() {
  // 切换学段后，年级清空
  currentGrade = null
  // 切换学段后，考试清空
  currentExam = null
  await getGrade()
}
// 切换年级
async function handleGradeChange() {
  // 切换年级后，考试清空
  currentExam = null
  await getExam()
}
/* 切换考试 */
async function handleExamChange() {
  classList = []
  removeClassList = []
  currentClass = null
  await getClassList()
}
// 切换班级
async function handleClassChange(schoolClassId: string) {
  currentClass = schoolClassId
  await getFilterRange()
  await getQuestionPaper()
}
// 获取试题试卷
async function getQuestionPaper() {
  try {
    showLoading = true
    let res = await getErrorExamQuestionPaperApi({
      sysSubjectId: route.query.subjectId,
      sysGradeId: currentGrade,
      examPaperId: currentExam,
      examId,
      sysCourseId,
      schoolClassId: currentClass,
    })
    showLoading = false
    data = res.map((v, i) => {
      return {
        ...v,
        questionNumber: i + 1,
      }
    })
  }
  catch (err) {
    showLoading = false
    data = []
    console.log('获取试题失败', err)
  }
}
let removeClassItem = $ref<any>({})
// 打开删除班级弹窗
function openRemoveDialog(item: any) {
  if (classList.length == 1) return $g.showToast('至少保留一个班级')
  removeDialogVisible = true
  removeClassItem = item
}
// 移除班级
async function handleRemoveClass(y) {
  classList = classList.filter(v => v.schoolClassId != removeClassItem.schoolClassId)
  removeClassList.push(removeClassItem)
  // 如果移除的班级是当前班级，则切换到第一个班级
  if (currentClass == removeClassItem.schoolClassId) {
    currentClass = classList[0].schoolClassId
    await getFilterRange()
    await getQuestionPaper()
  }
}
// 添加班级
async function openAddClass() {
  if (removeClassList.length)
    addClassDialogVisible = true

  else
    $g.showToast('已展示全部班级')
}
// 弹窗添加班级
async function handleAddClass(item: any) {
  classList = classList.concat(item)
  // 更新removeClassList,classList中不存在的
  removeClassList = removeClassList.filter(v => !classList.map(vv => vv.schoolClassId).includes(v.schoolClassId))
  addClassDialogVisible = false
}
let sortType = $ref<any>('index')
function onSort(type) {
  sortType = type
}

const examId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.examId
})
const sysCourseId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.sysCourseId
})
const jztSysCourseId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.jztSysCourseId
})
const bookId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.bookId
})
const examName = computed(() => {
  return examList.find(v => v.value == currentExam)?.examName
})
const gradeName = computed(() => {
  return gradeList.find(v => v.value == currentGrade)?.label
})
provide('examName', examName)
onBeforeMount(() => {
  getStage()
})
let data = $ref<any[]>([])
const questionData = $computed<any>(() => {
  let arr = data.filter((v) => {
    return v.classRate >= filterRange.rateBegin && v.classRate <= filterRange.rateEnd
  })
  return sortType === 'index' ? arr : arr.slice().sort((a, b) => a.correctRate - b.correctRate)
})
let btnLoading = $ref(false)
const router = useRouter()
/* 发布 */
async function handleRelease() {
  try {
    btnLoading = true
    await publishErrorTaskApi({
      sysStageId: currentStage,
      sysGradeId: currentGrade,
      sysSubjectId: route.query.subjectId,
      examId,
      examPaperId: currentExam,
      bookId,
      sysCourseId,
      schoolClassIds: classList.map(v => v.schoolClassId),
      ...formData,
    })
    $g.showToast('发布成功')
    if ($g.isPC)
      router.back()

    else
      $g.flutter('back', true)
  }
  catch (err) {
    btnLoading = false
    console.log('发布任务失败', err)
  }
}
// 设置题目过滤范围
async function handleSetFilterRange() {
  await setErrorExamQuestionFilterRangeApi({
    sysSubjectId: route.query.subjectId,
    rateBegin: filterRange.rateBegin,
    rateEnd: filterRange.rateEnd,
    schoolClassId: currentClass,
  })
}
let scrollContainer = $ref<any>(null)
let scrollTop = $ref(0)
// 监听滚动
onMounted(() => {
  scrollContainer.addEventListener('scroll', () => {
    scrollTop = scrollContainer.scrollTop
  })
})
// 监听滚动
onBeforeUnmount(() => {
  scrollContainer.removeEventListener('scroll', () => {})
})
// 回到顶部
function scrollToTop() {
  scrollContainer.scrollTop = 0
}
// 取消布置
function back() {
  if ($g.isPC)
    router.back()

  else
    $g.flutter('back')
}
</script>

<template>
  <div ref="scrollContainer" class="pt-26px relative pb-60px h-full overflow-y-auto no-bar">
    <div v-show="stepVal == 1">
      <div class="px-26px">
        <g-navbar title="布置错题任务">
          <template #center>
            <div class="flex items-center">
              <div class="flex items-center">
                <div class="mr-11px flex-shrink-0">
                  学段
                </div>
                <el-select
                  v-model="currentStage"
                  placeholder="请选择学段"
                  class="w-[110px]"
                  @change="handleStageChange"
                >
                  <el-option
                    v-for="item in stageList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="flex items-center mx-26px">
                <div class="mr-11px flex-shrink-0">
                  年级
                </div>
                <el-select
                  v-model="currentGrade"
                  placeholder="请选择年级"
                  class="w-[110px]"
                  @change="handleGradeChange"
                >
                  <el-option
                    v-for="item in gradeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="flex items-center">
                <div class="mr-11px flex-shrink-0">
                  选择考试
                </div>
                <el-select
                  v-model="currentExam"
                  placeholder="请选择考试"
                  class="w-[240px]"
                  @change="handleExamChange"
                >
                  <el-option
                    v-for="item in examList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </template>
          <template #right>
            <div class="text-[#6474FD] text-[13px] cursor-pointer" @click="taskRecordDialogVisible = true">
              发布记录
            </div>
          </template>
        </g-navbar>
        <!-- 班级列表 -->
        <div class="flex items-center  mt-31px min-h-36px">
          <div class="mr-13px text-[13px] text-[#333]">
            该场考试您任教的班级
          </div>
          <div class="flex items-center ">
            <div
              v-for="item in classList"
              :key="item.schoolClassId"
              :class="currentClass == item.schoolClassId ? 'class-item-active' : ''"
              class="cursor-pointer py-6px px-11px text-[13px] bg-white rounded-[5px] border-[1px] border-solid border-[#FBFBFB] mr-13px relative"
              @click="handleClassChange(item.schoolClassId)"
            >
              {{ item.className }}
              <svg-menu-close
                class="absolute w-18px h-18px top-0 right-0 translate-x-1/2 -translate-y-1/2"
                @click.stop="openRemoveDialog(item)"
              />
            </div>
            <div
              class="flex items-center cursor-pointer py-6px px-11px bg-white rounded-[5px] border-[1px] border-solid border-[#6474FD] text-[#6474FD] "
              :class="removeClassList.length ? '' : '!text-[#ccc] !border-[#ccc] cursor-pointer'"
              @click="openAddClass"
            >
              <svg-ri-add-line class="w-13px h-13px mr-4px" />
              <div class="text-[13px] mt-2px">
                添加班级
              </div>
            </div>
          </div>
        </div>
        <div class="mt-26px">
          <g-loading v-if="showLoading" class="h-200px"></g-loading>

          <template v-else>
            <g-empty v-if="!questionData.length"></g-empty>
            <template v-else>
              <QuestionWrap
                v-for="(item, index) in questionData"
                :key="item.questionId"
                :question-item="item"
                :index="index + 1"
                :exam-id="examId"
                :sys-course-id="sysCourseId"
                :jzt-sys-course-id="jztSysCourseId"
                :exam-paper-id="currentExam"
                :book-id="bookId"
                :class="quesPanelItemClass"
              >
              </QuestionWrap>
            </template>
          </template>
        </div>
      </div>

      <NumberCard
        v-model:range="filterRange"
        :ques-list="questionData"
        :item-class="quesPanelItemClass"
        :school-class-id="currentClass"
        :scroll-top="scrollTop"
        @sort="onSort"
        @handle-set-filter-range="handleSetFilterRange"
        @scroll-to-top="scrollToTop"
      />

      <!-- 添加班级 -->
      <AddClassDialog v-model:visible="addClassDialogVisible"
                      :class-list="removeClassList"
                      @add-class="handleAddClass"
      />
      <!-- 发布记录 -->
      <TaskRecordDialog v-model:visible="taskRecordDialogVisible" :book-id="bookId" />
      <!-- 删除班级 -->
      <RemoveDialog v-model:visible="removeDialogVisible" @confirm="handleRemoveClass" />
      <!-- 提交弹窗 -->
      <!-- <SubmitDialog v-model:visible="submitDialogVisible" :btn-loading="btnLoading" @submit="handleRelease" /> -->
    </div>
    <!-- 任务发布 -->
    <ErrorTaskSet
      v-show="stepVal == 2"
      :class-list="classList"
      :form-data="formData"
      :grade-name="gradeName"
    />
    <div class="fixed bottom-0  h-55px w-full flex items-center justify-end bg-white">
      <el-button
        v-if="stepVal == 1"
        color="#6474FD"
        class="text-[15px] rounded-[6px] px-15px py-4px mr-26px h-30px"
        :loading="btnLoading"
        @click="setStepVal(2)"
      >
        下一步
      </el-button>
      <div v-else class="flex items-center">
        <el-button
          color="#6474FD"
          class="text-[15px] rounded-[4px] px-15px py-4px h-30px text-[#666666] bg-[#FBFBFB] border-[1px] border-solid border-[#ccc]"
          :disabled="btnLoading"
          @click="back"
        >
          取消布置
        </el-button>
        <el-button
          color="#6474FD"
          class="text-[15px] rounded-[4px] px-15px py-4px h-30px text-[#5864F8] bg-[#ECEFFF] border-[1px] border-solid border-[#646AB4]"
          :disabled="btnLoading"
          @click="setStepVal(1)"
        >
          上一步
        </el-button>
        <el-button
          color="#6474FD"
          class="text-[15px] rounded-[6px] px-15px py-4px mr-26px h-30px"
          :loading="btnLoading"
          @click="handleRelease"
        >
          完成布置
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.class-item-active{
  color: #6474FD;
  border-color: #6474FD !important;
  background-color: #ECEFFF !important;
}
</style>
