import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config

// 获取周次列表
export async function getWeekApi() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/week/list`,
  )
}
// 添加备忘录
export async function addMemoApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/memo/add`,
    data,
  )
}
// 编辑备忘录
export async function editMemoApi(data) {
  return request.put(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/memo/edit`,
    data,
  )
}
// 查看备忘录
export async function getMemoApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/memo/list`,
    data,
  )
}
// 删除备忘录
export async function deleteMemoApi(data) {
  return request.delete(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/memo/delete`,
    data,
  )
}
// 布置ai课程任务
export async function createScheduleTaskApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/aiTask`,
    data,
  )
}
// 获取课表数据
export async function getScheduleDataApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable`,
    data,
  )
}
// 删除ai课程任务
export async function deleteScheduleTaskApi(data) {
  return request.delete(
    `${VITE_JZT_API}/tutoring/admin/school/class/timetable/aiTask/delete`,
    data,
  )
}
