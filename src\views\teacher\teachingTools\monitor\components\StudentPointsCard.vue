<script setup lang="ts">
const props = defineProps({
  studentInfo: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    required: true,
  },
})

let shooting = $ref(false)
let showError = $ref(false)

onBeforeMount(() => {
  $g.bus.on(props.studentInfo.idNum, (data) => {
    if (data.status === 1) {
      props.studentInfo.screenshotUrl = data.fileUrl
      showError = false
    }
    else {
      showError = true
    }
    shooting = false
  })

  $g.bus.on('check-url', () => {
    if (props.studentInfo.screenshotUrl) {
      shooting = false
    }
    else {
      shooting = true
    }
  })
})

watch(
  () => props.loading,
  (val) => {
    shooting = val
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div
    class="bg-white w-88px h-54px rounded-[6px] flex items-center justify-center  mt-5px text-[16px]"
  >
    <div
      v-if="shooting"
      class="w-88px h-54px flex flex-col text-[#999999] items-center justify-center border border-[#C4C4C4] rounded-[5px] relative"
    >
      <div class="text-12px leading-[12px] mt-10px flex items-center">
        <img
          class="w-[14px] h-[14px] mr-4px -translate-y-1px"
          :src="$g.tool.getFileUrl('teachingTools/loading.png')"
        />
        <span>截图中…</span>
      </div>
      <div class="text-[#fff] text-14px font-500 text-center w-54px h-19px tagBg absolute right-0 top-0">
        {{
          studentInfo?.studentName?.length > 3
            ? `${studentInfo?.studentName.substring(0, 3)}...`
            : studentInfo?.studentName
        }}
      </div>
    </div>
    <div v-else>
      <div v-if="showError" class="w-88px h-54px  border border-[#C4C4C4] rounded-[5px] relative flex items-center justify-center">
        <sapn class="text-[#FF4646] text-12px mt-10px">
          截图失败
        </sapn>
        <div class="text-[#fff] text-14px font-500 text-center w-54px h-19px tagBg absolute right-0 top-0">
          {{
            studentInfo?.studentName?.length > 3
              ? `${studentInfo?.studentName.substring(0, 3)}...`
              : studentInfo?.studentName
          }}
        </div>
      </div>
      <template v-else>
        <div
          v-if="studentInfo.screenshotUrl"
          class="w-88px h-54px rounded-[5px] relative  cursor-pointer active:brightness-110"
        >
          <g-img
            preview
            :src="studentInfo.screenshotUrl"
            radius="6px"
            width="88px"
            height="54px"
            class="cursor-pointer"
          />
          <div class="text-[#fff] text-14px font-500 text-center w-54px h-19px tagBg absolute right-0 top-0">
            {{
              studentInfo?.studentName?.length > 3
                ? `${studentInfo?.studentName.substring(0, 3)}...`
                : studentInfo?.studentName
            }}
          </div>
        </div>
        <div v-else>
          <!-- 学生使用中 -->
          <div
            class="w-88px h-54px border rounded-[5px] border-[#C4C4C4] flex justify-center items-center text-16px relative  cursor-pointer"
          >
            {{
              studentInfo?.studentName?.length > 4
                ? `${studentInfo?.studentName.substring(0, 4)}...`
                : studentInfo?.studentName
            }}
            <div class="w-11px h-11px rounded-[100%] bg-[#00D3A9] absolute right-[-5px] top-[-5px]"></div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tagBg {
  background-image: url(@/assets/img/teachingTools/biaoqian.png);
  background-size: cover;
}
</style>
