export default {
  plugins: {
    'tailwindcss': {},
    'autoprefixer': {
      overrideBrowserslist: [
        'Chrome >= 52',
        'Firefox >= 52',
        'Safari >= 10.1',
        'iOS >= 10.3',
        'Android >= 5',
        'ie >= 11',
      ],
      grid: true,
    },
    'postcss-px-to-viewport-8-plugin': {
      unitToConvert: 'px',
      viewportWidth: 1024,
      unitPrecision: 6,
      propList: ['*'],

      viewportUnit: 'vw',
      fontViewportUnit: 'vw',
      selectorBlackList: ['no-'],
      minPixelValue: 1,
      mediaQuery: true,
      replace: true,
      landscape: false,
      // exclude: [/\/src\/views\/results-search\/answer-card/],
    },
    'postcss-mobile-forever': {
      appSelector: '#app',
      viewportWidth: 1024,
      landscapeWidth: 1024,
      enableMediaQuery: true,
      desktopWidth: 1024,
      propList: ['*', '!opacity'],
      rootContainingBlockSelectorList: [
        'van-tabbar',
        'van-popup',
        'xgplayer-rotate-fullscreen',
      ],
      // 选择器黑名单，名单上的不转换
      selectorBlackList: [
        '.xgplayer.xgplayer-rotate-fullscreen',
        '.xgplayer.xgplayer-is-cssfullscreen',
        '.xg-top-bar',
        '.xg-pos',
        '.van-overlay',
        '.van-popup--bottom',
        '.van-popup--top',
        '.van-dropdown-item',
        '.van-dialog__footer',
        '.van-action-bar',
        '.van-popup',
        '.van-toast',
        '.el-overlay',
        '.van-image-preview',
        '.driver-popover-arrow',
        '.van-floating-bubble',
      ],
    },
  },
}
