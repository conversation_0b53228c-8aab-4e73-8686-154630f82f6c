<script setup lang="ts">
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const props = defineProps({
  // 数据
  listData: {
    type: Array,
    default: () => [],
  },
  // 每一项数据的最小高度
  minItemSize: {
    type: Number,
    default: 50,
  },
  // 唯一值
  itemKey: {
    type: String,
    required: true,
  },
  // 能够影响到每项数据展示大小的字段
  sizeDependencies: {
    type: Array,
    default: () => [],
  },
  // 开始渲染是否自动滚动到底部
  scrollBottom: {
    type: Boolean,
    default: false,
  },
  pageOption: {
    type: Object,
    default: () => {},
  },
  showLoading: {
    type: Boolean,
    default: false,
  },
  buffer: {
    type: Number,
    default: 200,
  },
  // 是否延迟关闭外部loading动画，作用：列表渲染时有自动滚动操作的时候建议打开
  delay: {
    type: Boolean,
    default: false,
  },
  // 是否开启下拉刷新
  pulldown: {
    type: Boolean,
    default: false,
  },
  pullingText: {
    type: String,
    default: '下拉即可刷新...',
  },
  loosingText: {
    type: String,
    default: '释放即可刷新...',
  },
  // 外部控制是否到达底部
  isFinished: {
    type: [Boolean, null],
    default: null,
  },
  // 是否添加鼠标、触摸事件监听
  addEvent: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'pullup',
  'update:listData',
  'closeLoading',
  'scrollFun',
  'pulldown',
  'wellScroll',
  'touchMove',
])
const scrollerRef: any = ref()
let loading = $ref(false)
let once = $ref(false) // 判断是否第一次加载数据
let bottom = $ref(true)
let refreshing = $ref(false)
let disabledDown = $ref(false)
let scroller: any = $ref(null)
let isDown = $ref(false)

/* 是否达到最后一页 */
const pullUpLoadEnd = $computed(() => {
  if (props.scrollBottom) return ''
  const { page } = props.pageOption
  return page + 1 > maxPage && !loading
})

const maxPage = $computed(() => {
  if (props.scrollBottom) return ''
  const {
    pageSize,
    total,
  } = props.pageOption
  const num = (total + pageSize - 1) / pageSize
  return Number.parseInt(num.toString())
})

const finished = computed({
  get() {
    if (props.scrollBottom) return ''
    if (props.isFinished !== null) return props.isFinished
    return pullUpLoadEnd && Boolean(props.listData.length)
  },
  set(value) {
    return value
  },
})

watch(
  () => props.listData,
  (val) => {
    loading = false
    once = true
    if (refreshing)
      refreshing = false

    // 延迟关闭loading，确保在滚动条变化完毕关闭loading
    if (props.delay || bottom) {
      setTimeout(() => {
        emit('closeLoading')
        bottom = false
      }, 800)
    }
  },
  { deep: true },
)

function scrollToBottom() {
  if (scrollerRef.value) scrollerRef.value.scrollToBottom()
}

// 下拉加载状态
function change(data) {
  if (['loading', 'normal'].includes(data.status))
    isDown = false

  else
    isDown = true
}

/* 下拉刷新 */
function onPulldownRefresh() {
  finished.value = false
  setTimeout(() => {
    emit('pulldown')
  }, 1000)
}

// 渲染最后一个项目时发出
function scrollEnd() {
  // scrollBottom为true时不执行
  if (loading || props.scrollBottom || finished.value) return
  // 触底
  loading = true
  emit('pullup')
}

const scrollToBottomFn = useDebounceFn(() => {
  if (!bottom) {
    stopObserver()
    return
  }
  scrollToBottomNew()
}, 100)

const { stop: stopObserver } = useMutationObserver(
  scrollerRef,
  (mutations) => {
    if (mutations[0].type === 'childList' && props.scrollBottom)
      scrollToBottomFn()
  },
  {
    childList: true,
    subtree: true,
  },
)

function scrollFun() {
  if (!scroller) scroller = document.querySelector('.vue-recycle-scroller')
  // 处于下拉加载但未放开手时
  if (isDown) return
  if (scroller.scrollTop == 0)
    disabledDown = false

  else
    disabledDown = true

  emit('scrollFun')
}

// 滚动到第几位
function scrollToItemIndex(num) {
  if (!scrollerRef.value || num >= props.listData.length) return
  scrollerRef.value.scrollToItem(num)
  // 公式渲染时会导致滚动条变化，等待公式渲染完成再次执行跳转操作，由于数据不同，可能有误差
  setTimeout(() => {
    // 滚动位置大于列表数据量取消滚动
    if (num >= props.listData.length) return
    scrollerRef.value.scrollToItem(num)
  }, 600)
}

// 滚动到底部
function scrollToBottomNew() {
  if (!scrollerRef.value) return
  scrollToBottom()
  setTimeout(() => {
    if (!scroller) return
    let scrollTop = scroller.scrollTop
    let scrollHeight = scroller.scrollHeight
    let clientHeight = scroller.clientHeight
    if (Math.floor(clientHeight + scrollTop) >= Math.floor(scrollHeight) - 1) return
    scrollToBottomNew()
  }, 200)
}

function touchMove() {
  emit('touchMove')
}

function wellScroll() {
  emit('wellScroll')
}

onMounted(() => {
  setTimeout(() => {
    scroller = document.querySelector('.vue-recycle-scroller')
    // ai老师页面滚动监听
    if (scroller && props.addEvent) {
      scroller.addEventListener('wheel', wellScroll)
      scroller.addEventListener('touchmove', touchMove)
    }
  }, 1000)
})

onUnmounted(() => {
  if (scroller && props.addEvent) {
    scroller.removeEventListener('wheel', wellScroll)
    scroller.removeEventListener('touchmove', touchMove)
  }
})

defineExpose({
  scrollToBottom,
  scrollToBottomNew,
  scrollToItemIndex,
})
</script>

<template>
  <div class="h-full">
    <g-loading
      v-if="(!once || showLoading) && !listData.length"
      class="h-200px"
    ></g-loading>
    <template v-else>
      <slot v-if="!listData.length" name="empty">
        <g-empty v-if="!listData.length"></g-empty>
      </slot>
      <van-pull-refresh
        v-else
        v-model="refreshing"
        :disabled="!pulldown || disabledDown"
        class="h-full"
        :pulling-text="pullingText"
        :loosing-text="loosingText"
        @refresh="onPulldownRefresh"
        @change="change"
      >
        <DynamicScroller
          ref="scrollerRef"
          :items="listData"
          :min-item-size="minItemSize"
          class="h-full"
          :class="$g.isPC ? '' : 'no-bar'"
          :buffer="buffer"
          :key-field="itemKey"
          @scroll-end="scrollEnd"
          @scroll="scrollFun"
        >
          <template #before>
            <slot name="before"></slot>
          </template>
          <template #default="{ item, index, active }">
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :data-active="active"
              :size-dependencies="sizeDependencies"
              :data-index="index"
            >
              <div class="border border-[transparent]">
                <slot :item="item"
                      :index="index"
                      :active="active"
                ></slot>
                <template v-if="index == listData.length - 1 && !scrollBottom">
                  <div v-if="loading" class="text-center py-20px">
                    <van-loading size="18px">
                      加载中...
                    </van-loading>
                  </div>
                  <div
                    v-if="finished"
                    class="text-[#cfcccc] text-14px text-center py-20px"
                  >
                    — 没有更多了 —
                  </div>
                </template>
              </div>
            </DynamicScrollerItem>
          </template>
          <template #after>
            <slot name="after"></slot>
          </template>
        </DynamicScroller>
      </van-pull-refresh>
    </template>
  </div>
</template>

<style scoped></style>
