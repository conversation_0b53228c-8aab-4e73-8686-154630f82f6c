{"name": "-", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "env-cmd -r ./.env-cmdrc.cjs -e development vite", "build:test": "env-cmd -r ./.env-cmdrc.cjs -e test vite build", "build:report": "env-cmd -r ./.env-cmdrc.cjs -e test-report vite build", "build": "env-cmd -r ./.env-cmdrc.cjs -e production run-p  type-check \"build-only {@}\" --", "preview": "vite preview", "git:push": "yarn type-check && git add -A && git-pro commit && git pull && git push", "git:tag": "run-p type-check && git-pro tag", "git:merge-test": "git-pro merge-test", "git:update-branch": "git remote update origin --prune && git checkout develop && git fetch -p && git branch -vv | grep ': gone]' | awk '{print $1}' | xargs git branch -D", "build-only": "node --max-old-space-size=4096 node_modules/vite/bin/vite.js build", "type-check": "vue-tsc --build --force", "lint:eslint": "eslint src --fix --quiet", "lint:stylelint": "stylelint \"src/**/*.{vue,scss,css}\" --fix --quiet", "lint": "run-s lint:*", "format": "prettier --write src/", "postinstall": "patch-package", "predev": "yarn"}, "dependencies": {"@better-scroll/core": "^2.5.1", "@cjh0/fetch-event-source": "^2.0.3", "@cjh0/git-pro": "^7.0.0", "@l9m/v-md-editor": "^3.2.12", "@sentry/vite-plugin": "^2.22.6", "@sentry/vue": "^8.42.0", "@vant/touch-emulator": "^1.4.0", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "driver.js": "^1.3.5", "echarts": "^5.5.1", "element-plus": "^2.9.4", "element-tree-line": "^0.2.1", "emoji-regex": "^10.4.0", "eventemitter3": "^5.0.1", "eventsource-parser": "^3.0.0", "extendable-media-recorder": "^9.2.28", "extendable-media-recorder-wav-encoder": "^7.0.130", "fabric": "^6.6.4", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "mitt": "^3.0.1", "namedavatar": "^1.2.0", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.1.3", "simple-mind-map": "0.12.0", "timeago.js": "^4.0.2", "vant": "^4.9.8", "vue": "^3.5.12", "vue-router": "^4.4.5", "vue-virtual-scroller": "^2.0.0-beta.8", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.20", "xgplayer-flv.js": "^3.0.20", "xgplayer-hls": "^3.0.20", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@antfu/eslint-config": "^3.16.0", "@iconify-json/ri": "^1.2.3", "@rollup/plugin-inject": "^5.0.5", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.9.0", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue-macros/reactivity-transform": "^1.1.3", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "cc-vite-progress": "^1.1.3", "chalk": "^5.4.1", "crypto-js": "4.2.0", "env-cmd": "^10.1.0", "eslint": "9.5.0", "eslint-plugin-oxlint": "^0.11.0", "eslint-plugin-vue": "^9.0.0", "npm-run-all2": "^7.0.1", "oxlint": "^0.11.0", "patch-package": "^8.0.0", "postcss": "^8.4.49", "postcss-html": "1", "postcss-mobile-forever": "^4.2.4", "postcss-px-to-viewport-8-plugin": "^1.2.5", "postinstall-postinstall": "^2.1.0", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.78.0", "stylelint": "15", "stylelint-config-recommended-vue": "1", "stylelint-config-standard-scss": "11", "stylelint-order": "6", "stylelint-scss": "5", "tailwindcss": "^3.4.15", "terser": "^5.36.0", "typescript": "~5.6.3", "unplugin-auto-import": "^0.18.4", "unplugin-icons": "^0.20.1", "unplugin-vue-components": "^0.27.4", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.4.10", "vite-plugin-cdn-import-async": "^1.1.0", "vite-plugin-vue-devtools": "^7.5.4", "vue-tsc": "^2.1.10"}, "volta": {"node": "18.20.5"}}