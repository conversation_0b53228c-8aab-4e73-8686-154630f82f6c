import { setupPlugins } from '@/plugins'
import { setupRouter } from '@/router'
import { setupStore } from '@/stores'
import { createApp } from 'vue'
import App from './App.vue'

import '@/styles/index.scss'

const app = createApp(App)

/* 注册插件 */
setupPlugins(app)
/* 注册状态机 */
setupStore(app)
/* 注册路由 */
setupRouter(app)

app.mount('#app')

app.config.warnHandler = (msg, instance, trace) => {
  if (msg.includes('Extraneous non-props attributes')) {

  }
}
