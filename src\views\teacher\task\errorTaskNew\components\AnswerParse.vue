<script setup lang="ts">
const props = defineProps({
  subQuestion: {
    type: Object,
    default: () => {},
  },
})
watch(
  () => props.subQuestion,
  () => {
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div>
    <div class="flex  text-[16px] ">
      <div class="text-[#6474FD] flex-shrink-0 mr-10px">
        [详解]
      </div>
      <div class="flex-1">
        <g-mathjax
          v-for="item in subQuestion.subQuestionParseList"
          :key="item.subQuestionParseId"
          :text="item.content"
          class="text-16px"
        />
      </div>
    </div>
    <div class="flex  text-[16px] mt-10px">
      <div class="text-[#6474FD] flex-shrink-0 mr-10px">
        [答案]
      </div>
      <div class="flex-1">
        <g-mathjax :text="subQuestion.subQuestionAnswer" class="text-16px" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>
