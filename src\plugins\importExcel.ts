// @ts-nocheck
/* 导入excel文件 */
import * as XLSX from 'xlsx'

/**
 * @param file 文件流
 * @param tableTemplate 要导入的表格模板，一个数组，如：
 * tableTemplate: ['userCode', 'userName', 'department', 'major', 'position']，其中的值
 * 为表格的字段名，注意字段的顺序应与实际的导入excel一致。
 */
export default function importExcel(file, tableTemplate) {
  return new Promise((resolve, reject) => {
    const f = file
    // 通过DOM取文件数据
    const reader = new FileReader()
    FileReader.prototype.readAsBinaryString = function (f) {
      let wb // 读取完成的数据
      let outdata
      const reader = new FileReader()
      reader.onload = function (e) {
        if (!e || !e.target) {
          console.error('文件加载失败')
          return
        }
        const arrayBuffer = e.target.result as ArrayBuffer
        const bytes = new Uint8Array(arrayBuffer)
        wb = XLSX.read(bytes, {
          type: 'array',
        })
        outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], {
          defval: '',
        }) // outdata就是表格中的值;{defval: ""}单元格为空值不过滤
        let arr = []
        // 下面是数据解析提取逻辑
        if (tableTemplate.length && outdata.length) {
          arr = outdata.map((item) => {
            const obj = {}
            Object.keys(item).map((key, index) => {
              obj[tableTemplate[index]] = item[key]
            })
            return obj
          })
        }
        resolve(arr)
      }
      reader.readAsArrayBuffer(f)
    }
    reader.readAsBinaryString(f)
  })
}
