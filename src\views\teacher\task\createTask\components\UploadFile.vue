<script setup lang="ts">
import PreviewFile from './PreviewFile.vue'

let fileList = defineModel<any>('fileList')
let uploadRef = $ref<any>(null)
let showFile = $ref(false)
let currentFile = $ref<any>({})
watch(
  () => showFile,
  (val) => {
    if (!val) {
      setTimeout(() => {
        currentFile = {}
      }, 350)
    }
  },
)
/* 预览视频 */
function handlePreview(item) {
  console.log(item)
  if (item.percentage != 100)
    return $g.showToast('视频正在上传中，请稍后再试')

  item.fileExtension = $g.tool.getExt(item.name)
  item.fileAbsoluteUrl = item.fullUrl
  currentFile = item
  showFile = true
}
/* 删除视频 */
function deleteFile(item, index) {
  if (item.percentage != 100)
    uploadRef.handleRemove(item)

  fileList.value.splice(index, 1)
}
</script>

<template>
  <div class="h-full overflow-y-auto">
    <!-- 视频列表 -->
    <div
      class="flex flex-wrap items-center"
      :class="{ 'justify-center h-full': !fileList.length }"
    >
      <div
        v-for="(item, index) in fileList"
        :key="item.uid"
        class="bg-[#F3F4F9] rounded-[6px] mr-15px mb-20px"
      >
        <div class="flex items-center w-[437px] h-[59px] box-border px-17px">
          <img
            :src="$g.tool.getFileUrl('taskCenter/video.png')"
            alt=""
            class="w-21px h-21px flex-shrink-0"
          />
          <div class="ml-10px text-[15px] font-600 flex-1 truncate">
            {{ item.name }}
          </div>
          <div class="flex flex-shrink-0">
            <div
              class="h-30px cursor-pointer leading-[30px] w-[94px] text-center border-[1px] border-solid border-[#DADDE8] text-[#6C6C74] rounded-[4px]"
              @click="handlePreview(item)"
            >
              预览视频
            </div>
            <div
              class="text-[#FF4646] cursor-pointer w-66px h-30px leading-[30px] border-[1px] border-solid border-[#FF4646] text-center rounded-[4px] ml-13px"
              @click="deleteFile(item, index)"
            >
              删除
            </div>
          </div>
        </div>
        <!-- 进度条 -->
        <el-progress
          v-if="item.percentage != 100"
          :percentage="item.percentage"
          class="mx-17px"
        />
      </div>

      <g-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        mode="text"
        class="text-center"
        :max-size="1024 * 1024 * 1024 * 5"
        accept=".mp4"
        :show-file-list="!fileList.length"
      >
        <div
          v-if="!fileList.length"
          class="flex-cc rounded-[6px] text-[#6474FD] bg-[#fbfbfb] w-[111px] leading-[30px]"
          style="border: 1px solid #646ab4"
        >
          <svg-ri-add-line />
          <div>上传视频</div>
        </div>
        <div v-else>
          <svg-common-add-circle class="w-26px h-26px" />
        </div>
        <template #tip>
          <div
            v-if="!fileList.length"
            class="text-[#929296] text-[15px] mt-10px"
          >
            只能上传.mp4文件，单个视频不能超过4.88GB
          </div>
          <div v-else></div>
        </template>
      </g-upload>
    </div>
    <PreviewFile v-model:show="showFile" :current-file="currentFile" />
  </div>
</template>

<style lang="scss" scoped></style>
