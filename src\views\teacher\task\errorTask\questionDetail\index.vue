<script setup lang="ts" name="ErrorQuestionDetail">
import { getErrorExamQuestionDetailApi, getErrorExamQuestionListApi, getErrorExamReportApi } from '@/api/taskCenter'
import QuestionPanel from '@/views/teacher/task/questionReport/components/QuestionPanel.vue'

const route = useRoute()

let showMenu = $ref(false)
let showLoading = $ref(true)
let quesList = $ref<any[]>([])
let currentQuesIndex = $ref(-1)
let quesDetail = $ref<any>(null)

let currentStudent = $ref<any>(null)

let currentSubIndex = $ref(0)

let currentStudentIndex = $ref(0)

let currentOptionIndex = $ref(0)
let showQues = $ref(true)
let showExplain = $ref(false)

let studentAnswerList = $ref<any>([])

let totalNum = $ref(1)

const currentQues = $computed(() => {
  return quesList[currentQuesIndex] || null
})
const currentSubQues = $computed(() => {
  return currentQues?.subList?.[currentSubIndex] || null
})
const currentOption = $computed(() => {
  return currentSubQues?.answerList?.[currentOptionIndex] || null
})

const currentStudentAnswerData = $computed(() => {
  return studentAnswerList[currentStudentIndex] || null
})

const currentQuesNumTitle = $computed(() => {
  if (currentQues) {
    const title = currentQues.qnum
    return `第${title}题`
  }
  return '---'
})

function toggleQues() {
  if (showQues) {
    showQues = false
  }
  else {
    showQues = true
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}

function toggleExplain() {
  if (showExplain) {
    showExplain = false
  }
  else {
    showExplain = true
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}

function getTitle(item, number) {
  const isSingle = quesDetail.subQuestions.length === 1
  const title = item.subQuestionTitle || ''
  const subTitle = Object.keys(item)
    .filter(
      key => key.includes('option') && key !== 'optionNumbers' && item[key],
    )
    .map((key) => {
      const option = key.replace('option', '')
      const value = item[key]
      return `<div class="ques-text-row"><span class="text-option">${option}.</span>${value}</div>`
    })
    .join('\n')
  const numberPrefix = isSingle
    ? ''
    : `<span class="ques-num">(${number})</span>`
  return (
    `<div class='ques-row'>${
      numberPrefix
    }<div><div>${
      title
    }</div><div>${
      subTitle
    }</div></div>`
  )
}

function handleSelect(index) {
  if (currentSubIndex !== index)
    currentSubIndex = index
}

function handleSelectStudent(index) {
  if (currentStudentIndex !== index)
    currentStudentIndex = index
}

function prev() {
  if (currentQuesIndex !== 0 && quesList.length !== 0) {
    currentQuesIndex--
  }
}

function next() {
  if (currentQuesIndex !== quesList.length - 1 && quesList.length !== 0) {
    currentQuesIndex++
  }
}

async function checkStudentDetail(item) {
  currentStudent = item
  const subId = currentSubQues?.subQuestionId
  const data = await getErrorExamQuestionDetailApi({
    exerciseId: item.exerciseId,
    examId: route.query.examId,
    examPaperId: route.query.examPaperId,
    sysCourseId: route.query.sysCourseId,
    questionId: currentQues.questionId,
    schoolStudentId: item.schoolStudentId,
  })
  const curId = data.subQuestionResultList.findIndex(v => v.subQuestionId === subId)
  currentStudentIndex = curId === -1 ? 0 : curId
  studentAnswerList = data.subQuestionResultList
  showMenu = true
}

async function getStudentReport() {
  try {
    showLoading = true
    const {
      paperReport,
      studentsReport,
    } = await getErrorExamReportApi({
      examId: route.query.examId,
      examPaperId: route.query.examPaperId,
      sysSubjectId: route.query.sysSubjectId,
      sysGradeId: route.query.sysGradeId,
      sysCourseId: route.query.sysCourseId,

    })
    currentQuesIndex = paperReport.questionList.findIndex(item => item.questionId == route?.query?.questionId) || 0
    quesList = paperReport.questionList
  }
  catch (e) {
    showLoading = false
    console.error(e)
  }
}

async function getQuestionListDetail() {
  try {
    showLoading = true
    const data = await getErrorExamQuestionListApi({
      examId: route.query.examId,
      examPaperId: route.query.examPaperId,
      sysGradeId: route.query.sysGradeId,
      sysCourseId: route.query.sysCourseId,
      sysSubjectId: route.query.sysSubjectId,
      questionId: currentQues.questionId,
    })
    quesDetail = data[0]
    nextTick(() => {
      $g.tool.renderMathjax()
    })
    showLoading = false
  }
  catch (e) {
    showLoading = false
    console.error(e)
  }
}

function getText(item) {
  if (item.answer !== null && /[A-Z]/i.test(item.answer))
    return item.answer

  return getTitleText(item.status, item.isCorrect)
}

function getTitleText(status, isCorrect) {
  if (status === 1) return '未作答'
  return {
    1: '答错了',
    2: '部分对',
    3: '答对了',
    4: '我不会',
  }[isCorrect] || '未自查'
}

function onQuesSelect([item, index]) {
  currentQuesIndex = index
}

function previewImg(urls, index) {
  $g.flutter('previewImage', {
    urls,
    index,
  })
}

onBeforeMount(() => {
  getStudentReport()
})

watch(() => currentQuesIndex, () => {
  currentSubIndex = 0
  currentOptionIndex = 0
  showQues = true
  showExplain = false
  getQuestionListDetail()
})

watch(() => currentSubIndex, () => {
  currentOptionIndex = 0
})
</script>

<template>
  <div class="p-26px ">
    <div class="w-full flex items-center justify-between mb-21px">
      <g-navbar title="答题结果详情">
      </g-navbar>
      <div class="flex items-center text-17px select-none">
        <div
          class="flex items-center arrow-left-icon"
          :class="{
            'grayscale-[100%] cursor-not-allowed': currentQuesIndex === 0 || quesList.length === 0,
            'van-haptics-feedback': currentQuesIndex !== 0 && quesList.length !== 0,
          }"
          @click="prev"
        >
          <div class="text-[#6474FD] ml-10px">
            上一题
          </div>
        </div>
        <div
          class="flex items-center ml-29px arrow-right-icon"
          :class="{
            'grayscale-[100%] cursor-not-allowed': currentQuesIndex === quesList.length - 1 || quesList.length === 0,
            'van-haptics-feedback': currentQuesIndex !== quesList.length - 1 && quesList.length !== 0,
          }"
          @click="next"
        >
          <div class="text-[#6474FD] mr-10px">
            下一题
          </div>
        </div>
      </div>
    </div>
    <div class="h-[calc(100vh-100px)] overflow-y-auto no-bar">
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <template v-else>
        <g-empty v-if="!quesList.length"></g-empty>
        <template v-else>
          <div class="w-full bg-white rounded-[9px]">
            <div class="p-17px border-b border-solid border-[#F3F4F9]">
              <div
                class="w-full flex items-center justify-between"
                :class="{
                  'mb-10px': showQues,
                }"
              >
                <div class="text-17px text-[#333] font-600">
                  {{ currentQuesNumTitle }}
                </div>
                <div class="flex items-center text-15px text-[#6474FD] van-haptics-feedback" @click="toggleQues">
                  <div class="mr-6px">
                    {{ showQues ? '收起' : '展开' }}
                  </div>
                  <img
                    src="@/assets/img/question/arrow.png"
                    alt="arrow"
                    class="w-15px h-9px"
                    :class="{
                      'rotate-180': !showQues,
                    }"
                  >
                </div>
              </div>
              <template v-if="showQues">
                <div v-if="quesDetail?.sysQuestionTypeName" class="w-fit h-21px leading-[21px] rounded-[4px] bg-[#F3F4F9] px-3px text-13px text-[#6C6C74] mb-11px">
                  {{ quesDetail?.sysQuestionTypeName }}
                </div>
                <div>
                  <g-mathjax
                    :text="quesDetail?.questionTitle || ''"
                    class="text-16px font-600"
                  ></g-mathjax>
                  <div class="mt-5px">
                    <g-mathjax
                      v-for="(item, index) in quesDetail?.subQuestions"
                      :key="item.subQuestionId"
                      :text="getTitle(item, index + 1)"
                      class="text-16px"
                    ></g-mathjax>
                  </div>
                </div>
              </template>
            </div>
            <div class="p-17px">
              <div
                class="w-full flex items-center justify-between"
                :class="{
                  'mb-15px': showExplain,
                }"
              >
                <div class="text-17px text-[#333] font-600">
                  答案解析
                </div>
                <div class="flex items-center text-15px text-[#6474FD] van-haptics-feedback" @click="toggleExplain">
                  <div class="mr-6px">
                    {{ showExplain ? '收起' : '展开' }}
                  </div>
                  <img
                    src="@/assets/img/question/arrow.png"
                    alt="arrow"
                    class="w-15px h-9px"
                    :class="{
                      'rotate-180': !showExplain,
                    }"
                  >
                </div>
              </div>
              <template v-if="showExplain">
                <div class="text-17px text-[#333] font-600 mb-11px">
                  答案：
                </div>
                <div class="text-16px text-[#333] pb-18px border-b border-dashed border-[#CCCCCC] mb-17px">
                  <div
                    v-for="(item, index) in quesDetail?.subQuestions"
                    :key="index"
                    class="flex items-start"
                    :class="{
                      'mt-10px': index !== 0,
                    }"
                  >
                    <div v-if="quesDetail?.subQuestions.length > 1" class="mr-5px">
                      ({{ index + 1 }})
                    </div>
                    <g-mathjax :text="item.subQuestionAnswer" class="text-16px" />
                  </div>
                </div>
                <div class="text-17px text-[#333] font-600 mb-11px">
                  解析：
                </div>
                <div class="text-16px text-[#333] pb-18px border-b border-dashed border-[#CCCCCC] mb-17px">
                  <div
                    v-for="(item, index) in quesDetail?.subQuestions"
                    :key="index"
                    class="flex items-start"
                    :class="{
                      'mt-10px': index !== 0,
                    }"
                  >
                    <div v-if="quesDetail?.subQuestions.length > 1" class="mr-5px">
                      ({{ index + 1 }})
                    </div>
                    <g-mathjax :text="item.subQuestionParse" class="text-16px" />
                  </div>
                </div>
                <div class="text-17px text-[#333] font-600">
                  知识点：
                </div>
                <div class="flex items-center flex-wrap">
                  <div v-for="(item, index) in quesDetail?.knowledgePoints"
                       :key="index"
                       class="h-21px leading-[21px] rounded-[4px] bg-[#F3F4F9] px-11px text-13px text-[#6C6C74] mt-11px mr-11px"
                  >
                    {{ item.sysKnowledgePointName }}
                  </div>
                  <div v-if="!quesDetail?.knowledgePoints?.length" class="text-[#b9b9b9] text-14px mt-11px h-21px">
                    暂无知识点
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="w-full bg-white rounded-[9px] p-17px mt-17px">
            <div class="text-17px text-[#333] font-600">
              作答统计
            </div>
            <div v-if="currentQues?.subList?.length > 1" class="flex flex-wrap">
              <div
                v-for="(item, index) in currentQues?.subList"
                :key="index"
                class="mt-13px mr-11px w-32px h-32px rounded-[32px] border border-solid border-[#DDDDDD] text-center leading-[30px] text-14px text-[#333] van-haptics-feedback"
                :class="{
                  '!text-[#6474FD] !border-[#6474FD] font-600': currentSubIndex === index,
                }"
                @click="handleSelect(index)"
              >
                {{ index + 1 }}
              </div>
            </div>
            <template v-if="currentSubQues?.answerList?.length">
              <div class="mt-13px bg-[#F3F4F9] rounded-[9px] px-17px py-22px flex items-start">
                <div class="w-[30%]">
                  <div class="flex items-baseline text-[#333] mb-7px">
                    <div class="font-600 text-26px">
                      {{ currentSubQues?.correctRate || 0 }}
                    </div>
                    <div class="text-13px">
                      %
                    </div>
                  </div>
                  <div class="text-[#929296] text-13px">
                    正答率
                  </div>
                </div>
                <div class="w-1px h-26px mt-10px bg-[#CCCCCC]">
                </div>
                <div class="w-[33%] pl-42px">
                  <div class="flex items-end text-[#333] mb-7px">
                    <div class="font-600 text-26px">
                      {{ currentSubQues?.correctNum || 0 }}
                    </div>
                  </div>
                  <div class="text-[#929296] text-13px">
                    答对人数
                  </div>
                </div>
                <div class="w-1px h-26px mt-10px bg-[#CCCCCC]">
                </div>
                <div class="w-[33%] pl-42px">
                  <div class="flex items-end text-[#333] mb-7px">
                    <div class="font-600 text-26px">
                      {{ currentSubQues?.errorNum || 0 }}
                    </div>
                  </div>
                  <div class="text-[#929296] text-13px">
                    答错人数
                  </div>
                </div>
              </div>
              <div class="mt-13px bg-[#F3F4F9] rounded-[9px] p-13px pt-17px flex items-start">
                <div class="w-[500px] mr-26px flex-shrink-0">
                  <div
                    v-for="(item, index) in currentSubQues?.answerList"
                    :key="index"
                    class="flex items-center cursor-pointer"
                    :class="{
                      'mt-27px': index !== 0,
                    }"
                    @click="currentOptionIndex = index"
                  >
                    <img
                      v-if="item.status === 1"
                      src="@/assets/img/question/info.png"
                      alt="info"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else-if="item.isCorrect === 1"
                      src="@/assets/img/question/wrong.png"
                      alt="wrong"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else-if="item.isCorrect === 2"
                      src="@/assets/img/question/half.png"
                      alt="half"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else-if="item.isCorrect === 3"
                      src="@/assets/img/question/right.png"
                      alt="right"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else
                      src="@/assets/img/question/not.png"
                      alt="not"
                      class="w-17px h-17px"
                    >
                    <div class="w-45px text-15px text-[#333333] ml-5px mr-18px font-600 whitespace-nowrap">
                      {{ getText(item) }}
                    </div>
                    <div
                      class="bg-white h-32px mr-9px max-w-[354px] rounded-[4px] min-w-[4px] border border-[transparent] border-solid"
                      :class="{
                        '!bg-[#999999]': item.status === 1,
                        '!bg-[#FF4646]': item.isCorrect === 1,
                        '!bg-[#FAAD14]': item.isCorrect === 2,
                        '!bg-[#52C41A]': item.isCorrect === 3,
                        '!bg-[#6474FD]': item.isCorrect === 4,
                        '!border-[#6474FD]': currentOptionIndex === index,
                      }"
                      :style="{
                        width: `${item.num / quesList[currentQuesIndex].answerNum * 354}px`,
                      }"
                    >
                    </div>
                    <div class="text-15px text-[#666666] whitespace-nowrap">
                      {{ item.num }}人
                    </div>
                  </div>
                </div>
                <div class="flex-grow">
                  <div class="flex items-center">
                    <img
                      v-if="currentOption?.status === 1"
                      src="@/assets/img/question/info.png"
                      alt="info"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else-if="currentOption?.isCorrect === 1"
                      src="@/assets/img/question/wrong.png"
                      alt="wrong"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else-if="currentOption?.isCorrect === 2"
                      src="@/assets/img/question/half.png"
                      alt="half"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else-if="currentOption?.isCorrect === 3"
                      src="@/assets/img/question/right.png"
                      alt="right"
                      class="w-17px h-17px"
                    >
                    <img
                      v-else
                      src="@/assets/img/question/not.png"
                      alt="not"
                      class="w-17px h-17px"
                    >
                    <div class="text-19px text-[#333333] ml-6px font-600 whitespace-nowrap">
                      {{ getTitleText(currentOption.status, currentOption?.isCorrect) }}
                    </div>
                  </div>
                  <div class="flex items-center flex-wrap">
                    <div
                      v-for="(item, index) in currentOption?.studentList"
                      :key="index"
                      class="mr-15px mt-15px px-14px h-26px leading-[26px] border border-solid cursor-pointer border-[#CCCCCC] rounded-[4px] text-15px text-[#333333] select-none"
                      :class="{
                        'van-haptics-feedback': item.exerciseId !== undefined && item.exerciseId !== null,
                        '!border-[#6474FD]': currentStudent?.schoolStudentId === item.schoolStudentId,
                        '!bg-[#ddd] text-[#666]': item.exerciseId === undefined || item.exerciseId === null,
                      }"
                      @click="checkStudentDetail(item)"
                    >
                      {{ item.studentName }}
                    </div>
                    <div v-if="!currentOption?.studentList?.length" class="text-[#b9b9b9] text-14px mt-11px h-21px">
                      暂无答题学生
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <div v-else class="w-full h-200px flex-cc">
              <g-empty description="暂无作答数据"></g-empty>
            </div>
          </div>
        </template>
      </template>
      <QuestionPanel :ques-list="quesList"
                     :current-idx="currentQuesIndex"
                     @select="onQuesSelect"
      />
      <van-popup
        v-model:show="showMenu"
        position="right"
        :style="{ width: '550px', height: '100%' }"
        :transition-appear="true"
        class="px-21px pt-17px pb-10px"
        closeable
      >
        <div class="flex items-center">
          <!-- <div class="w-30px h-30px rounded-[30px] mr-8px">
          <img :src="currentStudent?.headPicture" alt="avatar" class="w-full h-full rounded-[30px]">
        </div> -->
          <div class="text-16px font-600">
            {{ currentStudent?.studentName }} - 作答详情
          </div>
          <div v-if="currentQues?.subList?.length > studentAnswerList.length" class="text-theme-danger pt-5px text-12px font-600 ml-10px">
            只展示作答的小题
          </div>
        </div>
        <div v-if="studentAnswerList.length > 1" class="flex flex-wrap">
          <div
            v-for="(item, index) in studentAnswerList"
            :key="index"
            class="mt-13px mr-11px w-32px h-32px rounded-[32px] border border-solid border-[#DDDDDD] text-center leading-[30px] text-14px text-[#333] van-haptics-feedback"
            :class="{
              '!text-[#6474FD] !border-[#6474FD] font-600': currentStudentIndex === index,
            }"
            @click="handleSelectStudent(index)"
          >
            {{ index + 1 }}
          </div>
        </div>
        <div class="mt-15px bg-[#F3F4F9] rounded-[9px] p-17px">
          <div class="w-full flex items-center justify-between text-[#333]">
            <div class="flex items-baseline">
              <div class="text-17px font-600">
                作答结果
              </div>
              <div class="text-13px">
                （学生自查）
              </div>
            </div>
            <div class="flex items-center">
              <img
                v-if="currentStudentAnswerData?.isCorrect === 1"
                src="@/assets/img/question/wrong.png"
                alt="wrong"
                class="w-17px h-17px"
              >
              <img
                v-else-if="currentStudentAnswerData?.isCorrect === 2"
                src="@/assets/img/question/half.png"
                alt="half"
                class="w-17px h-17px"
              >
              <img
                v-else-if="currentStudentAnswerData?.isCorrect === 3"
                src="@/assets/img/question/right.png"
                alt="right"
                class="w-17px h-17px"
              >
              <img
                v-else
                src="@/assets/img/question/not.png"
                alt="not"
                class="w-17px h-17px"
              >
              <div class="ml-6px text-19px font-600">
                {{ getTitleText(currentStudentAnswerData?.status, currentStudentAnswerData?.isCorrect) }}
              </div>
            </div>
          </div>
          <div class="text-15px text-[#6C6C74] flex items-center flex-wrap">
            <div v-if="currentStudentAnswerData?.answerType === 1 || currentStudentAnswerData?.answerType === 3" class="text-15px text-[#6C6C74] mt-13px">
              <g-mathjax :text="currentStudentAnswerData?.answer || currentStudentAnswerData?.keyboard || '未作答'" />
            </div>
            <template v-else-if="currentStudentAnswerData?.answerType === 2">
              <div
                v-for="(item, index) in currentStudentAnswerData?.whiteBoard"
                :key="index"
                class="mt-13px w-96px h-96px rounded-[4px] overflow-hidden relative"
                :class="{
                  'ml-10px': index !== 0,
                }"
                @click="previewImg(currentStudentAnswerData?.whiteBoard, index)"
              >
                <img
                  src="@/assets/img/question/full.png"
                  alt="full"
                  class="absolute right-0 top-0 w-17px h-17px z-[2] pointer-events-none"
                >
                <img :src="item"
                     alt="img cover"
                     class="w-full h-full object-cover"
                >
              </div>
              <div v-if="!currentStudentAnswerData?.whiteBoard?.length" class="text-15px text-[#ddd]">
                没有答题数据
              </div>
            </template>
            <template v-else-if="currentStudentAnswerData?.answerType === 4">
              <div
                v-for="(item, index) in currentStudentAnswerData?.image"
                :key="index"
                class="mt-13px w-96px h-96px rounded-[4px] overflow-hidden relative"
                :class="{
                  'ml-10px': index !== 0,
                }"
                @click="previewImg(currentStudentAnswerData?.image, index)"
              >
                <img
                  src="@/assets/img/question/full.png"
                  alt="full"
                  class="absolute right-0 top-0 w-17px h-17px z-[2] pointer-events-none"
                >
                <img :src="item"
                     alt="img cover"
                     class="w-full h-full object-cover"
                >
              </div>
              <div v-if="!currentStudentAnswerData?.image?.length" class="text-15px text-[#ddd]">
                没有答题数据
              </div>
            </template>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.arrow-left-icon{
  &::before{
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-left-color: #6474FD!important;
    border-bottom-color: #6474FD!important;
    transform: rotate(45deg);
  }
}
.arrow-right-icon{
  &::after{
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-right-color: #6474FD!important;
    border-bottom-color: #6474FD!important;
    transform: rotate(-45deg);
  }
}

:deep() {
  .ques-num,
  .text-option {
    margin-right: 4px;
    flex-shrink: 0;
  }
  .ques-row,
  .ques-text-row {
    display: flex;
    align-items: flex-start;
  }
}
</style>
