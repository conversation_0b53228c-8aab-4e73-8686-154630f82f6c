<script setup lang="ts" name="ErrorTask">
import {
  getBookQuestionRecommendListApi,
  getErrorExamListApi,
  getErrorGradeSelectApi,
  getStageSelectApi,
  publishErrorTaskApi,
} from '@/api/taskCenter'
import router from '@/router'
import OriginalExam from './components/OriginalExam.vue'
import ParallelExam from './components/ParallelExam.vue'

// 学段
let stageList = $ref<any[]>([])
// 年级
let gradeList = $ref<any[]>([])
// 考试列表
let examList = $ref<any>([])
// 类型列表
let typeList = $ref<any>([{
  label: '原错题',
  value: 4,
}, {
  label: '平行试卷1',
  value: 1,
}, {
  label: '平行试卷2',
  value: 2,
}, {
  label: '平行试卷3',
  value: 3,
}])
// 当前学段
let currentStage = $ref<any>(null)
// 当前年级
let currentGrade = $ref<any>(null)
// 当前考试
let currentExam = $ref<any>(null)
// 当前类型
let currentType = $ref<any>(4)
let scrollRef = $ref<any>(null)
let scrollTop = $ref<any>(0)
let status = $ref(false)
/* 切换tab */
function handleTypeChange(val: any) {
  if (val != 4 && !status) {
    $g.showToast('平行试卷还未生成完毕，请稍等...')
    return
  }
  currentType = val
}
const tips = $computed(() => {
  let tipMap = {
    4: '',
    1: '当前试卷难度为难，适合题目得分率(70%,100%]的学生',
    2: '当前试卷难度为中，适合题目得分率(30%,70%]的学生',
    3: '当前试卷难度为易，适合题目得分率[0%,30%]的学生',
  }
  return tipMap[currentType]
})
onBeforeMount(() => {
  getStage()
})
const route = useRoute()
/* 获取学段 */
async function getStage() {
  try {
    let res = await getStageSelectApi({
      sysSubjectId: route.query.subjectId,
    })
    stageList = res.map((v) => {
      return {
        ...v,
        label: v.sysStageName,
        value: v.sysStageId,
      }
    }) || []
    if (!stageList.length) return
    currentStage = stageList[0].value
    await getGrade()
  }
  catch (err) {
    console.log('获取学段失败', err)
  }
}

/* 获取推题状态 */
async function getStatus() {
  try {
    let res = await getBookQuestionRecommendListApi({
      bookId,
    })
    status = res
    if (status)
      clearInterval(timer)
  }
  catch (err) {
    console.log('获取推题状态失败', err)
  }
}
/* 获取年级 */
async function getGrade() {
  try {
    let res = await getErrorGradeSelectApi({
      sysStageId: currentStage,
      sysSubjectId: route.query.subjectId,
    })
    gradeList = res.map((v) => {
      return {
        ...v,
        label: v.sysGradeName,
        value: v.sysGradeId,
      }
    }) || []
    if (!gradeList.length) return
    currentGrade = gradeList[0].value
    await getExam()
  }
  catch (err) {
    console.log('获取年级失败', err)
  }
}
/* 获取考试列表 */
async function getExam() {
  try {
    let res = await getErrorExamListApi({
      sysGradeId: currentGrade,
      sysSubjectId: route.query.subjectId,
    })
    examList = res.map((v) => {
      return {
        ...v,
        label: v.examName,
        value: v.examPaperId,
      }
    }) || []
    if (!examList.length) return
    currentExam = examList[0].value
  }
  catch (err) {
    console.log('获取考试列表失败', err)
  }
}
// 切换学段
async function handleStageChange() {
  // 切换学段后，年级清空
  currentGrade = null
  // 切换学段后，考试清空
  currentExam = null
  await getGrade()
}
// 切换年级
async function handleGradeChange() {
  // 切换年级后，考试清空
  currentExam = null
  await getExam()
}
const examId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.examId
})
const sysCourseId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.sysCourseId
})
const bookId = $computed(() => {
  return examList.find(v => v.value == currentExam)?.bookId
})
let btnLoading = $ref(false)
/* 发布 */
async function handleRelease() {
  try {
    if (btnLoading) return
    btnLoading = true
    await publishErrorTaskApi({
      sysStageId: currentStage,
      sysGradeId: currentGrade,
      sysSubjectId: route.query.subjectId,
      examId,
      examPaperId: currentExam,
      bookId,
      sysCourseId,
      isSendOriginalQuestion: 2,
    })
    $g.showToast('发布成功')
    router.back()
  }
  catch (err) {
    btnLoading = false
    console.log('发布任务失败', err)
  }
}
/* 记录滚动 */
function handleScroll() {
  scrollTop = scrollRef?.scrollTop
}
/* bookId改变就开启定时器请求推题状态，当status为true时，停止定时器 */
let timer = $ref<any>(null)
watch(() => bookId, () => {
  if (bookId) {
    currentType = 4
    status = false
    if (timer)
      clearInterval(timer)

    timer = setInterval(() => {
      getStatus()
    }, 1000)
  }
})

onActivated(() => {
  if (scrollTop)
    scrollRef.scrollTop = scrollTop
})
</script>

<template>
  <div class="pt-26px relative pb-60px">
    <div class="px-26px">
      <g-navbar title="布置错题任务">
        <template #right>
          <div class="flex items-center ">
            <div class="mr-11px">
              学段
            </div>
            <el-select
              v-model="currentStage"
              placeholder="请选择学段"
              class="w-[110px]"
              @change="handleStageChange"
            >
              <el-option
                v-for="item in stageList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="flex items-center mx-26px">
            <div class="mr-11px">
              年级
            </div>
            <el-select
              v-model="currentGrade"
              placeholder="请选择年级"
              class="w-[110px]"
              @change="handleGradeChange"
            >
              <el-option
                v-for="item in gradeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="flex items-center">
            <div class="mr-11px">
              选择考试
            </div>
            <el-select v-model="currentExam"
                       placeholder="请选择考试"
                       class="w-[240px]"
            >
              <el-option
                v-for="item in examList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </template>
      </g-navbar>
      <div ref="scrollRef" class="mt-35px h-[calc(100vh-26px-35px-35px-60px)] overflow-y-auto no-bar">
        <div class="text-[14px] text-[#333]">
          当前考试全部错题均已加入试题篮，并为您生成3套平行试卷，点击平行试卷查看题目详情
        </div>
        <!-- tab切换 -->
        <div class="flex items-center mt-17px mb-34px">
          <div
            v-for="item in typeList"
            :key="item.value"
            v-loading="!status && item.value != 4"
            class="flex items-center mr-17px px-17px py-13px cursor-pointer text-[#333] rounded-[6px] bg-white transition-all duration-100"
            :class="{ '!bg-[#6474FD] !text-[#fff]': currentType === item.value }"
            @click="handleTypeChange(item.value)"
          >
            <svg-menu-original-exam v-if="item.value === 4" class="w-17px h-17px"></svg-menu-original-exam>
            <svg-menu-parallel-exam v-else class="w-17px h-17px "></svg-menu-parallel-exam>
            <div class="text-[13px]  ml-4px">
              {{ item.label }}
            </div>
          </div>
        </div>
        <div class="text-[14px] text-[#333] ">
          {{ tips }}
        </div>
        <div class="mt-13px">
          <OriginalExam
            v-if="currentType === 4"
            :exam-id="examId"
            :exam-paper-id="currentExam"
            :sys-grade-id="currentGrade"
            :sys-course-id="sysCourseId"
            @handle-scroll="handleScroll"
          />
          <ParallelExam v-else
                        :book-id="bookId"
                        :type="currentType"
          />
        </div>
      </div>
    </div>
    <div class="fixed bottom-0  h-55px bg-white w-full flex items-center justify-end">
      <el-button
        color="#6474FD"
        :loading="btnLoading"
        class="text-[15px] rounded-[6px] px-34px py-9px mr-26px"
        :disabled="!bookId || !status"
        @click="handleRelease"
      >
        发布
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* From Uiverse.io by david-mohseni */
.loader {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 10px;
}

.loader div {
  width: 8%;
  height: 24%;
  background: rgb(128, 128, 128);
  position: absolute;
  left: 50%;
  top: 30%;
  opacity: 0;
  border-radius: 50px;
  box-shadow: 0 0 3px rgba(0,0,0,0.2);
  animation: fade458 1s linear infinite;
}

@keyframes fade458 {
  from {
    opacity: 1;
  }

  to {
    opacity: 0.25;
  }
}

.loader .bar1 {
  transform: rotate(0deg) translate(0, -130%);
  animation-delay: 0s;
}

.loader .bar2 {
  transform: rotate(30deg) translate(0, -130%);
  animation-delay: -1.1s;
}

.loader .bar3 {
  transform: rotate(60deg) translate(0, -130%);
  animation-delay: -1s;
}

.loader .bar4 {
  transform: rotate(90deg) translate(0, -130%);
  animation-delay: -0.9s;
}

.loader .bar5 {
  transform: rotate(120deg) translate(0, -130%);
  animation-delay: -0.8s;
}

.loader .bar6 {
  transform: rotate(150deg) translate(0, -130%);
  animation-delay: -0.7s;
}

.loader .bar7 {
  transform: rotate(180deg) translate(0, -130%);
  animation-delay: -0.6s;
}

.loader .bar8 {
  transform: rotate(210deg) translate(0, -130%);
  animation-delay: -0.5s;
}

.loader .bar9 {
  transform: rotate(240deg) translate(0, -130%);
  animation-delay: -0.4s;
}

.loader .bar10 {
  transform: rotate(270deg) translate(0, -130%);
  animation-delay: -0.3s;
}

.loader .bar11 {
  transform: rotate(300deg) translate(0, -130%);
  animation-delay: -0.2s;
}

.loader .bar12 {
  transform: rotate(330deg) translate(0, -130%);
  animation-delay: -0.1s;
}
</style>
