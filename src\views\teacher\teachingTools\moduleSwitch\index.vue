<script setup lang="ts">
import { getClassInfo, getGradeInfo, getModuleList, getStageInfo } from '@/api/teachingTools'
import ConfirmDialog from './components/ConfirmDialog.vue'

const filterFormData = reactive<any>({
  options: {
    stageList: [], // 学段列表
    gradeList: [], // 年级列表
    classList: [], // 班级列表
  },
  data: {
    periodLevel: null, // 学段id
    gradeId: null, // 年级id
    classId: null, // 班级id
  },
})
let moduleList = ref<any[]>([]) // 模块列表
let showLoading = $ref<boolean>(true) // 加载状态
let showDialog = $ref<boolean>(false) // 确认弹窗
const limitTimeList = $ref<any[]>([
  { label: '不限制', value: 0 },
  { label: '15分钟', value: 15 },
  { label: '30分钟', value: 30 },
  { label: '45分钟', value: 45 },
  { label: '60分钟', value: 60 },
  { label: '75分钟', value: 75 },
  { label: '90分钟', value: 90 },
  { label: '105分钟', value: 105 },
  { label: '120分钟', value: 120 },
])
// 获取当前剩余的班级
const remainingClassList = computed(() => {
  return filterFormData.options.classList.filter((item: any) => item.id !== filterFormData.data.classId)
})
provide('remainingClassList', remainingClassList)
provide('moduleList', moduleList)
/* 获取学段信息 */
async function getStageInfoApi() {
  try {
    showLoading = true
    const res = await getStageInfo()
    filterFormData.options.stageList = res.map((item) => {
      return {
        ...item,
        name: item.periodLevelName,
        id: item.periodLevel,
      }
    })
    filterFormData.data.periodLevel = res?.[0]?.periodLevel
    filterFormData.data.periodLevel && await getGradeInfoApi()
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}
/* 获取年级信息 */
async function getGradeInfoApi() {
  try {
    showLoading = true
    const res = await getGradeInfo({
      periodLevel: filterFormData.data.periodLevel,
    })
    filterFormData.options.gradeList = res.map((item) => {
      return {
        ...item,
        name: item.sysGradeName,
        id: item.sysGradeId,
      }
    })
    filterFormData.data.gradeId = res?.[0]?.sysGradeId
    filterFormData.data.gradeId && await getClassInfoApi()
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}
/* 获取班级信息 */
async function getClassInfoApi() {
  try {
    showLoading = true
    const res = await getClassInfo({
      sysGradeId: filterFormData.data.gradeId,
    })
    filterFormData.options.classList = res.map((item) => {
      return {
        ...item,
        name: item.className,
        id: item.schoolClassId,
      }
    })
    filterFormData.data.classId = res?.[0]?.schoolClassId
    filterFormData.data.classId && await getModuleListApi()
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}
/* 获取模块列表 */
async function getModuleListApi() {
  try {
    showLoading = true
    const res = await getModuleList({
      sysGradeId: filterFormData.data.gradeId,
      schoolClassId: filterFormData.data.classId,
    })
    moduleList.value = res.map((v) => {
      return {
        ...v,
        available: v.available == 2,
      }
    }) || []
    showLoading = false
  }
  catch (err) {
    showLoading = false
    console.log(err)
  }
}
/* 学段改变 */
async function handleStageChange() {
  filterFormData.data.gradeId = null
  filterFormData.data.classId = null
  filterFormData.options.gradeList = []
  filterFormData.options.classList = []
  await getGradeInfoApi()
}
/* 年级改变 */
async function handleGradeChange() {
  filterFormData.data.classId = null
  filterFormData.options.classList = []
  await getClassInfoApi()
}
/* 模块改变 */
async function handleModuleChange(item: any) {
  item.limitSeconds = item.available ? item.limitSeconds : 0
}
/* 班级改变 */
onBeforeMount(() => {
  getStageInfoApi()
})
</script>

<template>
  <div class="p-26px flex flex-col h-full">
    <g-navbar title="模块开关">
    </g-navbar>
    <!-- 筛选 -->
    <div class="flex justify-between mt-26px">
      <div class="flex items-center">
        <el-select
          v-model="filterFormData.data.periodLevel"
          placeholder="请选择学段"
          class="custom-select w-[148px]"
          @change="handleStageChange"
        >
          <el-option
            v-for="item in filterFormData.options.stageList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select
          v-model="filterFormData.data.gradeId"
          placeholder="请选择年级"
          class="custom-select w-[148px] mx-26px"
          @change="handleGradeChange"
        >
          <el-option
            v-for="item in filterFormData.options.gradeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select
          v-model="filterFormData.data.classId"
          placeholder="请选择班级"
          class="custom-select w-[148px]"
          @change="getModuleListApi"
        >
          <el-option
            v-for="item in filterFormData.options.classList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </div>
      <div
        class="px-22px py-10px bg-[#3C9EF9] text-[#fff] rounded-[21px] text-[15px] van-haptics-feedback cursor-pointer"
        @click="showDialog = true"
      >
        保存设置
      </div>
    </div>
    <!-- 列表容器 -->
    <div class="flex-1 mt-26px h-0">
      <g-loading v-if="showLoading" class="h-200px"></g-loading>
      <template v-else>
        <g-empty v-if="moduleList.length === 0" class="h-200px"></g-empty>
        <div v-else class="h-full overflow-auto no-bar">
          <div class="module-list-grid">
            <div v-for="item in moduleList" :key="item.key" class="h-[141px] p-13px bg-[#fff] rounded-[11px] module-item">
              <div class="flex justify-between items-center">
                <div class="text-[#333] text-[15px] font-[600]">
                  {{ item.title }}
                </div>
                <el-switch
                  v-model="item.available"
                  style="--el-switch-on-color: #3C9EF9; "
                  size="small"
                  @change="handleModuleChange(item)"
                />
              </div>
              <template v-if="item.available">
                <div class="text-[#666] text-[14px] my-15px">
                  单日可使用时长
                </div>
                <el-select
                  v-model="item.limitSeconds"
                  placeholder="请选择限制时长"
                  class="select-limit-time"
                >
                  <el-option
                    v-for="val in limitTimeList"
                    :key="val.value"
                    :label="val.label"
                    :value="val.value"
                  />
                </el-select>
              </template>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 确认弹窗 -->
    <ConfirmDialog v-model:show-dialog="showDialog" :school-class-id="filterFormData.data.classId" :modules="moduleList" />
  </div>
</template>

<style lang="scss" scoped>
.custom-select {
  --el-border-radius-base: 16px;
  --el-fill-color-blank: rgba(255,255,255,1);
  --el-border-color: rgba(235, 235, 235, 1);
  --el-border-color-hover: #6365ff;
  --el-popover-border-radius: 9px; /* 弹窗圆角 */
  --el-text-color-primary: #3C9EF9; /* 选中文本颜色 */
  --el-input-text-color: #000;
  :deep() {
  .el-select__wrapper {
      height: 43px;
    }
  }
}
.module-item {
  // 鼠标悬停阴影
  &:hover {
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  }
}
.select-limit-time{
  --el-fill-color-blank: #F5F5FA;
  --el-border-color: #F5F5FA;
  --el-popover-border-radius:6px; /* 弹窗圆角 */
  --el-input-text-color: #000;
  --el-border-color-hover: #F5F5FA;
  --el-border-color-focus: #F5F5FA;
}

/* 用grid布局计算每一列需要显示的模块数量 */
.module-list-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill,216px);
  gap: 20px;
}
</style>
