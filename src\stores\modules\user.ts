import { getEncrypt, login, workbenchLogin } from '@/api/common'
import { setToken } from '@/utils/token'

interface StateType {
  token: string
  jztToken: string
  userInfo: UserInfoType
  platform: string
  version: string
  currentRole?: any
}

// 用户信息
interface UserInfoType {
  accountId: number
  schoolId: number
  headPicture: null
  accountName: string
  userName: string
  mobile: null
  nickname: null
  token: string
  userType: string
  [key: string]: any
}

export const useUserStore = defineStore('user', {
  state: (): StateType => ({
    token: '',
    jztToken: '',
    userInfo: {} as UserInfoType,
    platform: 'JZT_USER_APP',
    version: '',
    currentRole: null, // 当前角色
  }),
  getters: {},
  actions: {
    async getFlutterToken() {
      if (!$g.isFlutter)
        return

      // 获取token
      await $g.flutter('token').then((res) => {
        this.token = res
      })
      // 获取金字塔token
      await $g.flutter('jztToken').then((res) => {
        if (res) {
          this.jztToken = res
          setToken('jzt_token', res, 'cookie')
        }
      })
      // 获取用户信息
      await $g.flutter('userInfo').then((res) => {
        this.userInfo = res
      })
      // 获取当前使用平台
      await $g.flutter('platform').then((res) => {
        this.platform = res || 'JZT_USER_APP'
      })
      // 获取版本信息
      await $g.flutter('version').then((res) => {
        this.version = res
      })
    },

    // 获取工作台导航token
    async getTokenTest(loginInfo: { account: string
      password: string }) {
      try {
        await workbenchLogin({
          ...loginInfo,
          type: 1,
        }).then(async (res) => {
          this.token = res.token
          await this.getEncryptApi()
          $g.showToast('登录成功')
        })
      }
      catch (error) {
        console.log('⚡[ error ] >', error)
      }
    },

    // 登录token置换
    async getEncryptApi() {
      try {
        const encrypt = await getEncrypt()
        const res = await login({ encryptedStr: encrypt })
        this.jztToken = res.token
        setToken('jzt_token', res.token, 'cookie')
        this.userInfo = res
      }
      catch (error) {
        console.log('⚡[ error ] >', error)
      }
    },

    async resetAll() {
      const userStore = useUserStore()
      userStore.$reset()
      localStorage.clear()
      sessionStorage.clear()
    },
  },
  persist: {
    storage: localStorage,
  },
})
