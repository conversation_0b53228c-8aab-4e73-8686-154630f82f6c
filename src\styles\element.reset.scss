/* ------------------------------ element-tree-line ------------------------------ */
$--element-tree-line-color: #dcdfe6;
$--element-tree-line-style: dashed;
$--element-tree-line-width: 1.5px;

/* ------------------------------ dialog重置 ------------------------------ */
.el-dialog {
  border-radius: 0px !important;
}

/* ------------------------------ checkbox 重置样式 -------------------- */
.el-checkbox {

  // 修改复选框大小和样式
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
    border-radius: 2px;

    &::after {
      box-sizing: content-box;
      width: 4px;
      height: 8px;
      border: 2px solid #fff;
      border-left: 0;
      border-top: 0;
      left: 4px;
      top: 1px;
    }
  }

  &:not(.is-checked) {
    .el-checkbox__inner::after {
      opacity: 0;
      transform: rotate(45deg) scale(0) !important;
      transition:
        all 0.1s ease-in-out,
        opacity 0.1s !important;
    }
  }

  &.is-checked {
    .el-checkbox__inner {
      transition: all 0.2s ease-in-out !important;

      &::after {
        opacity: 1;
        transform: rotate(45deg) scale(1) !important;
        transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s !important;
      }
    }
  }

  .el-checkbox__input {
    &.is-indeterminate {
      .el-checkbox__inner {
        &:before {
          height: 4px;
          top: 5px;
          background-color: #fff;
        }
      }
    }
  }
}

/* ------------------------------ message 重置样式 ------------------------------ */
.el-message {
  min-width: 100px;
  border-width: 1px;
  border-style: solid;
  border-radius: 6px !important;
  background: #ffffff;
  padding: 11px 10px;
  box-shadow: none !important;

  .el-message__content {
    color: #333333;
    font-size: 14px;
  }

  .el-message__icon {
    margin-right: 0;
    font-size: 16px;
  }

  // 成功类型
  &.el-message--success {
    background-color: #def9ec;
    border-color: var(--el-color-success);

    .el-message__content {
      color: var(--el-color-success);
    }
  }

  // 警告类型
  &.el-message--warning {
    background-color: #fdf6ec;
    border-color: var(--el-color-warning);

    .el-message__content {
      color: var(--el-color-warning);
    }
  }

  // 错误类型
  &.el-message--error {
    background-color: #fef0f0;
    border-color: var(--el-color-danger);

    .el-message__content {
      color: var(--el-color-danger);
    }
  }

  // 信息类型
  &.el-message--info {
    background-color: #f4f4f5;
    border-color: var(--el-color-info);

    .el-message__content {
      color: var(--el-color-info);
    }
  }
}


/* ------------------------------ 表格重置样式 ------------------------------ */
.el-table {

  // 表头样式重置
  .el-table__header {
    .el-table__cell {
      background: #EFF1FE !important;
      color: #6C6C74;
      font-weight: 500;
      height: 36px !important;

      .cell {
        font-size: 13px;
        line-height: 20px;
        padding: 0;
      }
    }
  }

  // 表格主体样式
  .el-table__body {
    .el-table__row--striped {
      .el-table__cell {
        background: #F5F6Fe !important;
      }
    }

    .el-table__cell {
      height: 50px !important;

      .cell {
        font-size: 13px;
      }
    }

    // 取消悬浮高亮
    tr:hover>td.el-table__cell {
      background-color: transparent !important;
    }

    // 取消点击后的高亮
    tr.current-row>td.el-table__cell {
      background-color: transparent !important;
    }
  }
}

/* ------------------------------ 分页重置样式 ------------------------------ */
.el-pagination {
  &.is-background {
    .btn-prev,
    .btn-next,
    .number {
      background: #fff !important;
      border: 1px solid #DADADA;
      border-radius: 4px;
      color: #666 !important;
      font-size: 13px;
      line-height: 20px;
    }

    .is-active {
      border-color: #6474FD !important;
      color: #6474FD !important;
    }
  }
}