<script setup lang="ts">
import { editCatalogResModify, getVideoAnalyse } from '@/api/aiTask'
import { getKnowledgeResource } from '@/api/syncClass'
import { useAiTaskStore } from '@/stores/modules/aiTask'
import Preview from '@/views/teacher/syncClass/courseLearning/components/Preview.vue'
import Draggable from 'vuedraggable'

const props = defineProps({
  resourceList: {
    type: Array,
    default: () => [],
  },
  bookCatalogName: {
    type: String,
    default: '',
  },
})
const aiTaskStore = useAiTaskStore()
const router = useRouter()
const route = useRoute()

let localResourceList = $ref([...props.resourceList]) // 本地副本用于拖拽
let showFile = $ref(false)
let showTitle = $ref(false)
let articleInfo = $ref<any>({})
let previewType = $ref<any>(1)
let currentFile = $ref<any>()
let tempData: any = null
let tempRow: any = null

function getTitle(element) {
  const prefix = element.bookCatalogResourceType === 3 ? '课程学习 · ' : element.bookCatalogResourceType === 2 ? '知识卡片 · ' : ''
  return `${prefix}${element.title || element.fileName || ''} ${element.bookCatalogResourceType === 3 ? `(${secondsToMinutes(element)}分钟)` : ''}`
}

/* 秒钟转换为分钟，向上取整 */
function secondsToMinutes(item) {
  return Math.ceil(item?.fileDuration / 60) || 0
}

function onSortEnd() {
  const params = {
    bookCatalogId: route.query?.bookCatalogId,
    taskResourceType: localResourceList?.some((v: any) => v.bookCatalogResourceType == 2) ? 2 : 3,
    addTaskResourceFromIdList: localResourceList?.map((v: any) => v.bookCatalogAttachId),
  }
  changeSubmit(params)
}

async function preview(element) {
  if (element.bookCatalogResourceType == 3) {
    router.push({
      name: 'AiTaskVideoPreview',
      query: {
        videoResourceId: element.videoResourceId,
      },
    })
    return
  }

  tempData = {}
  tempRow = element
  if (element.bookCatalogResourceType === 1) {
    router.push({
      name: 'AfterTraining',
      query: {
        bookCatalogId: element.bookCatalogId || route.query?.bookCatalogId,
        isPreview: 1,
        // bookCatalogName: `${currentChapter?.bookCatalogName || ''} · 实战演练 · ${element.resourceName || ''}`,
        bookCatalogName: '课堂知识精讲 · 实战演练 · 知识点2 元素与集合的关系',
        bookCatalogClassResourceId: element.bookCatalogClassResourceId,
        isDefault: element.isDefault,
        schoolClassId: 26525,
      },
    })
  }
  else {
    const res = await getKnowledgeResource({
      bookCatalogArticleId: element.bookCatalogArticleId,
    })
    articleInfo = res
    if (res?.bookCatalogArticleFormatType == 3)
      articleInfo.content = JSON.parse(articleInfo.content)

    if (element.bookCatalogResourceType === 3) {
      articleInfo.bookCatalogArticleFormatType = 2
      articleInfo.content = res.analyseContent
      if (res.analyseState != 2) {
        const msg = res.analyseState == 1 ? '视频拆解中，请稍后' : '视频拆解失败'
        $g.showToast(msg)
        return
      }
    }
    previewType = 2
    showTitle = false
    showFile = true
  }
}
function edit(element) {
  if (element.bookCatalogResourceType === 1) {
    router.push({
      name: 'AfterTraining',
      query: {
        bookCatalogId: element.bookCatalogId || route.query?.bookCatalogId,
        bookCatalogName: '课堂知识精讲 · 实战演练 · 知识点2 元素与集合的关系',
        bookCatalogClassResourceId: element.bookCatalogClassResourceId,
        schoolClassId: 26525,
        isDefault: element.isDefault,
      },
    })
  }
  else {
    let content = element.bookCatalogResourceType == 3 ? '更换课程学习' : '更换知识卡片'
    router.push({
      name: 'CourseLearning',
      query: {
        title: `您正在给知识精讲${content}`,
        bookCatalogId: route.query?.bookCatalogId,
        schoolClassId: 26525,
        // bookId: item.bookId,
        type: element.bookCatalogResourceType,
        from: 'aiTask',
        sysGradeId: route.query?.sysCourseId,
        initCheckedIds: element.bookCatalogResourceType == 3
          ? localResourceList?.map((v: any) => v.videoResourceId)
          : localResourceList?.map((v: any) => v.bookCatalogArticleId),
      },
    })
  }
}

function toLeaveMsg(element) {
  router.push({
    name: 'AiTaskLeaveMsg',
    query: {
      bookCatalogId: route.query?.bookCatalogId,
      taskResourceType: 3,
      taskResourceFromId: element?.videoResourceId,
    },
  })
}

function changeSubmit(params) {
  try {
    editCatalogResModify(params)
    const bookCatalogId = route.query?.bookCatalogId
    if (!bookCatalogId) return
    if (aiTaskStore.isModified(bookCatalogId))
      return

    aiTaskStore.setModified(bookCatalogId)
  }
  catch (e) {}
}

// 监听父级变化同步本地副本
watch(() => props.resourceList, (val) => {
  localResourceList = [...val]
})

onMounted(() => {
  $g.bus.on('saveCheckedRes', (data) => {
    changeSubmit({
      bookCatalogId: route.query?.bookCatalogId,
      taskResourceType: localResourceList?.some((v: any) => v.bookCatalogResourceType == 2) ? 2 : 3,
      addTaskResourceFromIdList: data?.checkedIds,
      removeTaskResourceFromIdList: data?.cancelIds,
    })
  })
})
onBeforeMount(() => {
  $g.bus.off('saveCheckedRes')
})
</script>

<template>
  <div>
    <div
      class="rounded-[13px] bg-[#fff] p-13px pt-17px"
    >
      <div class="w-full px-9px h-26px leading-[26px] text-15px text-[#333] font-600 mb-13px truncate">
        {{ bookCatalogName }}
      </div>
      <Draggable
        v-model:list="localResourceList"
        ghost-class="opacity-25"
        handle=".handle"
        :animation="150"
        @end="onSortEnd"
      >
        <template #item="{ element }">
          <div
            class="w-full h-51px bg-white border border-[#DCDFE6] rounded-[11px] flex items-center px-13px mb-13px select-none"
          >
            <div
              class="w-19px h-19px mr-9px"
              :class="{
                'icon-book': element.bookCatalogResourceType === 2,
                'icon-play': element.bookCatalogResourceType === 3,
                'icon-roll': element.bookCatalogResourceType === 1,
              }"
            >
            </div>
            <div
              class="text-15px text-[#333] font-600 leading-[20px] mr-15px"
              :class="{
                'title-w': element.bookCatalogResourceType === 3,
                'title-w2': element.bookCatalogResourceType !== 3,
              }"
            >
              <g-mathjax :text="`${getTitle(element)}`" class="title-row-math truncate" />
            </div>

            <div
              v-if="element.bookCatalogResourceType == 3"
              class="w-99px h-30px relative flex items-center justify-center rounded-[6px] border border-solid border-[#6474FD] mr-9px select-none van-haptics-feedback hover:opacity-80"
              @click="toLeaveMsg(element)"
            >
              <svg-common-msg class="w-15px h-15px mr-4px text-[#6474FD]"></svg-common-msg>
              <div class="text-13px text-[#6474FD] font-600 pt-2px">
                留言
              </div>
              <div v-if="element.hasMsg" class="absolute top-0 right-[-1px] w-18px h-13px bg-[#6474FD] rounded-[0_6px_0_6px] flex items-center justify-center">
                <svg-ri-check-line class="text-white text-12px"></svg-ri-check-line>
              </div>
            </div>
            <div
              class="w-62px h-30px flex items-center justify-center rounded-[6px] border border-solid border-[#6474FD] mr-9px select-none van-haptics-feedback hover:opacity-80"
              @click="preview(element)"
            >
              <img
                src="@/assets/img/syncClass/eye.png"
                alt="eye"
                class="w-15px h-15px mr-4px"
              >
              <div class="text-13px text-[#6474FD] font-600 pt-2px">
                预览
              </div>
            </div>
            <div
              class="w-62px h-30px flex items-center justify-center rounded-[6px] border border-solid border-[#FF4646] mr-9px select-none van-haptics-feedback hover:opacity-80"
              @click="edit(element)"
            >
              <svg-ri-edit-fill class="w-15px h-15px mr-4px text-[#FF4646]"></svg-ri-edit-fill>
              <div class="text-13px text-[#FF4646] font-600 pt-2px">
                修改
              </div>
            </div>
            <div v-if="localResourceList?.length > 1" class="menu-icon w-15px h-15px cursor-move handle"></div>
          </div>
        </template>
      </Draggable>

      <div></div>
    </div>
    <Preview
      v-model:show="showFile"
      :current-file="currentFile"
      :show-edit="true"
      :show-close="false"
      :show-title="showTitle"
      :type="previewType"
      :article-info="articleInfo"
      @edit="() => edit(tempRow)"
    />
  </div>
</template>

<style scoped lang="scss">
.menu-icon {
  background: url("@/assets/img/syncClass/menu.png") center / contain no-repeat;
}
.icon-book {
  background: url("@/assets/img/syncClass/book.png") center / contain no-repeat;
}

.icon-play {
  background: url("@/assets/img/syncClass/play.png") center / contain no-repeat;
}

.icon-roll {
  background: url("@/assets/img/syncClass/roll.png") center / contain no-repeat;
}
.title-w {
  width: calc(100% - 19px - 9px - 99px - (62px * 2) - (9px * 3) - 15px)
}
.title-w2 {
  width: calc(100% - 19px - 9px - (62px * 2) - (9px * 2) - 15px)
}

.dragging-box-mask {
  position: relative;

  &::after {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 100%;
    height: 100%;
    content: '';
  }
}
</style>
