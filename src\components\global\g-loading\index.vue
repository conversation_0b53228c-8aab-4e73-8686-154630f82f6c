<script setup>
import feiji from './feiji.json' // 引入下载的动效json

defineProps({
  textColor: {
    type: String,
    default: '#969799',
  },
})

const lottieOptions = {
  animationData: feiji,
  // path: 'https://assets9.lottiefiles.com/packages/lf20_fyye8szy.json',
  loop: true,
  renderer: 'svg',
  autoplay: true,
  speed: 20,
}

function animCreated(anim) {
  anim.setSpeed(1.4)
}
</script>

<template>
  <div class="flex items-center justify-center py-20px relative">
    <div class="relative flex flex-col items-center !h-full">
      <g-lottie :options="lottieOptions" @anim-created="animCreated" />
      <!-- <div class="loading"></div> -->
    </div>
    <div
      class="loading-text text-14px absolute bottom-[26px] w-100px text-center"
      :style="{ color: textColor }"
    >
      加载中...
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
