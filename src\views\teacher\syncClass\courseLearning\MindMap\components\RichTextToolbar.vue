<script>
import { fontFamilyList, fontSizeList } from '../config/other'
import Color from './Color.vue'

let bus = $g.bus
export default {
  name: 'RichTextToolbar',
  components: {
    Color,
  },
  props: {
    mindMap: {
      type: Object,
    },
  },
  data() {
    return {
      fontFamilyList,
      fontSizeList,
      showRichTextToolbar: false,
      style: {
        left: 0,
        top: 0,
      },
      fontColor: '',
      fontBackgroundColor: '',
      formatInfo: {},
    }
  },
  mounted() {
    bus.on(
      'mind_map_rich_text_selection_change',
      this.onRichTextSelectionChange,
    )
    document.body.append(this.$refs.richTextToolbar)
  },
  beforeUnmount() {
    bus.off(
      'mind_map_rich_text_selection_change',
      this.onRichTextSelectionChange,
    )
  },
  methods: {
    getMindMap() {
      return toRaw(this.mindMap)
    },
    onRichTextSelectionChange(e) {
      const hasRange = e[0]
      const rect = e[1]
      const formatInfo = e[2]
      if (hasRange) {
        this.style.left = `${rect.left + rect.width / 2}px`
        this.style.top = `${rect.top - 60}px`
        this.formatInfo = { ...(formatInfo || {}) }
      }
      setTimeout(() => {
        this.showRichTextToolbar = hasRange
      }, 300)
    },
    toggleBold() {
      this.formatInfo.bold = !this.formatInfo.bold
      this.getMindMap().richText.formatText({
        bold: this.formatInfo.bold,
      })
    },
    toggleItalic() {
      this.formatInfo.italic = !this.formatInfo.italic
      this.getMindMap().richText.formatText({
        italic: this.formatInfo.italic,
      })
    },
    toggleUnderline() {
      this.formatInfo.underline = !this.formatInfo.underline
      this.getMindMap().richText.formatText({
        underline: this.formatInfo.underline,
      })
    },
    toggleStrike() {
      this.formatInfo.strike = !this.formatInfo.strike
      this.getMindMap().richText.formatText({
        strike: this.formatInfo.strike,
      })
    },
    changeFontFamily(font) {
      this.formatInfo.font = font
      this.getMindMap().richText.formatText({
        font,
      })
    },
    changeFontSize(size) {
      this.formatInfo.size = size
      this.getMindMap().richText.formatText({
        size: `${size}px`,
      })
    },
    changeFontColor(color) {
      this.formatInfo.color = color
      this.getMindMap().richText.formatText({
        color,
      })
    },
    changeFontBackgroundColor(background) {
      this.formatInfo.background = background
      this.getMindMap().richText.formatText({
        background,
      })
    },
    removeFormat() {
      this.getMindMap().richText.removeFormat()
    },
  },
}
</script>

<template>
  <div
    v-show="showRichTextToolbar"
    ref="richTextToolbar"
    class="richTextToolbar"
    :style="style"
    @click.stop.passive
  >
    <el-tooltip content="加粗" placement="top">
      <div class="btn"
           :class="{ active: formatInfo.bold }"
           @click="toggleBold"
      >
        <span class="icon iconfont iconzitijiacu"></span>
      </div>
    </el-tooltip>
    <el-tooltip content="斜体" placement="top">
      <div
        class="btn"
        :class="{ active: formatInfo.italic }"
        @click="toggleItalic"
      >
        <span class="icon iconfont iconzitixieti"></span>
      </div>
    </el-tooltip>
    <el-tooltip content="下划线" placement="top">
      <div
        class="btn"
        :class="{ active: formatInfo.underline }"
        @click="toggleUnderline"
      >
        <span class="icon iconfont iconzitixiahuaxian"></span>
      </div>
    </el-tooltip>
    <el-tooltip content="删除线" placement="top">
      <div
        class="btn"
        :class="{ active: formatInfo.strike }"
        @click="toggleStrike"
      >
        <span class="icon iconfont iconshanchuxian"></span>
      </div>
    </el-tooltip>
    <el-popover
      v-if="showRichTextToolbar"
      width="auto"
      :hide-after="0"
      placement="bottom"
      trigger="hover"
      popper-style="zIndex: 3500"
    >
      <template #reference>
        <div class="btn">
          <span class="icon iconfont iconxingzhuang-wenzi"></span>
        </div>
      </template>
      <div class="fontOptionsList">
        <div
          v-for="item in fontFamilyList"
          :key="item.value"
          class="fontOptionItem"
          :style="{ fontFamily: item.value }"
          :class="{ active: formatInfo.font === item.value }"
          @click="changeFontFamily(item.value)"
        >
          {{ item.name }}
        </div>
      </div>
    </el-popover>
    <el-popover
      v-if="showRichTextToolbar"
      width="auto"
      :hide-after="0"
      placement="bottom"
      trigger="hover"
      popper-style="zIndex: 3500"
    >
      <template #reference>
        <div class="btn">
          <span class="icon iconfont iconcase fontColor"></span>
        </div>
      </template>
      <div class="fontOptionsList">
        <div
          v-for="item in fontSizeList"
          :key="item"
          class="fontOptionItem"
          :style="{ fontSize: `${item}px` }"
          :class="{ active: formatInfo.size === `${item}px` }"
          @click="changeFontSize(item)"
        >
          {{ item }}px
        </div>
      </div>
    </el-popover>
    <el-popover
      v-if="showRichTextToolbar"
      :hide-after="0"
      placement="bottom"
      width="auto"
      trigger="hover"
      popper-style="zIndex: 3500"
    >
      <template #reference>
        <div class="btn" :style="{ color: formatInfo.color }">
          <span class="icon iconfont iconzitiyanse"></span>
        </div>
      </template>
      <Color :color="fontColor" @change="changeFontColor"></Color>
    </el-popover>
    <el-popover
      v-if="showRichTextToolbar"
      :hide-after="0"
      width="auto"
      placement="bottom"
      trigger="hover"
      popper-style="zIndex: 3500"
    >
      <template #reference>
        <div class="btn">
          <span class="icon iconfont iconbeijingyanse"></span>
        </div>
      </template>
      <Color
        :color="fontBackgroundColor"
        @change="changeFontBackgroundColor"
      ></Color>
    </el-popover>
    <el-tooltip content="清除样式" placement="top">
      <div class="btn" @click="removeFormat">
        <span class="icon iconfont iconqingchu"></span>
      </div>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
.richTextToolbar {
  position: fixed;
  z-index: 2400;
  height: 55px;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  transform: translateX(-50%);

  .btn {
    width: 55px;
    height: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &:hover {
      background-color: #eefbed;
    }
    &.active {
      color: #12bb37;
    }
    .icon {
      font-size: 20px;
      &.fontColor {
        font-size: 26px;
      }
    }
  }
}
.fontOptionsList {
  width: 150px;

  .fontOptionItem {
    min-height: 30px;
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      background-color: #f7f7f7;
    }
    &.active {
      color: #12bb37;
    }
  }
}
</style>
