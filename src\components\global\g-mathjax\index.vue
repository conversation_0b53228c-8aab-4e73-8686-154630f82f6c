<script setup>
defineProps({
  text: {
    type: String,
    default: '',
  },
  // 序号,字符串形式
  order: {
    type: String,
    default: '',
  },
  // 字体宽度，也就是字体大小，传此参数只是为了计算序号显示宽度,建议设置得和公式字体大小一致
  // (单个数字按照此属性的一半进行计算)
  fontWidth: {
    type: Number,
    default: 14,
  },
})
</script>

<template>
  <div
    class="mathjax g-mathjax"
    :style="{
      '--order': `'${String(order)}'`,
      '--orderWidth': `calc(${String(order).length}*${fontWidth / 2}px)`,
    }"
    v-html="text"
  />
</template>

<style lang="scss" scoped>
.mathjax {
  width: 100%;
  word-break: break-word;
  word-wrap: break-word;
  overflow-wrap: anywhere;
  font-size: 14px;
  padding-left: var(--orderWidth);

  :deep() {
    img {
      max-width: 680rpx;
      display: inline;
    }
    span[wave] {
      text-decoration-style: wavy;
      text-decoration-line: underline;
      text-underline-position: auto;
      white-space: pre-wrap;
    }
    & > :first-child{
      position: relative;
    }
    // 把序号加入到题目中显示
    & > :first-child::before {
      content: var(--order);
      white-space: nowrap;
      display: inline-block;
      width: var(--orderWidth);
      font-family: var(--van-base-font);
      margin-left: calc(var(--orderWidth) * -1);
    }

    // 以下是某些题目遇到的特殊情况
    // 匹配第一个子元素带缩进样式的标签,统一缩进值
    & > :first-child[style*="text-indent"]:not([style*="text-indent: 0"]){
      text-indent: 28px !important;
      &::before{
        text-indent:-28px;
      }
    }
    // 匹配第一个子元素带居中样式的标签
    & > :first-child[style*="text-align: center"]::before{
      text-indent:0 !important;
      position: absolute;
      left: 0;
    }

  }
}
</style>
